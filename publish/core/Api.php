<?php
namespace Core;

class Api {
    private $timeout;

    /**
     * 构造函数
     */
    public function __construct() {
        $config = require __DIR__ . '/../config/config.php';
        $this->timeout = $config['api_timeout'] ?? 30;
    }

    /**
     * 发送GET请求
     * @param string $url 请求URL
     * @param array $params 请求参数
     * @return array 响应结果
     */
    public function get($url, $params = []) {
        if (!empty($params)) {
            $url .= (strpos($url, '?') === false ? '?' : '&') . http_build_query($params);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'message' => "cURL Error: {$error}",
                'http_code' => $httpCode,
                'data' => null
            ];
        }

        $data = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => "JSON解析错误: " . json_last_error_msg(),
                'http_code' => $httpCode,
                'data' => $response
            ];
        }

        return [
            'success' => $httpCode >= 200 && $httpCode < 300,
            'message' => $data['message'] ?? ($httpCode >= 200 && $httpCode < 300 ? '请求成功' : '请求失败'),
            'http_code' => $httpCode,
            'data' => $data
        ];
    }

    /**
     * 发送POST请求
     * @param string $url 请求URL
     * @param array $data 请求数据
     * @param array $headers 请求头
     * @return array 响应结果
     */
    public function post($url, $data = [], $headers = []) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);

        // 使用application/x-www-form-urlencoded格式
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));

        // 设置默认请求头
        $defaultHeaders = [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ];

        // 合并自定义请求头
        if (!empty($headers)) {
            $headers = array_merge($defaultHeaders, $headers);
        } else {
            $headers = $defaultHeaders;
        }

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $info = curl_getinfo($ch);

        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'message' => "cURL Error: {$error}",
                'http_code' => $httpCode,
                'data' => null
            ];
        }

        $responseData = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => "JSON解析错误: " . json_last_error_msg() . ", 原始响应: " . substr($response, 0, 1000),
                'http_code' => $httpCode,
                'data' => $response
            ];
        }

        // 检查API响应中的错误信息
        if (isset($responseData['code'])) {
            if ($responseData['code'] == 200) {
                return [
                    'success' => true,
                    'message' => $responseData['message'] ?? '请求成功',
                    'http_code' => $httpCode,
                    'data' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['message'] ?? '请求失败',
                    'http_code' => $httpCode,
                    'data' => $responseData
                ];
            }
        }

        return [
            'success' => $httpCode >= 200 && $httpCode < 300,
            'message' => $responseData['message'] ?? ($httpCode >= 200 && $httpCode < 300 ? '请求成功' : '请求失败'),
            'http_code' => $httpCode,
            'data' => $responseData
        ];
    }

    /**
     * 获取站点分类列表
     * @param string $siteUrl 站点URL
     * @param string $apiKey API密钥
     * @return array 分类列表
     */
    public function getCategories($siteUrl, $apiKey) {
        // 确保URL格式正确，移除末尾的斜杠
        $baseUrl = rtrim($siteUrl, '/');
        $url = $baseUrl . '/api/publisher/getCategories';
        // 准备请求数据
        $postData = [
            'api_key' => $apiKey
        ];

        // 发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        // 记录响应信息
        // 处理响应
        if ($error) {
            return [
                'success' => false,
                'message' => "cURL Error: {$error}"
            ];
        }

        $responseData = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => "JSON解析错误: " . json_last_error_msg()
            ];
        }

        return [
            'success' => isset($responseData['code']) && $responseData['code'] == 200,
            'message' => $responseData['message'] ?? '未知响应',
            'data' => $responseData
        ];
    }

    /**
     * 生成站点地图
     * @param string $siteUrl 站点URL
     * @param string $apiKey API密钥
     * @return array 响应结果
     */
    public function generateSitemap($siteUrl, $apiKey) {
        $url = rtrim($siteUrl, '/') . '/api/publisher/generateSitemap';
        return $this->post($url, ['api_key' => $apiKey]);
    }

    /**
     * 测试连接
     * @param string $siteUrl 站点URL
     * @param string $apiKey API密钥
     * @return array 测试结果
     */
    public function testConnection($siteUrl, $apiKey) {
        // 确保URL格式正确，移除末尾的斜杠
        $baseUrl = rtrim($siteUrl, '/');
        $url = $baseUrl . '/api/publisher/test';

        return $this->get($url, ['api_key' => $apiKey]);
    }
    /**
     * 清除站点缓存
     * @param string $siteUrl 站点URL
     * @param string $apiKey API密钥
     * @return array 响应结果
     */
    public function cleanCache($siteUrl, $apiKey) {
        $url = rtrim($siteUrl, '/') . '/api/system/clean';
        return $this->post($url, ['api_key' => $apiKey]);
    }


    /**
     * 从混合盘获取最新资源（使用 cURL 实现更好的反反爬）
     * @param int $page 页码
     * @param int $size 每页数量
     * @return array 获取结果
     */
    public function fetchFromHunhepan($page = 1, $size = 20) {
        $url = "https://api.hunhepan.com/v1/raw_disk/latest_with_extab?page={$page}&size={$size}";
        
        // 使用cURL发送请求，模拟真实浏览器
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
        curl_setopt($ch, CURLOPT_ENCODING, ''); // 自动处理压缩编码
        
        // 设置更真实的请求头
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept: application/json, text/plain, */*',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Cache-Control: no-cache',
            'Pragma: no-cache',
            'Sec-Ch-Ua: "Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile: ?0',
            'Sec-Ch-Ua-Platform: "macOS"',
            'Sec-Fetch-Dest: empty',
            'Sec-Fetch-Mode: cors',
            'Sec-Fetch-Site: cross-site',
            'Referer: https://www.hunhepan.com/',
            'Origin: https://www.hunhepan.com'
        ]);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $info = curl_getinfo($ch);
        
        curl_close($ch);
        
        if ($error) {
            error_log("[混合盘API] cURL错误: {$error}");
            return [
                'success' => false,
                'message' => "网络请求失败: {$error}",
                'resources' => []
            ];
        }

        // 检查HTTP响应状态
        if ($httpCode !== 200) {
            error_log("[混合盘API] HTTP错误，状态码: {$httpCode}");
            return [
                'success' => false,
                'message' => "API请求失败，状态码: {$httpCode}",
                'resources' => []
            ];
        }

        // 检查是否被拦截
        if (strpos($response, 'Intercept illegal requests') !== false || 
            strpos($response, '非法请求') !== false ||
            strpos($response, 'Access Denied') !== false) {
            return [
                'success' => false,
                'message' => "API访问被拦截，混合盘可能加强了反爬虫措施。建议联系管理员检查API访问策略。",
                'resources' => []
            ];
        }

        // 解析JSON响应
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $jsonError = json_last_error_msg();
            error_log("[混合盘API] JSON解析失败: {$jsonError}, 响应内容: " . substr($response, 0, 500));
            return [
                'success' => false,
                'message' => "API响应解析失败: {$jsonError}",
                'resources' => []
            ];
        }

        // 检查API业务逻辑响应状态
        if (!isset($data['code']) || $data['code'] !== 200) {
            $apiError = $data['msg'] ?? '未知API错误';
            error_log("[混合盘API] API业务错误: {$apiError}");
            return [
                'success' => false,
                'message' => "API返回错误: {$apiError}",
                'resources' => []
            ];
        }

        // 提取资源列表
        $resources = [];
        if (isset($data['data']['list']) && is_array($data['data']['list'])) {
            foreach ($data['data']['list'] as $item) {
                $resources[] = [
                    'title' => $item['disk_name'] ?? '',
                    'url' => $item['link'] ?? '',
                    'description' => isset($item['files']) && is_array($item['files']) ? implode("\n", $item['files']) : '',
                    'type' => $item['disk_type'] ?? '',
                    'code' => $item['disk_pass'] ?? ''
                ];
            }
        }

        $totalCount = count($resources);

        return [
            'success' => true,
            'message' => '获取成功',
            'count' => $totalCount,
            'total' => $data['data']['total'] ?? 0,
            'resources' => $resources
        ];
    }
}
