<?php
namespace Core;

/**
 * 采集管理类
 * 用于从各种来源采集资源
 */
class Crawler {
    private $db;
    private $resource;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->db = Database::getInstance();
        $this->resource = new Resource();
    }
    
    /**
     * 获取采集任务列表
     * @param array $filters 过滤条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array 任务列表和分页信息
     */
    public function getTasks($filters = [], $page = 1, $pageSize = 20) {
        $auth = Auth::getInstance();

        // 构建查询条件
        $where = "1=1";
        $params = [];

        // 添加用户数据隔离
        if (!$auth->isAdmin()) {
            $where .= " AND user_id = ?";
            $params[] = $auth->getUser()['id'];
        }

        if (isset($filters['status']) && $filters['status'] !== '') {
            $where .= " AND status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['search'])) {
            $where .= " AND (name LIKE ? OR source_url LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }

        if (isset($filters['type']) && $filters['type'] !== '') {
            $where .= " AND type = ?";
            $params[] = $filters['type'];
        }
        
        // 获取总记录数
        $totalSql = "SELECT COUNT(*) as total FROM tasks WHERE {$where}";
        $total = $this->db->fetch($totalSql, $params)['total'] ?? 0;
        
        // 计算分页
        $offset = ($page - 1) * $pageSize;
        $totalPages = ceil($total / $pageSize);
        
        // 获取数据
        $sql = "SELECT * FROM tasks WHERE {$where} ORDER BY id DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $pageSize;
        
        $tasks = $this->db->fetchAll($sql, $params);
        
        return [
            'tasks' => $tasks,
            'pagination' => [
                'total' => $total,
                'totalPages' => $totalPages,
                'page' => $page,
                'pageSize' => $pageSize
            ]
        ];
    }
    
    /**
     * 获取单个任务
     * @param int $id 任务ID
     * @return array|false 任务信息
     */
    public function getTaskById($id) {
        $auth = Auth::getInstance();

        if (!$auth->isAdmin()) {
            $sql = "SELECT * FROM tasks WHERE id = ? AND user_id = ? LIMIT 1";
            return $this->db->fetch($sql, [$id, $auth->getUser()['id']]);
        } else {
            $sql = "SELECT * FROM tasks WHERE id = ? LIMIT 1";
            return $this->db->fetch($sql, [$id]);
        }
    }
    
    /**
     * 添加采集任务
     * @param array $data 任务数据
     * @return int|bool 添加成功返回任务ID，失败返回false
     */
    public function addTask($data) {
        $auth = Auth::getInstance();

        // 验证数据
        if (empty($data['name']) || empty($data['source_url']) || empty($data['type'])) {
            return false;
        }

        // 添加任务
        $data['user_id'] = $auth->getUser()['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['status'] = $data['status'] ?? 0; // 默认未执行

        return $this->db->insert('tasks', $data);
    }
    
    /**
     * 更新采集任务
     * @param int $id 任务ID
     * @param array $data 任务数据
     * @return bool 是否更新成功
     */
    public function updateTask($id, $data) {
        $auth = Auth::getInstance();

        // 验证数据
        if (empty($data['name']) || empty($data['source_url']) || empty($data['type'])) {
            return false;
        }

        // 权限验证：非管理员只能更新自己的任务
        if (!$auth->isAdmin()) {
            $task = $this->getTaskById($id);
            if (!$task || $task['user_id'] != $auth->getUser()['id']) {
                return false;
            }
        }

        // 更新任务
        if (!$auth->isAdmin()) {
            $result = $this->db->update('tasks', $data, 'id = ? AND user_id = ?', [$id, $auth->getUser()['id']]);
        } else {
            $result = $this->db->update('tasks', $data, 'id = ?', [$id]);
        }

        return $result > 0;
    }
    
    /**
     * 删除采集任务
     * @param int $id 任务ID
     * @return bool 是否删除成功
     */
    public function deleteTask($id) {
        $auth = Auth::getInstance();

        // 权限验证：非管理员只能删除自己的任务
        if (!$auth->isAdmin()) {
            $task = $this->getTaskById($id);
            if (!$task || $task['user_id'] != $auth->getUser()['id']) {
                return false;
            }
            return $this->db->delete('tasks', 'id = ? AND user_id = ?', [$id, $auth->getUser()['id']]) > 0;
        } else {
            return $this->db->delete('tasks', 'id = ?', [$id]) > 0;
        }
    }
    
    /**
     * 执行采集任务
     * @param int $taskId 任务ID
     * @return array 执行结果
     */
    public function executeTask($taskId) {
        $task = $this->getTaskById($taskId);
        
        if (!$task) {
            return [
                'success' => false,
                'message' => '任务不存在'
            ];
        }
        
        // 更新任务状态为执行中
        $this->db->update('tasks', ['status' => 1, 'last_run_time' => date('Y-m-d H:i:s')], 'id = ?', [$taskId]);
        
        try {
            $result = $this->crawl($task);
            
            // 更新任务状态和结果
            $this->db->update('tasks', [
                'status' => 2, // 已完成
                'last_result' => json_encode($result, JSON_UNESCAPED_UNICODE),
                'last_success_time' => date('Y-m-d H:i:s')
            ], 'id = ?', [$taskId]);
            
            return [
                'success' => true,
                'message' => '采集完成',
                'result' => $result
            ];
        } catch (\Exception $e) {
            // 更新任务状态为失败
            $this->db->update('tasks', [
                'status' => 3, // 失败
                'last_result' => $e->getMessage()
            ], 'id = ?', [$taskId]);
            
            return [
                'success' => false,
                'message' => '采集失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 根据任务类型执行不同的采集逻辑
     * @param array $task 任务信息
     * @return array 采集结果
     */
    private function crawl($task) {
        $type = $task['type'];
        $sourceUrl = $task['source_url'];
        $config = json_decode($task['config'], true) ?? [];
        
        switch ($type) {
            case 'rss':
                return $this->crawlRss($sourceUrl, $config);
            case 'api':
                return $this->crawlApi($sourceUrl, $config);
            case 'html':
                return $this->crawlHtml($sourceUrl, $config);
            default:
                throw new \Exception('不支持的采集类型');
        }
    }
    
    /**
     * 采集RSS源
     * @param string $url RSS地址
     * @param array $config 配置信息
     * @return array 采集结果
     */
    private function crawlRss($url, $config) {
        // 这里是RSS采集的具体实现
        // 预留功能，后续实现
        
        return [
            'success' => true,
            'message' => 'RSS采集功能预留，待实现',
            'items' => []
        ];
    }
    
    /**
     * 采集API接口
     * @param string $url API地址
     * @param array $config 配置信息
     * @return array 采集结果
     */
    private function crawlApi($url, $config) {
        // 这里是API采集的具体实现
        // 预留功能，后续实现
        
        return [
            'success' => true,
            'message' => 'API采集功能预留，待实现',
            'items' => []
        ];
    }
    
    /**
     * 采集HTML页面
     * @param string $url 网页地址
     * @param array $config 配置信息
     * @return array 采集结果
     */
    private function crawlHtml($url, $config) {
        // 这里是HTML采集的具体实现
        // 预留功能，后续实现
        
        return [
            'success' => true,
            'message' => 'HTML采集功能预留，待实现',
            'items' => []
        ];
    }
    
    /**
     * 将采集结果保存为资源
     * @param array $items 采集项目
     * @param array $config 配置信息
     * @return array 保存结果
     */
    private function saveResources($items, $config) {
        $success = 0;
        $failed = 0;
        $resources = [];
        
        foreach ($items as $item) {
            $resource = [
                'title' => $item['title'] ?? '',
                'url' => $item['url'] ?? '',
                'description' => $item['description'] ?? '',
                'category' => $config['category'] ?? '',
                'content' => $item['content'] ?? '',
                'tags' => $item['tags'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 1
            ];
            
            $result = $this->resource->add($resource);
            
            if ($result) {
                $success++;
                $resources[] = $result;
            } else {
                $failed++;
            }
        }
        
        return [
            'success' => $success,
            'failed' => $failed,
            'resources' => $resources
        ];
    }
    
    /**
     * 获取支持的采集类型
     * @return array 类型列表
     */
    public function getSupportedTypes() {
        return [
            'rss' => 'RSS源',
            'api' => 'API接口',
            'html' => 'HTML页面',
            'search' => '全网搜索'
        ];
    }
    
    /**
     * 搜索资源
     * @param string $keywords 搜索关键词
     * @param array $options 搜索选项
     * @return array 搜索结果
     */
    public function search($keywords, $options = []) {
        // 检查API调用限制并增加计数
        $auth = Auth::getInstance();
        if (!$auth->incrementApiCalls()) {
            return [
                'success' => false,
                'message' => '您的API调用次数已达到本月限制，请升级套餐或等待下月重置',
                'items' => []
            ];
        }

        if (empty($keywords)) {
            return [
                'success' => false,
                'message' => '请输入搜索关键词',
                'items' => []
            ];
        }
        
        $sourceType = $options['source_type'] ?? null;
        $limit = $options['limit'] ?? 20;
        $sourceId = $options['source_id'] ?? null;
        
        // 获取采集来源
        $crawlerSource = new CrawlerSource();
        $sources = [];
        
        if ($sourceId) {
            // 如果指定了来源ID，只使用该来源
            $source = $crawlerSource->getSourceById($sourceId);
            if ($source && $source['status'] == 1) {
                $sources[] = $source;
            }
        } else {
            // 否则获取所有启用的来源
            $sources = $crawlerSource->getActiveSources($sourceType);
        }
        
        if (empty($sources)) {
            return [
                'success' => false,
                'message' => '没有可用的采集来源，请先添加采集来源',
                'items' => []
            ];
        }
        
        $allResults = [];
        $totalFound = 0;
        
        // 遍历所有来源进行搜索
        foreach ($sources as $source) {
            try {
                $results = $this->searchFromSource($source, $keywords);

                if (!empty($results)) {
                    foreach ($results as &$item) {
                        $item['source_name'] = $source['name'];
                        $item['source_id'] = $source['id'];
                    }

                    $allResults = array_merge($allResults, $results);
                    $totalFound += count($results);

                    // 如果已经达到限制，停止搜索
                    if ($totalFound >= $limit) {
                        break;
                    }
                }
            } catch (\Exception $e) {
                // 记录错误但继续搜索其他来源
                error_log("搜索来源 {$source['name']} 失败: " . $e->getMessage());
            }
        }
        
        // 限制结果数量
        $allResults = array_slice($allResults, 0, $limit);        
        return [
            'success' => true,
            'message' => "找到 " . count($allResults) . " 个结果",
            'items' => $allResults
        ];
    }
    
    /**
     * 从指定来源搜索资源
     * @param array $source 来源配置
     * @param string $keywords 搜索关键词
     * @return array 搜索结果
     */
    private function searchFromSource($source, $keywords) {
        $type = $source['type'];
        $url = $source['url'];
        
        switch ($type) {
            case 'api':
                return $this->searchFromApi($source, $keywords);
            case 'html':
                return $this->searchFromHtml($source, $keywords);
            case 'tg':
                return $this->searchFromTg($source, $keywords);
            default:
                return [];
        }
    }
    
    /**
     * 从API接口搜索
     * @param array $source API接口配置
     * @param string $keywords 搜索关键词
     * @return array 搜索结果
     */
    private function searchFromApi($source, $keywords) {
        
        $url = $source['url'];
        $method = $source['method'] ?? 'GET';
        $headers = !empty($source['headers']) ? json_decode($source['headers'], true) : [];
        $params = !empty($source['params']) ? json_decode($source['params'], true) : [];
        $fieldMap = !empty($source['field_map']) ? json_decode($source['field_map'], true) : [
            'title' => 'title',
            'url' => 'url',
            'description' => 'description'
        ];
        
        // 添加搜索关键词参数
        if (!empty($source['keyword_param'])) {
            $params[$source['keyword_param']] = $keywords;
        } else {
            $params['keywords'] = $keywords;
            $params['q'] = $keywords;
            $params['query'] = $keywords;
            $params['search'] = $keywords;
        }
        
        // 准备请求
        $ch = curl_init();
        
        $fullUrl = $url;
        if ($method === 'GET') {
            $queryString = http_build_query($params);
            $fullUrl .= (strpos($url, '?') === false ? '?' : '&') . $queryString;
        }
        
        curl_setopt($ch, CURLOPT_URL, $fullUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 增加超时时间
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 禁用SSL验证
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 禁用主机验证
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // 设置请求头
        if (!empty($headers)) {
            $headerArray = [];
            foreach ($headers as $key => $value) {
                $headerArray[] = "$key: $value";
            }
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArray);
        }
        
        // 如果是POST请求，设置POST参数
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        $curlErrno = curl_errno($ch);
        curl_close($ch);
        
        if ($response === false) {
            throw new \Exception("API请求失败: {$curlError}");
        }

        if ($httpCode !== 200) {
            throw new \Exception("API请求失败，状态码: {$httpCode}");
        }
        
        // 记录响应内容
        $shortResponse = substr($response, 0, 1000) . (strlen($response) > 1000 ? '...' : '');
        // 解析响应
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $jsonError = json_last_error_msg();
            throw new \Exception("API响应解析失败: {$jsonError}");
        }
        
        // 记录解析后的数据结构
        $dataKeys = is_array($data) ? implode(', ', array_keys($data)) : 'not an array';
        // 提取结果
        $results = $this->extractApiResults($data, $fieldMap);
        
        return $results;
    }
    
    /**
     * 从API响应中提取结果
     * @param array $data API响应数据
     * @param array $fieldMap 字段映射
     * @return array 提取的结果
     */
    private function extractApiResults($data, $fieldMap) {
        $results = [];
        
        // 获取结果列表路径
        $listPath = $fieldMap['list'] ?? '';
        $items = $data;
        
        // 如果指定了列表路径，按路径获取列表
        if (!empty($listPath)) {
            $pathParts = explode('.', $listPath);
            foreach ($pathParts as $part) {
                if (isset($items[$part])) {
                    $items = $items[$part];
                } else {
                    // 如果在根级别找不到，尝试在data字段中查找（适用于Panseeker等API）
                    if (isset($data['data']) && isset($data['data'][$part])) {
                        $items = $data['data'][$part];
                        // 继续处理剩余的路径部分
                        for ($i = 1; $i < count($pathParts); $i++) {
                            if (isset($items[$pathParts[$i]])) {
                                $items = $items[$pathParts[$i]];
                            } else {
                                $items = [];
                                break;
                            }
                        }
                        break;
                    } else {
                        $items = [];
                        break;
                    }
                }
            }
        }
        
        // 检查是否是分组数据结构（如Panseeker的merged_by_type）
        if (is_array($items) && !isset($items[0])) {
            // 检查是否所有值都是数组（分组结构）
            $isGroupedData = true;
            foreach ($items as $key => $value) {
                if (!is_array($value)) {
                    $isGroupedData = false;
                    break;
                }
            }

            // 如果是分组数据，合并所有组的数据
            if ($isGroupedData) {
                $mergedItems = [];
                foreach ($items as $groupName => $groupItems) {
                    if (is_array($groupItems)) {
                        foreach ($groupItems as $item) {
                            // 为每个项目添加来源类型信息
                            if (is_array($item)) {
                                $item['source_type'] = $groupName;
                            }
                            $mergedItems[] = $item;
                        }
                    }
                }
                $items = $mergedItems;
            } else {
                // 如果不是分组数据，按原逻辑处理
                $items = [$items];
            }
        } elseif (!is_array($items)) {
            $items = [];
        }
        
        // 遍历结果列表
        foreach ($items as $item) {
            $result = [
                'title' => $this->extractFieldValue($item, $fieldMap['title'] ?? 'title'),
                'url' => $this->extractFieldValue($item, $fieldMap['url'] ?? 'url'),
                'description' => $this->extractFieldValue($item, $fieldMap['description'] ?? 'description'),
                'type' => $this->extractFieldValue($item, $fieldMap['type'] ?? 'type'),
                'code' => $this->extractFieldValue($item, $fieldMap['code'] ?? 'code')
            ];

            // 如果有来源类型信息，添加到描述中
            if (isset($item['source_type'])) {
                $sourceTypeMap = [
                    'quark' => '夸克网盘',
                    'baidu' => '百度网盘',
                    'aliyun' => '阿里云盘',
                    'uc' => 'UC网盘',
                    'tianyi' => '天翼云盘',
                    '115' => '115网盘',
                    'xunlei' => '迅雷网盘',
                    '123' => '123网盘',
                    'mobile' => '移动云盘'
                ];
                $sourceTypeName = $sourceTypeMap[$item['source_type']] ?? $item['source_type'];
                $result['description'] = "[{$sourceTypeName}] " . $result['description'];
            }
            
            // 确保必要字段不为空
            if (!empty($result['title']) && !empty($result['url'])) {
                $results[] = $result;
            }
        }
        
        return $results;
    }
    
    /**
     * 从对象中提取字段值
     * @param array $item 数据项
     * @param string $fieldPath 字段路径
     * @return string 字段值
     */
    private function extractFieldValue($item, $fieldPath) {
        if (empty($fieldPath)) {
            return '';
        }
        
        $value = $item;
        $pathParts = explode('.', $fieldPath);
        
        foreach ($pathParts as $part) {
            if (isset($value[$part])) {
                $value = $value[$part];
            } else {
                return '';
            }
        }
        
        return is_string($value) || is_numeric($value) ? (string)$value : '';
    }
    
    /**
     * 从HTML页面搜索
     * @param array $source HTML配置
     * @param string $keywords 搜索关键词
     * @return array 搜索结果
     */
    private function searchFromHtml($source, $keywords) {
        $url = $source['url'];
        $htmlItem = $source['html_item'] ?? '';
        $htmlTitle = $source['html_title'] ?? '';
        $htmlUrl = $source['html_url'] ?? '';
        $htmlDesc = $source['html_desc'] ?? '';
        
        // 替换URL中的关键词占位符
        $url = str_replace(['{keywords}', '{query}', '{q}', '{search}'], urlencode($keywords), $url);
        
        // 获取HTML内容
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        $html = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200 || empty($html)) {
            throw new \Exception("HTML请求失败，状态码: $httpCode");
        }
        
        // 使用DOM解析HTML
        $dom = new \DOMDocument();
        @$dom->loadHTML('<?xml encoding="UTF-8">' . $html);
        $xpath = new \DOMXPath($dom);
        
        $results = [];
        
        // 查找列表项
        $items = $xpath->query($htmlItem);
        if ($items->length > 0) {
            foreach ($items as $item) {
                // 提取标题
                $titleNode = $xpath->query($htmlTitle, $item)->item(0);
                $title = $titleNode ? trim($titleNode->textContent) : '';
                
                // 提取URL
                $urlNode = $xpath->query($htmlUrl, $item)->item(0);
                $itemUrl = '';
                if ($urlNode) {
                    if ($urlNode->hasAttribute('href')) {
                        $itemUrl = $urlNode->getAttribute('href');
                    } else {
                        $itemUrl = trim($urlNode->textContent);
                    }
                    
                    // 处理相对URL
                    if (!empty($itemUrl) && strpos($itemUrl, 'http') !== 0) {
                        $parsedUrl = parse_url($url);
                        $baseUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
                        $itemUrl = $baseUrl . ($itemUrl[0] === '/' ? '' : '/') . $itemUrl;
                    }
                }
                
                // 提取描述
                $descNode = $xpath->query($htmlDesc, $item)->item(0);
                $description = $descNode ? trim($descNode->textContent) : '';
                
                // 添加结果
                if (!empty($title) && !empty($itemUrl)) {
                    $results[] = [
                        'title' => $title,
                        'url' => $itemUrl,
                        'description' => $description,
                        'type' => '',
                        'code' => ''
                    ];
                }
            }
        }
        
        return $results;
    }
    
    /**
     * 从TG频道搜索
     * @param array $source TG配置
     * @param string $keywords 搜索关键词
     * @return array 搜索结果
     */
    private function searchFromTg($source, $keywords) {
        
        $url = $source['url'];
        $maxResults = 20; // 最大结果数
        $pantype = isset($source['pantype']) ? (int)$source['pantype'] : 0; // 网盘类型
        
        // 构建TG频道搜索URL
        $searchUrl = "https://t.me/s/{$url}?q=" . urlencode($keywords);
        
        // 记录请求信息        
        // 获取DOM
        $dom = $this->getDom($searchUrl);
        if (!$dom) {
            // 记录错误日志
            error_log("TG采集失败: 无法获取DOM - URL: {$searchUrl}");
            return [];
        }
        
        $finder = new \DOMXPath($dom);
        $nodes = $finder->query('//div[contains(@class, "tgme_widget_message_text")]');
        
        // 记录找到的节点数量        
        $results = [];
        
        foreach ($nodes as $node) {
            // 获取HTML内容
            $htmlContent = $dom->saveHTML($node);
            
            // 调试输出HTML内容            
            $parsedItem = [
                'title' => '',
                'url' => '',
                'description' => ''
            ];
            
            // 提取标题（支持多种格式）
            $titlePatterns = [
                '/名称[:：](.+?)(<br|$)/i',           // 名称：xxx
                '/标题[:：](.+?)(<br|$)/i',           // 标题：xxx
                '/资源[:：](.+?)(<br|$)/i',           // 资源：xxx
                '/【(.+?)】/u',                       // 【xxx】
                '/<b>(.+?)<\/b>/i',                   // <b>xxx</b>
                '/<strong>(.+?)<\/strong>/i',         // <strong>xxx</strong>
                '/《(.+?)》/u',                       // 《xxx》
                '/「(.+?)」/u',                       // 「xxx」
                '/\[(.+?)\]/u',                       // [xxx]
                '/^\s*([^<\n\r]+)/m'                  // 第一行非HTML内容
            ];

            $titleFound = false;
            foreach ($titlePatterns as $pattern) {
                if (preg_match($pattern, $htmlContent, $titleMatch)) {
                    $title = trim(strip_tags($titleMatch[1]));
                    if (!empty($title) && mb_strlen($title) > 2) { // 标题长度至少3个字符
                        $parsedItem['title'] = $title;
                        $titleFound = true;
                        break;
                    }
                }
            }

            // 如果没有找到合适的标题，使用搜索关键词
            if (!$titleFound) {
                $parsedItem['title'] = $keywords;
            }
            
            // 提取描述
            if (preg_match('/描述[:：](.+?)<br/i', $htmlContent, $descMatch)) {
                $parsedItem['description'] = trim(strip_tags($descMatch[1]));
            }
            
            // 根据网盘类型提取网盘链接
            switch ($pantype) {
                case 0: // 夸克网盘
                    if (preg_match('/https:\/\/pan\.quark\.cn\/s\/[a-zA-Z0-9_-]+/', $htmlContent, $urlMatch)) {
                        $parsedItem['url'] = trim($urlMatch[0]);
                    }
                    break;
                case 1: // 阿里云盘
                    if (preg_match('/https:\/\/(www\.)?(alipan|aliyundrive)\.com\/s\/[a-zA-Z0-9_-]+/', $htmlContent, $urlMatch)) {
                        $parsedItem['url'] = trim($urlMatch[0]);
                    }
                    break;
                case 2: // 百度网盘
                    if (preg_match('/https:\/\/pan\.baidu\.com\/s\/[a-zA-Z0-9_-]+(\?pwd=[a-zA-Z0-9]+)?/', $htmlContent, $urlMatch)) {
                        $parsedItem['url'] = trim($urlMatch[0]);
                        
                        // 如果URL中没有提取码，尝试从内容中提取
                        if (strpos($parsedItem['url'], '?pwd=') === false && preg_match('/提取码[:：]?\s*([a-zA-Z0-9]{4})/i', $htmlContent, $codeMatch)) {
                            $parsedItem['url'] .= '?pwd=' . trim($codeMatch[1]);
                        }
                    }
                    break;
                default:
                    // 默认尝试提取所有类型的网盘链接（按优先级顺序）
                    $patterns = [
                        // 夸克网盘
                        '/https:\/\/pan\.quark\.cn\/s\/[a-zA-Z0-9_-]+/',
                        // 阿里云盘
                        '/https:\/\/(www\.)?(alipan|aliyundrive)\.com\/s\/[a-zA-Z0-9_-]+/',
                        // 百度网盘
                        '/https:\/\/pan\.baidu\.com\/s\/[a-zA-Z0-9_-]+(\?pwd=[a-zA-Z0-9]+)?/',
                        // 迅雷网盘
                        '/https:\/\/pan\.xunlei\.com\/s\/[a-zA-Z0-9_-]+/',
                        // 天翼云盘
                        '/https:\/\/cloud\.189\.cn\/web\/share\?code=[a-zA-Z0-9]+/',
                        // 115网盘
                        '/https:\/\/115\.com\/s\/[a-zA-Z0-9_-]+/',
                        // UC网盘
                        '/https:\/\/drive\.uc\.cn\/s\/[a-zA-Z0-9_-]+/',
                        // 蓝奏云
                        '/https:\/\/(www\.)?lanzou[a-z]\.com\/[a-zA-Z0-9_-]+/',
                        // 通用网盘链接（以防有新格式）
                        '/https:\/\/[a-zA-Z0-9.-]+\/(s|share)\/[a-zA-Z0-9_-]+/'
                    ];

                    foreach ($patterns as $pattern) {
                        if (preg_match($pattern, $htmlContent, $urlMatch)) {
                            $parsedItem['url'] = trim($urlMatch[0]);

                            // 特殊处理百度网盘提取码
                            if (strpos($parsedItem['url'], 'pan.baidu.com') !== false &&
                                strpos($parsedItem['url'], '?pwd=') === false &&
                                preg_match('/提取码[:：]?\s*([a-zA-Z0-9]{4})/i', $htmlContent, $codeMatch)) {
                                $parsedItem['url'] .= '?pwd=' . trim($codeMatch[1]);
                            }
                            break;
                        }
                    }
                    break;
            }
            
            // 过滤不合法或无效链接
            if (!empty($parsedItem['title']) && !empty($parsedItem['url'])) {
                // 清理标题和描述开头的乱码字符
                $parsedItem['title'] = $this->cleanString($parsedItem['title']);
                $parsedItem['description'] = $this->cleanString($parsedItem['description']);
                
                $results[] = $parsedItem;
                // 记录成功提取的结果                
                // 如果达到最大结果数，停止处理
                if (count($results) >= $maxResults) {
                    break;
                }
            } else {
            }
        }
        
        return $results;
    }
    
    /**
     * 清理字符串开头的乱码字符
     * @param string $str 输入字符串
     * @return string 清理后的字符串
     */
    private function cleanString($str) {
        if (empty($str)) {
            return '';
        }
        
        // 直接移除前两个问号字符（如果存在）
        if (mb_substr($str, 0, 2) === '??') {
            $str = mb_substr($str, 2);
        }
        
        // 去除字符串开头的不可见字符和一些常见乱码
        $str = preg_replace('/^[\x00-\x1F\xC2\xA0\xEF\xBB\xBF]+/', '', $str);
        
        // 去除字符串中连续的控制字符
        $str = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F]+/', '', $str);
        
        // 处理UTF-8 BOM
        $str = str_replace("\xEF\xBB\xBF", '', $str);
        
        // 去除特殊的乱码字符组合
        $str = str_replace('', '', $str);
        
        // 修正一些常见乱码组合
        $patterns = [
            '/\xEF\xBF\xBD/' => '', //
            '/\xC2\xA0/' => ' ',    // 不间断空格
        ];
        
        $str = preg_replace(array_keys($patterns), array_values($patterns), $str);
        
        // 修复UTF-8编码
        if (!mb_check_encoding($str, 'UTF-8')) {
            $str = mb_convert_encoding($str, 'UTF-8', 'UTF-8');
        }
        
        return trim($str);
    }
    
    /**
     * 获取DOM对象
     * @param string $url 页面URL
     * @return \DOMDocument|false 成功返回DOM对象，失败返回false
     */
    private function getDom($url) {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        // 设置超时时间
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15); // 连接超时：15秒
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);       // 响应超时：15秒
        // 临时跳过 SSL 验证
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 避免跳转被拦
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'); // 更新UA

        $html = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // 检查CURL错误
        if ($error) {
            error_log("CURL错误: {$error} - URL: {$url}");
            return false;
        }

        // 检查HTTP状态码
        if ($httpCode !== 200) {
            error_log("HTTP错误: {$httpCode} - URL: {$url}");
            return false;
        }

        if (empty($html)) {
            error_log("获取到空内容 - URL: {$url}");
            return false;
        }

        // 检查是否被反爬虫拦截
        if (strpos($html, 'blocked') !== false || strpos($html, 'captcha') !== false || strpos($html, 'Access denied') !== false) {
            error_log("可能被反爬虫拦截 - URL: {$url}");
            return false;
        }
        
        // 检测编码并转为UTF-8
        $encoding = mb_detect_encoding($html, ['UTF-8', 'GBK', 'GB2312', 'BIG5', 'ASCII'], true);
        if ($encoding !== 'UTF-8') {
            $html = @iconv($encoding, 'UTF-8//IGNORE', $html);
        }
        
        $dom = new \DOMDocument();
        @$dom->loadHTML('<?xml encoding="UTF-8">' . $html);
        
        return $dom;
    }
}
