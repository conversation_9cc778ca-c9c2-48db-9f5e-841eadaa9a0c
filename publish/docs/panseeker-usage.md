# PanSeeker 资源搜索页面使用说明

## 功能概述

PanSeeker 资源搜索页面是一个专门针对 PanSeeker API 开发的资源搜索界面，提供强大的网盘资源搜索和筛选功能。

## 访问方式

1. 登录系统后，在顶部导航栏点击"采集管理"
2. 在下拉菜单中选择"PanSeeker搜索"
3. 或直接访问：`http://publish.com/panseeker.php`

## 主要功能

### 1. 关键词搜索
- 在搜索框中输入要查找的资源关键词
- 支持中文、英文等多种语言搜索
- 建议使用具体的资源名称以获得更准确的结果

### 2. 网盘类型筛选
支持以下13种网盘类型的筛选：
- 百度网盘 (baidu)
- 阿里云盘 (aliyun)
- 夸克网盘 (quark)
- UC网盘 (uc)
- 天翼云盘 (tianyi)
- 115网盘 (115)
- 迅雷网盘 (xunlei)
- 123网盘 (123)
- 移动云盘 (mobile)
- PikPak (pikpak)
- 磁力链接 (magnet)
- 电驴链接 (ed2k)
- 其他 (others)

### 3. 快速筛选按钮
页面提供了多个快速筛选按钮：
- **仅百度网盘**：只搜索百度网盘资源
- **仅夸克网盘**：只搜索夸克网盘资源
- **仅阿里云盘**：只搜索阿里云盘资源
- **百度+夸克**：搜索百度和夸克网盘资源
- **主流网盘**：搜索百度、夸克、阿里云盘资源
- **全选**：选择所有网盘类型
- **清空**：清除所有选择

### 4. 高级选项
- **强制刷新**：勾选此选项可获取最新数据，不使用缓存
- 适用于需要实时最新资源的场景

### 5. 搜索结果管理

#### 结果显示
- 搜索结果以表格形式展示
- 显示资源名称、网盘类型、资源地址、提取码、来源、时间等信息
- 支持点击链接直接访问资源

#### 结果筛选
点击"筛选结果"按钮可进一步筛选搜索结果：
- **按网盘类型筛选**：从搜索结果中筛选特定网盘类型
- **按标题筛选**：输入关键词筛选标题
- **按来源筛选**：筛选TG频道或插件来源

#### 资源导入
- **单个导入**：点击每行的"导入"按钮导入单个资源
- **批量导入**：选择多个资源后点击"批量导入"
- **全选功能**：点击"全选"按钮选择所有资源
- 导入时需要选择目标分类

## 使用技巧

### 1. 搜索优化
- 使用具体的资源名称而不是泛泛的关键词
- 例如：搜索"速度与激情10"而不是"电影"
- 中英文混合搜索效果更佳

### 2. 网盘类型选择
- 如果不确定资源在哪个网盘，可以不选择任何类型（搜索全部）
- 如果知道特定网盘有资源，建议选择对应类型提高搜索效率
- 主流网盘（百度、夸克、阿里）通常资源较多

### 3. 结果筛选
- 搜索结果较多时，使用结果筛选功能快速定位目标资源
- 可以按网盘类型分类查看
- 利用标题筛选功能搜索特定关键词

### 4. 导入策略
- 导入前检查资源链接是否有效
- 选择合适的分类便于后续管理
- 批量导入时注意API调用限制

## 技术特性

### 1. API集成
- 直接调用 PanSeeker 官方API
- 支持实时搜索和缓存机制
- 自动处理API响应数据结构

### 2. 响应式设计
- 支持桌面和移动设备
- 自适应屏幕尺寸
- 优化触摸操作体验

### 3. 数据处理
- 自动解析API返回的分组数据
- 智能提取资源信息
- 支持多种数据格式

### 4. 安全性
- 用户权限验证
- API调用限制
- 防重复导入机制

## 注意事项

1. **API调用限制**：根据用户套餐有不同的API调用次数限制
2. **网络要求**：需要稳定的网络连接以访问PanSeeker API
3. **资源时效性**：网盘资源可能存在失效情况，建议及时保存
4. **分类管理**：合理设置资源分类，便于后续管理和同步

## 故障排除

### 搜索无结果
1. 检查关键词是否正确
2. 尝试更换搜索关键词
3. 检查网盘类型筛选是否过于严格
4. 尝试勾选"强制刷新"获取最新数据

### API调用失败
1. 检查网络连接
2. 确认API调用次数是否已达限制
3. 稍后重试或联系管理员

### 导入失败
1. 检查是否选择了分类
2. 确认资源链接格式是否正确
3. 检查是否重复导入相同资源

---

*更新时间：2025-01-22*
*版本：v1.0*