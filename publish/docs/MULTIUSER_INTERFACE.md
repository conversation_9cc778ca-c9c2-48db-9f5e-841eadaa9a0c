# 多用户界面功能说明

## 概述

心悦网盘搜索管理平台已成功升级为多用户SaaS平台，支持管理员和普通用户两种角色，具备完整的用户管理、配额控制、套餐管理和充值续费功能。

## 主要功能

### 1. 用户角色管理

#### 管理员 (admin)
- 拥有系统所有权限
- 可以管理所有用户账户
- 可以生成和管理充值卡
- 可以查看系统全局统计数据
- 不受配额限制

#### 普通用户 (user)
- 只能管理自己的数据
- 受套餐配额限制
- 可以进行充值续费
- 数据完全隔离

### 2. 套餐系统

#### 基础版 (basic)
- 最大站点数：5个
- 最大资源数：1000个
- 每月API调用：10000次

#### 专业版 (professional)
- 最大站点数：20个
- 最大资源数：5000个
- 每月API调用：50000次

#### 企业版 (enterprise)
- 最大站点数：100个
- 最大资源数：20000个
- 每月API调用：200000次

### 3. 界面功能

#### 导航栏增强
- 显示用户角色信息
- 显示套餐等级和到期时间
- 配额使用警告提示
- 角色相关菜单项

#### 控制面板
- 管理员：显示系统全局统计
- 普通用户：显示个人数据和配额使用情况
- 套餐信息卡片
- 配额使用进度条

#### 资源管理
- 配额使用显示
- 达到配额限制时禁用添加按钮
- 升级套餐提示

#### 站点管理
- 站点配额显示和限制
- 配额警告提示
- 升级套餐引导

#### 个人资料
- 套餐信息展示
- 配额使用可视化
- 到期时间提醒
- 快速充值入口

### 4. 充值续费系统

#### 充值卡类型
- **天数卡**：延长当前套餐时间
- **套餐升级卡**：升级套餐等级并延长时间

#### 充值卡格式
- 新版：卡号-卡密（如：ABC123-DEF456）
- 旧版：仅卡号（如：ABC123）

#### 充值流程
1. 用户在充值页面输入充值卡信息
2. 系统验证充值卡有效性
3. 根据卡片类型更新用户套餐或延长时间
4. 记录充值日志
5. 标记充值卡为已使用

### 5. 管理员功能

#### 用户管理
- 用户列表和搜索
- 添加/编辑/删除用户
- 套餐管理
- 用户状态统计

#### 充值卡管理
- 批量生成充值卡
- 充值卡状态管理
- 使用记录查询
- 充值卡统计

### 6. 数据隔离

#### 完全隔离
- 每个用户只能看到和操作自己的数据
- 资源、站点、分类、同步记录等完全隔离
- 数据库层面通过user_id字段实现隔离

#### 权限控制
- 页面级权限检查
- API级权限验证
- 数据操作权限控制

## 技术实现

### 数据库设计
- 所有主要表添加user_id字段
- 充值卡和充值记录表
- 操作日志和系统通知表
- 用户套餐和配额字段

### 核心类增强
- `Core\Auth`：增加配额检查和权限验证
- `Core\Resource`：数据隔离和权限控制
- `Core\Site`：用户数据隔离
- `Core\Category`：分类数据隔离

### 前端界面
- Bootstrap 5 + FontAwesome 6.0.0
- 响应式设计
- 统计卡片悬停效果
- 配额进度条可视化
- 角色相关界面元素

## 使用说明

### 管理员操作
1. 登录管理员账户
2. 在用户管理中添加新用户
3. 在充值卡管理中生成充值卡
4. 将充值卡提供给用户进行充值

### 用户操作
1. 登录用户账户
2. 查看控制面板了解配额使用情况
3. 在资源管理中添加和管理资源
4. 在站点管理中配置同步站点
5. 配额不足时通过充值页面进行续费



## 安全特性

### 数据安全
- 严格的数据隔离机制
- SQL注入防护
- XSS攻击防护
- CSRF保护

### 权限安全
- 基于角色的访问控制
- 页面级权限验证
- API级权限检查
- 敏感操作确认

### 充值安全
- 充值卡唯一性验证
- 充值记录完整日志
- IP地址和用户代理记录
- 防重复使用机制

## 升级说明

### 从单用户升级
1. 运行数据库升级脚本
2. 现有数据自动分配给管理员用户
3. 创建新的普通用户账户
4. 配置套餐和配额限制

### 配置建议
- 定期备份数据库
- 监控用户配额使用情况
- 及时处理过期用户
- 定期清理无效充值卡

## 故障排除

### 常见问题
1. **配额显示不正确**：检查Auth类的checkQuota方法
2. **数据隔离失效**：验证user_id字段和查询条件
3. **充值卡无法使用**：检查卡片状态和过期时间
4. **权限验证失败**：确认用户角色和登录状态

### 调试工具
- 数据库查询日志
- PHP错误日志
- 浏览器开发者工具

## 后续开发

### 计划功能
- 在线支付集成
- 更多套餐类型
- 用户使用统计
- 邮件通知系统
- API访问限制

### 扩展建议
- 增加更多用户角色
- 实现子账户功能
- 添加使用量分析
- 集成第三方支付
- 移动端适配

---

**版本**：v2.0.0  
**更新时间**：2024年  
**开发者**：心悦搜索团队
