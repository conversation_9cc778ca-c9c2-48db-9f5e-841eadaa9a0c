# Panseeker API 文档

## 概述

Panseeker 是一个高性能的网盘资源搜索API服务，支持TG频道和插件搜索。系统设计以性能和可扩展性为核心，支持多频道多插件并发搜索、结果智能排序和网盘类型分类。

**官方API地址**: `https://api.panseeker.com/api/search`

## 支持的网盘类型

- 百度网盘 (`baidu`)
- 阿里云盘 (`aliyun`) 
- 夸克网盘 (`quark`)
- 天翼云盘 (`tianyi`)
- UC网盘 (`uc`)
- 移动云盘 (`mobile`)
- 115网盘 (`115`)
- PikPak (`pikpak`)
- 迅雷网盘 (`xunlei`)
- 123网盘 (`123`)
- 磁力链接 (`magnet`)
- 电驴链接 (`ed2k`)
- 其他 (`others`)

## API接口

### 1. 搜索API

**接口地址**: `/api/search`  
**请求方法**: `POST` 或 `GET`  
**Content-Type**: `application/json`（POST方法）

#### POST请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| kw | string | 是 | 搜索关键词 |
| channels | string[] | 否 | 搜索的频道列表，不提供则使用默认配置 |
| conc | number | 否 | 并发搜索数量，不提供则自动设置为频道数+插件数+10 |
| refresh | boolean | 否 | 强制刷新，不使用缓存，便于调试和获取最新数据 |
| res | string | 否 | 结果类型：all(返回所有结果)、results(仅返回results)、merge(仅返回merged_by_type)，默认为merge |
| src | string | 否 | 数据源类型：all(默认，全部来源)、tg(仅Telegram)、plugin(仅插件) |
| plugins | string[] | 否 | 指定搜索的插件列表 |
| cloud_types | string[] | 否 | 指定返回的网盘类型列表 |
| ext | object | 否 | 扩展参数，传递给插件的自定义参数 |

#### GET请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| kw | string | 是 | 搜索关键词 |
| channels | string | 否 | TG频道列表，使用英文逗号分隔多个频道，不提供则使用默认配置 |
| conc | number | 否 | 并发搜索数量，不提供则自动设置为频道数+插件数+10 |
| refresh | boolean | 否 | 强制刷新，设置为"true"表示不使用缓存 |
| res | string | 否 | 结果类型：all/results/merge |
| src | string | 否 | 数据来源类型：all/tg/plugin |
| plugins | string | 否 | 插件列表，使用英文逗号分隔多个插件名，不指定则搜索全部插件 |
| cloud_types | string | 否 | 网盘类型列表，使用英文逗号分隔，支持：baidu、aliyun、quark等 |
| ext | string | 否 | JSON格式的扩展参数 |

#### 请求示例

**POST请求**:
```json
{
  "kw": "速度与激情",
  "channels": ["tgsearchers3", "xxx"],
  "conc": 2,
  "refresh": true,
  "res": "merge",
  "src": "all",
  "plugins": ["jikepan"],
  "cloud_types": ["baidu", "quark"],
  "ext": {
    "title_en": "Fast and Furious",
    "is_all": true
  }
}
```

**GET请求**:
```
GET /api/search?kw=速度与激情&channels=tgsearchers3,xxx&conc=2&refresh=true&res=merge&src=tg&cloud_types=baidu,quark
```

#### 成功响应示例

```json
{
  "total": 15,
  "results": [
    {
      "message_id": "12345",
      "unique_id": "channel-12345",
      "channel": "tgsearchers3",
      "datetime": "2023-06-10T14:23:45Z",
      "title": "速度与激情全集1-10",
      "content": "速度与激情系列全集，1080P高清...",
      "links": [
        {
          "type": "baidu",
          "url": "https://pan.baidu.com/s/1abcdef",
          "password": "1234"
        }
      ],
      "tags": ["电影", "合集"],
      "images": [
        "https://cdn1.cdn-telegram.org/file/xxx.jpg"
      ]
    }
  ],
  "merged_by_type": {
    "baidu": [
      {
        "url": "https://pan.baidu.com/s/1abcdef",
        "password": "1234",
        "note": "速度与激情全集1-10",
        "datetime": "2023-06-10T14:23:45Z",
        "source": "tg:频道名称",
        "images": [
          "https://cdn1.cdn-telegram.org/file/xxx.jpg"
        ]
      }
    ],
    "quark": [
      {
        "url": "https://pan.quark.cn/s/xxxx",
        "password": "",
        "note": "凡人修仙传",
        "datetime": "2023-06-10T15:30:22Z",
        "source": "plugin:插件名",
        "images": []
      }
    ],
    "aliyun": []
  }
}
```

#### 字段说明

- `source`: 数据来源标识
  - `tg:频道名称`: 来自Telegram频道
  - `plugin:插件名`: 来自指定插件
  - `unknown`: 未知来源
- `images`: TG消息中的图片链接数组（可选字段）

#### 错误响应

```json
{
  "code": 400,
  "message": "关键词不能为空"
}
```

### 2. 健康检查API

**接口地址**: `/api/health`  
**请求方法**: `GET`

#### 成功响应

```json
{
  "channels_count": 1,
  "channels": [
    "tgsearchers3"
  ],
  "plugin_count": 16,
  "plugins": [
    "pansearch",
    "panta", 
    "qupansou",
    "hunhepan",
    "jikepan",
    "pan666",
    "panyq",
    "susu",
    "xuexizhinan",
    "hdr4k",
    "labi",
    "shandian",
    "duoduo",
    "muou",
    "wanou",
    "ouge",
    "zhizhen",
    "huban"
  ],
  "plugins_enabled": true,
  "status": "ok"
}
```

## 使用建议

1. **推荐使用GET请求**：对于简单的搜索需求，GET请求更方便
2. **合理设置并发数**：conc参数默认为频道数+插件数+10，通常无需手动设置
3. **使用缓存**：除非需要最新数据，否则不建议设置refresh=true
4. **选择合适的结果类型**：
   - `merge`: 仅返回按网盘类型分组的结果（推荐）
   - `results`: 仅返回原始搜索结果
   - `all`: 返回完整数据（包含results和merged_by_type）

## 集成到心悦搜剧系统

### 配置参数

- **来源名称**: `Panseeker-API`
- **接口地址**: `https://api.panseeker.com/api/search`
- **请求方法**: `GET`
- **请求头**: `{"Content-Type": "application/json"}`
- **固定参数**: `{"res": "merge"}`
- **字段映射**: 
```json
{
  "list": "merged_by_type",
  "title": "note",
  "url": "url", 
  "description": "note"
}
```
- **关键词参数名**: `kw`

---

*文档更新时间: 2025-08-22*  
*API版本: v1*
