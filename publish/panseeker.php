<?php
/**
 * PanSeeker资源搜索页面
 */

require_once 'index.php';

// 检查用户是否已登录
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// 实例化资源类
$resourceObj = new \Core\Resource();

// 处理表单提交
$message = '';
$messageType = '';

// 获取搜索参数
$keywords = isset($_GET['keywords']) ? trim($_GET['keywords']) : (isset($_POST['keywords']) ? trim($_POST['keywords']) : '');
$cloud_types = isset($_GET['cloud_types']) ? $_GET['cloud_types'] : (isset($_POST['cloud_types']) ? $_POST['cloud_types'] : []);
$refresh = isset($_GET['refresh']) ? (bool)$_GET['refresh'] : (isset($_POST['refresh']) ? (bool)$_POST['refresh'] : false);

// 确保cloud_types是数组
if (!is_array($cloud_types)) {
    $cloud_types = !empty($cloud_types) ? explode(',', $cloud_types) : [];
}

// 支持的网盘类型配置
$supportedCloudTypes = [
    'baidu' => '百度网盘',
    'aliyun' => '阿里云盘',
    'quark' => '夸克网盘',
    'uc' => 'UC网盘',
    'tianyi' => '天翼云盘',
    '115' => '115网盘',
    'xunlei' => '迅雷网盘',
    '123' => '123网盘',
    'mobile' => '移动云盘',
    'pikpak' => 'PikPak',
    'magnet' => '磁力链接',
    'ed2k' => '电驴链接',
    'others' => '其他'
];

// 初始化结果变量
$result = null;

// PanSeeker API搜索函数
function searchPanSeeker($keywords, $cloud_types = [], $refresh = false) {
    if (empty($keywords)) {
        return [
            'success' => false,
            'message' => '请输入搜索关键词',
            'resources' => []
        ];
    }
    
    $url = 'https://api.panseeker.com/api/search';
    
    // 构建请求参数
    $params = [
        'kw' => $keywords,
        'res' => 'merge'
    ];
    
    // 添加网盘类型筛选
    if (!empty($cloud_types)) {
        $params['cloud_types'] = implode(',', $cloud_types);
    }
    
    // 添加刷新参数
    if ($refresh) {
        $params['refresh'] = 'true';
    }
    
    // 构建完整URL
    $requestUrl = $url . '?' . http_build_query($params);
    
    // 使用cURL发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $requestUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    // 添加请求头
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json, text/plain, */*',
        'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
        'Origin: http://publish.com',
        'Referer: http://publish.com/panseeker.php',
        'Connection: keep-alive'
    ]);
    
    $response = curl_exec($ch);
    $error = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false || $httpCode != 200) {
        return [
            'success' => false,
            'message' => 'API请求失败: ' . ($error ? $error : "HTTP状态码: {$httpCode}"),
            'resources' => []
        ];
    }
    
    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'success' => false,
            'message' => 'API响应解析失败: ' . json_last_error_msg(),
            'resources' => []
        ];
    }
    
    // 检查API响应
    if (isset($data['code']) && $data['code'] !== 0) {
        return [
            'success' => false,
            'message' => $data['message'] ?? 'API返回错误',
            'resources' => []
        ];
    }
    
    // 提取merged_by_type数据
    $mergedData = $data['data']['merged_by_type'] ?? [];
    $resources = [];
    $total = 0;
    
    // 网盘类型映射
    $typeMap = [
        'baidu' => '百度网盘',
        'aliyun' => '阿里云盘',
        'quark' => '夸克网盘',
        'uc' => 'UC网盘',
        'tianyi' => '天翼云盘',
        '115' => '115网盘',
        'xunlei' => '迅雷网盘',
        '123' => '123网盘',
        'mobile' => '移动云盘',
        'pikpak' => 'PikPak',
        'magnet' => '磁力链接',
        'ed2k' => '电驴链接',
        'others' => '其他'
    ];
    
    // 合并所有类型的数据
    foreach ($mergedData as $cloudType => $items) {
        if (!empty($items) && is_array($items)) {
            foreach ($items as $item) {
                $resources[] = [
                    'title' => $item['note'] ?? '',
                    'url' => $item['url'] ?? '',
                    'password' => $item['password'] ?? '',
                    'description' => $item['note'] ?? '',
                    'source' => $item['source'] ?? '',
                    'datetime' => $item['datetime'] ?? '',
                    'type' => $typeMap[$cloudType] ?? $cloudType,
                    'type_key' => $cloudType
                ];
                $total++;
            }
        }
    }
    
    return [
        'success' => true,
        'message' => "找到 {$total} 个资源",
        'resources' => $resources,
        'count' => $total,
        'total' => $data['data']['total'] ?? $total
    ];
}

// 处理搜索请求
if (!empty($keywords)) {
    // 检查API调用限制并增加计数
    if (!$auth->incrementApiCalls()) {
        $message = '您的API调用次数已达到本月限制，请升级套餐或等待下月重置';
        $messageType = 'danger';
    } else {
        $result = searchPanSeeker($keywords, $cloud_types, $refresh);
        if ($result['success']) {
            $message = $result['message'];
            $messageType = 'success';
        } else {
            $message = "搜索失败: {$result['message']}";
            $messageType = 'danger';
        }
    }
}

// 处理导入资源请求
if (isset($_POST['import_resource']) && isset($_POST['selected_resources'])) {
    $selectedResources = array_unique($_POST['selected_resources']);
    $category = isset($_POST['category']) ? (int)$_POST['category'] : 0;
    
    $importCount = 0;
    $failCount = 0;
    $duplicateCount = 0;
    $duplicateResources = [];

    foreach ($selectedResources as $resourceId) {
        if (!isset($_POST["resource_data_{$resourceId}"])) {
            $failCount++;
            continue;
        }

        $resourceData = json_decode(base64_decode($_POST["resource_data_{$resourceId}"]), true);
        if (!$resourceData) {
            $failCount++;
            continue;
        }

        if (empty($resourceData['title']) || empty($resourceData['url'])) {
            $failCount++;
            continue;
        }

        $data = [
            'title' => $resourceData['title'],
            'url' => $resourceData['url'],
            'description' => $resourceData['description'] ?? '',
            'source_category_id' => $category,
            'password' => $resourceData['password'] ?? '',
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 检查URL是否重复
        $db = \Core\Database::getInstance();
        $userId = $auth->getUser()['id'];
        $existingSql = "SELECT COUNT(*) as count FROM resources WHERE url = ? AND user_id = ?";
        $existingCount = $db->fetch($existingSql, [$data['url'], $userId])['count'] ?? 0;

        if ($existingCount > 0) {
            $duplicateCount++;
            $duplicateResources[] = $data['title'];
            continue;
        }

        $resultAdd = $resourceObj->add($data);
        if ($resultAdd) {
            if (!$auth->incrementApiCalls()) {
                error_log("用户 {$auth->getUser()['username']} API调用次数已达限制");
            }
            $importCount++;
        } else {
            $failCount++;
        }
    }
    
    // 生成友好的提示信息
    $messageParts = [];
    if ($importCount > 0) {
        $messageParts[] = "成功导入 {$importCount} 个资源";
    }
    if ($duplicateCount > 0) {
        $messageParts[] = "{$duplicateCount} 个资源已存在，已跳过";
    }
    if ($failCount > 0) {
        $messageParts[] = "{$failCount} 个资源导入失败";
    }

    if ($importCount > 0) {
        $message = implode('，', $messageParts);
        $messageType = 'success';
        if ($duplicateCount > 0) {
            $message .= '<br><small class="text-muted">重复资源: ' . implode('、', array_slice($duplicateResources, 0, 3));
            if (count($duplicateResources) > 3) {
                $message .= ' 等' . count($duplicateResources) . '个';
            }
            $message .= '</small>';
        }
    } elseif ($duplicateCount > 0 && $failCount == 0) {
        $message = "所选资源均已存在，无需重复导入";
        $messageType = 'info';
    } else {
        $message = '没有资源被导入' . ($failCount > 0 ? '，请检查资源格式或配额限制' : '');
        $messageType = 'warning';
    }
    
    // 导入后重新搜索
    if (!empty($keywords)) {
        $result = searchPanSeeker($keywords, $cloud_types, $refresh);
    }
}

// 获取分类列表
$categories = $resourceObj->getCategoryNames();

// 页面标题
$pageTitle = 'PanSeeker 资源搜索';

// 包含页头
include 'templates/header.php';

// 包含PanSeeker资源模板
include 'templates/panseeker.php';

// 包含页脚
include 'templates/footer.php';
?>