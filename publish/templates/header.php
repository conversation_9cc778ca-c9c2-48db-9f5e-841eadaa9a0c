<?php
/**
 * 页面头部模板
 */

// 获取当前页面
$current_page = basename($_SERVER['SCRIPT_NAME']);

// 获取用户信息
$auth = Core\Auth::getInstance();
$user = $auth->getUser();

// 获取系统配置
$config = require __DIR__ . '/../config/config.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title ?? ($config['app_name'] ?? 'Panseeker资源管理系统'); ?></title>
    <!-- 先加载jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <!-- 然后加载Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 加载FontAwesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <!-- 添加SweetAlert2库 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.8/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.4.8/dist/sweetalert2.all.min.js"></script>
    <?php if (isset($extra_css)): echo $extra_css; endif; ?>
    <style>
        /* 自定义表格样式 */
        .table td.url-column {
            max-width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .table td.actions-column {
            min-width: 300px;
            position: relative; /* 添加相对定位 */
        }
        .btn-group.actions-group {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
        .btn-group.actions-group .btn {
            margin-bottom: 4px;
        }
        /* 修复下拉菜单定位和层级 */
        .dropdown-menu {
            z-index: 1050 !important; /* 提高z-index */
        }
        /* 禁用表单元素的拖动行为 */
        form, table, tr, td, th, input, select, button, a {
            -webkit-user-drag: none;
            user-drag: none;
        }
        /* 确保下拉菜单不被截断 */
        .table-responsive {
            overflow-x: visible !important;
        }
        /* 修复更多按钮的下拉菜单 */
        .btn-group-sm .dropdown-menu {
            position: absolute !important;
            transform: none !important;
            top: 100% !important;
            left: auto !important;
            right: 0 !important;
            will-change: auto !important;
        }
        @media (max-width: 992px) {
            .table td.url-column {
                max-width: 150px;
            }
            .table td.actions-column {
                min-width: 200px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <?php echo $config['app_name'] ?? 'Panseeker资源管理系统'; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <?php if ($auth->isLoggedIn()): ?>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page == 'dashboard.php' ? 'active' : ''; ?>" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> 控制面板
                        </a>
                    </li>


                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page == 'sites.php' ? 'active' : ''; ?>" href="sites.php">
                            <i class="fas fa-globe"></i> 站点管理
                            <?php if (!$auth->isAdmin()): ?>
                                <?php
                                $siteQuota = $auth->checkQuota('sites');
                                if ($siteQuota['max'] > 0 && $siteQuota['current'] >= $siteQuota['max']): ?>
                                    <span class="badge bg-danger ms-1" title="站点配额已满">满</span>
                                <?php elseif ($siteQuota['max'] > 0 && $siteQuota['current'] >= $siteQuota['max'] * 0.8): ?>
                                    <span class="badge bg-warning ms-1" title="站点配额即将用完"><?php echo $siteQuota['current']; ?>/<?php echo $siteQuota['max']; ?></span>
                                <?php endif; ?>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page == 'resources.php' ? 'active' : ''; ?>" href="resources.php">
                            <i class="fas fa-folder-open"></i> 资源管理
                            <?php if (!$auth->isAdmin()): ?>
                                <?php
                                $resourceQuota = $auth->checkQuota('resources');
                                if ($resourceQuota['max'] > 0 && $resourceQuota['current'] >= $resourceQuota['max']): ?>
                                    <span class="badge bg-danger ms-1" title="资源配额已满">满</span>
                                <?php elseif ($resourceQuota['max'] > 0 && $resourceQuota['current'] >= $resourceQuota['max'] * 0.8): ?>
                                    <span class="badge bg-warning ms-1" title="资源配额即将用完"><?php echo $resourceQuota['current']; ?>/<?php echo $resourceQuota['max']; ?></span>
                                <?php endif; ?>
                            <?php endif; ?>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page == 'sync.php' ? 'active' : ''; ?>" href="sync.php">
                            <i class="fas fa-sync-alt"></i> 同步管理
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo in_array($current_page, ['crawler.php', 'crawler_sources.php', 'hunhepan.php', 'kule.php', 'panseeker.php']) ? 'active' : ''; ?>" href="#" id="crawlerDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-cloud-download-alt"></i> 采集管理
                            <?php if (!$auth->isAdmin()): ?>
                                <?php
                                $quotaInfo = $auth->checkQuota('api_calls');
                                if ($quotaInfo['max'] > 0 && $quotaInfo['current'] >= $quotaInfo['max'] * 0.8): ?>
                                    <span class="badge bg-warning ms-1" title="配额即将用完">!</span>
                                <?php endif; ?>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="crawlerDropdown">
                            <li><a class="dropdown-item" href="crawler.php?action=search"><i class="fas fa-search"></i> 全网搜索</a></li>
                            <li><a class="dropdown-item" href="hunhepan.php"><i class="fas fa-cloud-download-alt"></i> 混合盘资源</a></li>
                            <li><a class="dropdown-item" href="kule.php"><i class="fas fa-cloud-download-alt"></i> 酷乐资源</a></li>
                            <li><a class="dropdown-item" href="panseeker.php"><i class="fas fa-search-plus"></i> PanSeeker搜索</a></li>
                            <li><a class="dropdown-item" href="crawler_sources.php"><i class="fas fa-cog"></i> 采集来源</a></li>
                            <?php if ($auth->isAdmin()): ?>
                            <li><a class="dropdown-item" href="crawler.php"><i class="fas fa-tasks"></i> 采集任务</a></li>
                            <?php endif; ?>
                            <?php if (!$auth->isAdmin()): ?>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <span class="dropdown-item-text">
                                    <small class="text-muted">
                                        <i class="fas fa-chart-bar"></i>
                                        <?php if ($quotaInfo['max'] == -1): ?>
                                            API调用：无限制
                                        <?php else: ?>
                                            API调用：<?php echo $quotaInfo['current']; ?>/<?php echo $quotaInfo['max']; ?>
                                        <?php endif; ?>
                                    </small>
                                </span>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo in_array($current_page, ['categories.php', 'category_mappings.php']) ? 'active' : ''; ?>" href="#" id="categoryDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-tags"></i> 分类管理
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="categoryDropdown">
                            <li><a class="dropdown-item" href="categories.php"><i class="fas fa-tags"></i> 分类编辑</a></li>
                            <li><a class="dropdown-item" href="category_mappings.php"><i class="fas fa-project-diagram"></i> 分类映射</a></li>
                        </ul>
                    </li>
                    <?php if ($auth->isAdmin()): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $current_page == 'users.php' ? 'active' : ''; ?>" href="users.php">
                            <i class="fas fa-users"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle <?php echo in_array($current_page, ['packages.php', 'recharge_cards.php']) ? 'active' : ''; ?>" href="#" id="marketingDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-chart-line"></i> 营销管理
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="marketingDropdown">
                            <li><a class="dropdown-item" href="packages.php"><i class="fas fa-crown"></i> 套餐管理</a></li>
                            <li><a class="dropdown-item" href="recharge_cards.php"><i class="fas fa-credit-card"></i> 充值卡管理</a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav align-items-center">
                    <!-- 用户信息和套餐显示 -->
                    <?php if (!$auth->isAdmin()): ?>
                    <li class="nav-item d-flex align-items-center">
                        <span class="navbar-text me-3 d-flex align-items-center">
                            <small class="text-light">
                                <i class="fas fa-crown me-1"></i>
                                <?php
                                $packageNames = [
                                    'basic' => '基础版',
                                    'professional' => '专业版',
                                    'enterprise' => '企业版'
                                ];

                                // 使用UserPackageManager获取当前套餐
                                if (!isset($userPackageManager)) {
                                    require_once __DIR__ . '/../core/UserPackageManager.php';
                                    $userPackageManager = new Core\UserPackageManager();
                                }
                                $currentPackage = $userPackageManager->getUserCurrentPackage($user['id']);
                                echo $packageNames[$currentPackage['package_level']] ?? '未知套餐';
                                ?>
                                <span class="mx-1">|</span>
                                <i class="fas fa-calendar-alt me-1"></i>
                                <?php if ($currentPackage['expire_date']): ?>
                                <?php
                                $expireDate = new DateTime($currentPackage['expire_date']);
                                $now = new DateTime();
                                $diff = $now->diff($expireDate);
                                if ($expireDate < $now): ?>
                                    <span class="text-warning">已过期</span>
                                <?php elseif ($diff->days <= 7): ?>
                                    <span class="text-warning"><?php echo $diff->days; ?>天后过期</span>
                                <?php else: ?>
                                    <?php echo $expireDate->format('Y-m-d'); ?>
                                <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-success">永久有效</span>
                                <?php endif; ?>
                            </small>
                        </span>
                    </li>
                    <?php endif; ?>

                    <li class="nav-item dropdown d-flex align-items-center">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i>
                            <?php echo htmlspecialchars($user['username']); ?>
                            <?php if ($auth->isAdmin()): ?>
                                <span class="badge bg-warning ms-1">管理员</span>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <h6 class="dropdown-header">
                                    <i class="fas fa-info-circle"></i> 账户信息
                                </h6>
                            </li>
                            <li>
                                <span class="dropdown-item-text">
                                    <small class="text-muted">
                                        角色：<?php echo $auth->isAdmin() ? '管理员' : '普通用户'; ?><br>
                                        <?php if (!$auth->isAdmin()): ?>
                                        套餐：<?php echo $packageNames[$currentPackage['package_level']] ?? '未知'; ?><br>
                                        到期：<?php if ($currentPackage['expire_date']): ?>
                                        <?php
                                        $expireDate = new DateTime($currentPackage['expire_date']);
                                        echo $expireDate->format('Y-m-d');
                                        ?>
                                        <?php else: ?>
                                        永久有效
                                        <?php endif; ?>
                                        <?php endif; ?>
                                    </small>
                                </span>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item <?php echo $current_page == 'profile.php' ? 'active' : ''; ?>" href="profile.php"><i class="fas fa-user"></i> 个人资料</a></li>
                            <?php if (!$auth->isAdmin()): ?>
                            <li><a class="dropdown-item" href="recharge.php"><i class="fas fa-credit-card"></i> 套餐续费</a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
            <?php endif; ?>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="container-fluid py-4">
        <?php if (isset($page_header)): ?>
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><?php echo $page_header; ?></h2>
            <?php if (isset($page_actions)): ?>
            <div class="page-actions">
                <?php echo $page_actions; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
