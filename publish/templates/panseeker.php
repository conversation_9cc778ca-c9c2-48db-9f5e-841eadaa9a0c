<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3"><?php echo $pageTitle; ?></h1>
    </div>
    
    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-search"></i> PanSeeker 资源搜索</h5>
        </div>
        <div class="card-body">
            <form method="get" action="" class="needs-validation" novalidate>
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="keywords" class="form-label">搜索关键词 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="keywords" name="keywords" 
                               value="<?php echo htmlspecialchars($keywords); ?>" 
                               placeholder="请输入要搜索的资源关键词" required>
                        <div class="invalid-feedback">
                            请输入搜索关键词
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">网盘类型筛选</label>
                        <div class="form-check-container" style="max-height: 200px; overflow-y: auto;">
                            <?php foreach ($supportedCloudTypes as $key => $name): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" 
                                       name="cloud_types[]" value="<?php echo $key; ?>" 
                                       id="cloud_type_<?php echo $key; ?>"
                                       <?php echo in_array($key, $cloud_types) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="cloud_type_<?php echo $key; ?>">
                                    <?php echo htmlspecialchars($name); ?>
                                </label>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <small class="text-muted">不选择则搜索所有类型</small>
                    </div>
                    <div class="col-md-2">
                        <label class="d-block">&nbsp;</label>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="refresh" value="1" 
                                   id="refresh" <?php echo $refresh ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="refresh">
                                强制刷新
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> 开始搜索
                        </button>
                    </div>
                </div>
                
                <!-- 快速筛选按钮 -->
                <div class="row mt-3">
                    <div class="col-12">
                        <label class="form-label">快速筛选：</label>
                        <div class="btn-group-custom">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectCloudTypes(['baidu'])">
                                仅百度网盘
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectCloudTypes(['quark'])">
                                仅夸克网盘
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectCloudTypes(['aliyun'])">
                                仅阿里云盘
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectCloudTypes(['baidu', 'quark'])">
                                百度+夸克
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectCloudTypes(['baidu', 'quark', 'aliyun'])">
                                主流网盘
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectAllCloudTypes()">
                                全选
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearCloudTypes()">
                                清空
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <?php if (isset($result) && is_array($result) && isset($result['success']) && $result['success'] && isset($result['resources']) && is_array($result['resources']) && !empty($result['resources'])): ?>
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">搜索结果 (共 <?php echo $result['count']; ?> 个)</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-info" id="filterResultsBtn">
                        <i class="fas fa-filter"></i> 筛选结果
                    </button>
                    <button type="button" class="btn btn-sm btn-success" id="selectAllBtn">
                        <i class="fas fa-check-double"></i> 全选
                    </button>
                    <button type="button" class="btn btn-sm btn-primary" id="batchImportBtn" data-bs-toggle="modal" data-bs-target="#batchImportModal">
                        <i class="fas fa-cloud-download-alt"></i> 批量导入
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- 结果筛选器 -->
            <div class="row mb-3" id="resultFilter" style="display: none;">
                <div class="col-md-3">
                    <label for="filterType" class="form-label">按网盘类型筛选</label>
                    <select class="form-select" id="filterType">
                        <option value="">全部类型</option>
                        <?php 
                        $resultTypes = [];
                        foreach ($result['resources'] as $resource) {
                            if (!empty($resource['type'])) {
                                $resultTypes[$resource['type']] = true;
                            }
                        }
                        foreach (array_keys($resultTypes) as $type): 
                        ?>
                        <option value="<?php echo htmlspecialchars($type); ?>"><?php echo htmlspecialchars($type); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="filterTitle" class="form-label">按标题筛选</label>
                    <input type="text" class="form-control" id="filterTitle" placeholder="输入关键词筛选标题">
                </div>
                <div class="col-md-3">
                    <label for="filterSource" class="form-label">按来源筛选</label>
                    <select class="form-select" id="filterSource">
                        <option value="">全部来源</option>
                        <option value="tg:">TG频道</option>
                        <option value="plugin:">插件</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="button" class="btn btn-sm btn-primary" onclick="applyFilter()">
                            <i class="fas fa-filter"></i> 应用筛选
                        </button>
                        <button type="button" class="btn btn-sm btn-secondary" onclick="clearFilter()">
                            <i class="fas fa-times"></i> 清除
                        </button>
                    </div>
                </div>
            </div>
            
            <form id="importForm" method="post" action="">
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead>
                            <tr>
                                <th width="40" style="min-width: 40px; max-width: 40px;">
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                </th>
                                <th width="350" style="min-width: 150px; max-width: 350px;">资源名称</th>
                                <th width="50" style="min-width: 50px; max-width: 90px;">网盘类型</th>
                                <th width="180" style="min-width: 150px; max-width: 180px;">资源地址</th>
                                <th width="50" style="min-width: 50px; max-width: 70px;">提取码</th>
                                <th width="90" style="min-width: 90px; max-width: 120px;">来源</th>
                                <th width="50" style="min-width: 50px; max-width: 80px;">时间</th>
                                <th width="50" style="min-width: 50px; max-width: 80px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($result['resources'] as $index => $resource): ?>
                            <tr class="resource-row" 
                                data-type="<?php echo htmlspecialchars($resource['type']); ?>"
                                data-title="<?php echo htmlspecialchars($resource['title']); ?>"
                                data-source="<?php echo htmlspecialchars($resource['source']); ?>">
                                <td>
                                    <input type="checkbox" class="form-check-input resource-checkbox" name="selected_resources[]" value="<?php echo $index; ?>">
                                    <input type="hidden" name="resource_data_<?php echo $index; ?>" value="<?php echo base64_encode(json_encode([
                                        'title' => $resource['title'],
                                        'url' => $resource['url'],
                                        'description' => $resource['description'],
                                        'password' => $resource['password'],
                                        'type' => $resource['type'],
                                        'source' => $resource['source']
                                    ])); ?>">
                                </td>
                                <td>
                                    <div class="fw-bold" 
                                         title="<?php echo htmlspecialchars($resource['title']); ?>">
                                        <?php echo htmlspecialchars($resource['title']); ?>
                                    </div>
                                    <?php if (!empty($resource['description']) && $resource['description'] !== $resource['title']): ?>
                                    <small class="text-muted d-block" 
                                           title="<?php echo htmlspecialchars($resource['description']); ?>">
                                        <?php echo htmlspecialchars(mb_substr($resource['description'], 0, 60)); ?><?php echo mb_strlen($resource['description']) > 60 ? '...' : ''; ?>
                                    </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo match($resource['type_key'] ?? '') {
                                            'baidu' => 'primary',
                                            'quark' => 'success', 
                                            'aliyun' => 'info',
                                            'uc' => 'warning',
                                            'tianyi' => 'secondary',
                                            '115' => 'dark',
                                            default => 'light text-dark'
                                        };
                                    ?>">
                                        <?php echo htmlspecialchars($resource['type']); ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="<?php echo htmlspecialchars($resource['url']); ?>" 
                                       target="_blank" 
                                       title="点击访问：<?php echo htmlspecialchars($resource['url']); ?>">
                                        <?php echo htmlspecialchars($resource['url']); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php if (!empty($resource['password'])): ?>
                                    <code class="small" style="font-size: 0.8em;"><?php echo htmlspecialchars($resource['password']); ?></code>
                                    <?php else: ?>
                                    <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted" 
                                           title="<?php echo htmlspecialchars($resource['source']); ?>">
                                        <?php echo htmlspecialchars($resource['source']); ?>
                                    </small>
                                </td>
                                <td class="time-cell">
                                    <small class="text-muted" style="font-size: 0.8em;">
                                        <?php 
                                        if (!empty($resource['datetime']) && $resource['datetime'] !== '0001-01-01T00:00:00Z') {
                                            $dateTime = new DateTime($resource['datetime']);
                                            echo '<span style="display: block;">' . $dateTime->format('m-d') . '</span>';
                                            echo '<span style="display: block;">' . $dateTime->format('H:i') . '</span>';
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </small>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary import-btn" 
                                            data-index="<?php echo $index; ?>" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#importModal"
                                            title="导入此资源">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <input type="hidden" name="import_resource" value="1">
                <input type="hidden" name="category" id="selectedCategory" value="0">
                <input type="hidden" name="keywords" value="<?php echo htmlspecialchars($keywords); ?>">
                <?php foreach ($cloud_types as $type): ?>
                <input type="hidden" name="cloud_types[]" value="<?php echo htmlspecialchars($type); ?>">
                <?php endforeach; ?>
                <?php if ($refresh): ?>
                <input type="hidden" name="refresh" value="1">
                <?php endif; ?>
            </form>
        </div>
    </div>
    
    <!-- 单个资源导入模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModalLabel">选择导入分类</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="import_category_modal" class="form-label">选择分类</label>
                        <select class="form-select" id="import_category_modal">
                            <option value="0">-- 请选择分类 --</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <p id="resourceTitle" class="mt-3 fw-bold"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmImport">确认导入</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 批量导入模态框 -->
    <div class="modal fade" id="batchImportModal" tabindex="-1" aria-labelledby="batchImportModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchImportModalLabel">批量导入资源</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="batch_import_category" class="form-label">选择导入分类</label>
                        <select class="form-select" id="batch_import_category">
                            <option value="0">-- 请选择分类 --</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <p class="mt-3">已选择 <span id="selectedCount">0</span> 个资源</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmBatchImport">确认导入</button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (empty($keywords) || (isset($result) && !$result['success'])): ?>
    <div class="card">
        <div class="card-body text-center py-5">
            <div class="mb-4">
                <i class="fas fa-search fa-3x text-muted"></i>
            </div>
            <h5 class="text-muted mb-3">开始您的PanSeeker搜索之旅</h5>
            <p class="text-muted mb-4">
                输入关键词搜索全网网盘资源，支持多种网盘类型筛选
            </p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="row text-start">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-2"><i class="fas fa-star text-warning"></i> 主要特性</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success"></i> 支持13种网盘类型</li>
                                <li><i class="fas fa-check text-success"></i> 实时搜索最新资源</li>
                                <li><i class="fas fa-check text-success"></i> 智能结果排序</li>
                                <li><i class="fas fa-check text-success"></i> 批量导入功能</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-2"><i class="fas fa-lightbulb text-info"></i> 使用建议</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-tip"></i> 使用具体的资源名称搜索</li>
                                <li><i class="fas fa-tip"></i> 选择特定网盘类型提高精准度</li>
                                <li><i class="fas fa-tip"></i> 可使用快速筛选按钮</li>
                                <li><i class="fas fa-tip"></i> 支持强制刷新获取最新数据</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
.btn-group-custom {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.btn-group-custom .btn {
    margin: 0;
}

.form-check-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
}

.resource-row.filtered-out {
    display: none;
}

.badge {
    font-size: 0.75em;
}

/* 表格优化样式 */
.table-sm th,
.table-sm td {
    padding: 0.4rem 0.3rem;
    vertical-align: middle;
}

/* 固定表格布局，防止内容撑开 */
.table {
    table-layout: fixed !important;
    width: 100% !important;
}

.table th {
    font-weight: 600;
    font-size: 0.9em;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
    overflow: hidden !important;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table td {
    font-size: 0.9em;
    border-top: 1px solid #dee2e6;
    overflow: hidden !important;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 0; /* 关键：强制列宽度不超过表头设定 */
}

/* 强制内容截断 */
.table td > * {
    overflow: hidden !important;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
}

/* 文本截断和悬停效果 */
.text-truncate-hover {
    cursor: help;
    transition: all 0.2s ease;
}

.text-truncate-hover:hover {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 0.1rem 0.3rem;
    margin: -0.1rem -0.3rem;
}

/* 网盘类型徽章优化 */
.badge {
    font-size: 0.7em;
    padding: 0.3em 0.5em;
    font-weight: 500;
}

/* 按钮优化 */
.btn-sm {
    padding: 0.25rem 0.4rem;
    font-size: 0.8rem;
    border-radius: 0.2rem;
}

/* 复选框对齐 */
.form-check-input {
    margin-top: 0.1em;
}

/* 链接样式优化 */
a.text-truncate {
    text-decoration: none;
    color: #0d6efd;
}

a.text-truncate:hover {
    color: #0a58ca;
    text-decoration: underline;
}

/* 时间列的换行显示 */
.time-cell {
    line-height: 1.2;
    white-space: nowrap;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 表单验证
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // 网盘类型快速选择功能
    window.selectCloudTypes = function(types) {
        // 清除所有选择
        document.querySelectorAll('input[name="cloud_types[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });
        // 选择指定类型
        types.forEach(type => {
            const checkbox = document.getElementById('cloud_type_' + type);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    };
    
    window.selectAllCloudTypes = function() {
        document.querySelectorAll('input[name="cloud_types[]"]').forEach(checkbox => {
            checkbox.checked = true;
        });
    };
    
    window.clearCloudTypes = function() {
        document.querySelectorAll('input[name="cloud_types[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });
    };
    
    // 结果筛选功能
    document.getElementById('filterResultsBtn')?.addEventListener('click', function() {
        const filterDiv = document.getElementById('resultFilter');
        if (filterDiv.style.display === 'none') {
            filterDiv.style.display = 'block';
            this.innerHTML = '<i class="fas fa-times"></i> 隐藏筛选';
        } else {
            filterDiv.style.display = 'none';
            this.innerHTML = '<i class="fas fa-filter"></i> 筛选结果';
            clearFilter();
        }
    });
    
    window.applyFilter = function() {
        const typeFilter = document.getElementById('filterType').value;
        const titleFilter = document.getElementById('filterTitle').value.toLowerCase();
        const sourceFilter = document.getElementById('filterSource').value;
        
        document.querySelectorAll('.resource-row').forEach(row => {
            let show = true;
            
            if (typeFilter && row.dataset.type !== typeFilter) {
                show = false;
            }
            
            if (titleFilter && !row.dataset.title.toLowerCase().includes(titleFilter)) {
                show = false;
            }
            
            if (sourceFilter && !row.dataset.source.startsWith(sourceFilter)) {
                show = false;
            }
            
            if (show) {
                row.classList.remove('filtered-out');
            } else {
                row.classList.add('filtered-out');
            }
        });
        
        updateSelectedCount();
    };
    
    window.clearFilter = function() {
        document.getElementById('filterType').value = '';
        document.getElementById('filterTitle').value = '';
        document.getElementById('filterSource').value = '';
        
        document.querySelectorAll('.resource-row').forEach(row => {
            row.classList.remove('filtered-out');
        });
        
        updateSelectedCount();
    };
    
    // 全选功能
    document.getElementById('selectAll')?.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.resource-checkbox');
        checkboxes.forEach(checkbox => {
            const row = checkbox.closest('.resource-row');
            if (!row.classList.contains('filtered-out')) {
                checkbox.checked = this.checked;
            }
        });
        updateSelectedCount();
    });
    
    document.getElementById('selectAllBtn')?.addEventListener('click', function() {
        const selectAllCheckbox = document.getElementById('selectAll');
        selectAllCheckbox.checked = !selectAllCheckbox.checked;
        selectAllCheckbox.dispatchEvent(new Event('change'));
    });
    
    // 更新选择计数
    function updateSelectedCount() {
        const checkedBoxes = document.querySelectorAll('.resource-checkbox:checked');
        const visibleCheckedBoxes = Array.from(checkedBoxes).filter(checkbox => {
            const row = checkbox.closest('.resource-row');
            return !row.classList.contains('filtered-out');
        });
        
        document.getElementById('selectedCount').textContent = visibleCheckedBoxes.length;
        
        // 更新全选状态
        const allVisible = document.querySelectorAll('.resource-row:not(.filtered-out) .resource-checkbox');
        const allVisibleChecked = Array.from(allVisible).every(checkbox => checkbox.checked);
        const someVisibleChecked = Array.from(allVisible).some(checkbox => checkbox.checked);
        
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = allVisibleChecked && allVisible.length > 0;
            selectAllCheckbox.indeterminate = someVisibleChecked && !allVisibleChecked;
        }
    }
    
    // 监听复选框变化
    document.querySelectorAll('.resource-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
    
    // 单个导入功能
    document.querySelectorAll('.import-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const index = this.dataset.index;
            const resourceData = JSON.parse(atob(document.querySelector(`input[name="resource_data_${index}"]`).value));
            document.getElementById('resourceTitle').textContent = `资源名称: ${resourceData.title}`;
            
            document.getElementById('confirmImport').onclick = function() {
                const category = document.getElementById('import_category_modal').value;
                if (category === '0') {
                    alert('请选择分类');
                    return;
                }
                
                // 创建表单并提交
                const form = document.createElement('form');
                form.method = 'post';
                form.innerHTML = `
                    <input type="hidden" name="import_resource" value="1">
                    <input type="hidden" name="category" value="${category}">
                    <input type="hidden" name="selected_resources[]" value="${index}">
                    <input type="hidden" name="resource_data_${index}" value="${document.querySelector(`input[name="resource_data_${index}"]`).value}">
                    <input type="hidden" name="keywords" value="<?php echo htmlspecialchars($keywords); ?>">
                    <?php foreach ($cloud_types as $type): ?>
                    <input type="hidden" name="cloud_types[]" value="<?php echo htmlspecialchars($type); ?>">
                    <?php endforeach; ?>
                    <?php if ($refresh): ?>
                    <input type="hidden" name="refresh" value="1">
                    <?php endif; ?>
                `;
                document.body.appendChild(form);
                form.submit();
            };
        });
    });
    
    // 批量导入功能
    document.getElementById('confirmBatchImport')?.addEventListener('click', function() {
        const category = document.getElementById('batch_import_category').value;
        if (category === '0') {
            alert('请选择分类');
            return;
        }
        
        const checkedBoxes = document.querySelectorAll('.resource-checkbox:checked');
        const visibleCheckedBoxes = Array.from(checkedBoxes).filter(checkbox => {
            const row = checkbox.closest('.resource-row');
            return !row.classList.contains('filtered-out');
        });
        
        if (visibleCheckedBoxes.length === 0) {
            alert('请选择要导入的资源');
            return;
        }
        
        document.getElementById('selectedCategory').value = category;
        document.getElementById('importForm').submit();
    });
    
    // 初始化选择计数
    updateSelectedCount();
});
</script>