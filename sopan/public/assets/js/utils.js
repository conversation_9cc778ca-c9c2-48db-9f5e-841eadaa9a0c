/**
 * SOPAN 前端通用工具类
 * 提供错误处理、消息提示、网络请求等通用功能
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class Utils {
    
    /**
     * 显示消息提示
     */
    static showMessage(message, type = 'info', duration = 3000) {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `sopan-message sopan-message--${type}`;
        messageEl.innerHTML = `
            <div class="sopan-message__content">
                <i class="sopan-message__icon ${this.getMessageIcon(type)}"></i>
                <span class="sopan-message__text">${message}</span>
                <button class="sopan-message__close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        // 添加样式
        if (!document.querySelector('#sopan-message-styles')) {
            this.addMessageStyles();
        }
        
        // 添加到页面
        document.body.appendChild(messageEl);
        
        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.classList.add('sopan-message--fade-out');
                    setTimeout(() => {
                        messageEl.remove();
                    }, 300);
                }
            }, duration);
        }
        
        return messageEl;
    }
    
    /**
     * 获取消息图标
     */
    static getMessageIcon(type) {
        const icons = {
            'success': '✓',
            'error': '✕',
            'warning': '⚠',
            'info': 'ℹ'
        };
        return icons[type] || icons.info;
    }
    
    /**
     * 添加消息样式
     */
    static addMessageStyles() {
        const style = document.createElement('style');
        style.id = 'sopan-message-styles';
        style.textContent = `
            .sopan-message {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                max-width: 500px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transform: translateX(100%);
                opacity: 0;
                animation: sopan-message-slide-in 0.3s ease-out forwards;
            }
            
            .sopan-message--success { border-left: 4px solid #10b981; }
            .sopan-message--error { border-left: 4px solid #ef4444; }
            .sopan-message--warning { border-left: 4px solid #f59e0b; }
            .sopan-message--info { border-left: 4px solid #3b82f6; }
            
            .sopan-message__content {
                display: flex;
                align-items: center;
                padding: 16px 20px;
            }
            
            .sopan-message__icon {
                margin-right: 12px;
                font-size: 18px;
                font-weight: bold;
            }
            
            .sopan-message--success .sopan-message__icon { color: #10b981; }
            .sopan-message--error .sopan-message__icon { color: #ef4444; }
            .sopan-message--warning .sopan-message__icon { color: #f59e0b; }
            .sopan-message--info .sopan-message__icon { color: #3b82f6; }
            
            .sopan-message__text {
                flex: 1;
                color: #374151;
                line-height: 1.5;
            }
            
            .sopan-message__close {
                margin-left: 12px;
                background: none;
                border: none;
                color: #9ca3af;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .sopan-message__close:hover {
                color: #374151;
            }
            
            .sopan-message--fade-out {
                animation: sopan-message-slide-out 0.3s ease-in forwards;
            }
            
            @keyframes sopan-message-slide-in {
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            @keyframes sopan-message-slide-out {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
            
            /* 移动端适配 */
            @media (max-width: 640px) {
                .sopan-message {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                    min-width: auto;
                    max-width: none;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 复制文本到剪贴板
     */
    static async copyToClipboard(text) {
        try {
            if (navigator.clipboard) {
                await navigator.clipboard.writeText(text);
                this.showMessage('已复制到剪贴板', 'success');
            } else {
                // 降级方案
                this.fallbackCopyTextToClipboard(text);
            }
        } catch (err) {
            console.error('复制失败:', err);
            this.showMessage('复制失败，请手动复制', 'error');
        }
    }
    
    /**
     * 降级复制方案
     */
    static fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.width = '2em';
        textArea.style.height = '2em';
        textArea.style.padding = '0';
        textArea.style.border = 'none';
        textArea.style.outline = 'none';
        textArea.style.boxShadow = 'none';
        textArea.style.background = 'transparent';
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            this.showMessage('已复制到剪贴板', 'success');
        } catch (err) {
            console.error('降级复制失败:', err);
            this.showMessage('复制失败，请手动复制', 'error');
        }
        
        document.body.removeChild(textArea);
    }
    
    /**
     * 格式化文件大小
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 格式化时间
     */
    static formatTime(timestamp) {
        const date = new Date(timestamp * 1000);
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        
        // 1分钟内
        if (diff < 60000) {
            return '刚刚';
        }
        
        // 1小时内
        if (diff < 3600000) {
            return Math.floor(diff / 60000) + '分钟前';
        }
        
        // 24小时内
        if (diff < 86400000) {
            return Math.floor(diff / 3600000) + '小时前';
        }
        
        // 超过24小时显示日期
        return date.toLocaleDateString('zh-CN');
    }
    
    /**
     * 防抖函数
     */
    static debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    }
    
    /**
     * 节流函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

/**
 * 错误处理器
 */
class ErrorHandler {
    
    /**
     * 初始化全局错误处理
     */
    static init() {
        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise错误:', event.reason);
            this.handleError(event.reason, 'Promise Error');
            event.preventDefault();
        });
        
        // 捕获JavaScript错误
        window.addEventListener('error', (event) => {
            console.error('JavaScript错误:', event.error);
            this.handleError(event.error, 'JavaScript Error');
        });
        
        // Vue错误处理（如果使用Vue）
        if (typeof Vue !== 'undefined') {
            const originalErrorHandler = Vue.config.errorHandler;
            Vue.config.errorHandler = (error, vm, info) => {
                console.error('Vue错误:', error, info);
                this.handleError(error, 'Vue Error', { info });
                
                if (originalErrorHandler) {
                    originalErrorHandler.call(this, error, vm, info);
                }
            };
        }
        
        // 网络请求错误处理
        this.setupAxiosInterceptors();
    }
    
    /**
     * 处理错误
     */
    static handleError(error, type = 'Unknown Error', context = {}) {
        const errorInfo = {
            type,
            message: error?.message || error || '未知错误',
            stack: error?.stack || '',
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            ...context
        };
        
        // 发送错误报告到后端
        this.reportError(errorInfo);
        
        // 显示用户友好的错误提示
        this.showUserError(errorInfo);
    }
    
    /**
     * 显示用户友好的错误
     */
    static showUserError(errorInfo) {
        let message = '系统出现异常，请稍后重试';
        
        // 根据错误类型显示不同的提示
        if (errorInfo.type === 'Network Error') {
            message = '网络连接失败，请检查网络后重试';
        } else if (errorInfo.message.includes('timeout')) {
            message = '请求超时，请稍后重试';
        } else if (errorInfo.message.includes('404')) {
            message = '请求的资源不存在';
        } else if (errorInfo.message.includes('500')) {
            message = '服务器内部错误，请稍后重试';
        }
        
        Utils.showMessage(message, 'error', 5000);
    }
    
    /**
     * 报告错误到后端
     */
    static reportError(errorInfo) {
        try {
            // 避免在错误报告过程中再次出错
            fetch('/api/error/report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(errorInfo)
            }).catch(err => {
                console.error('错误报告发送失败:', err);
            });
        } catch (err) {
            console.error('错误报告处理失败:', err);
        }
    }
    
    /**
     * 设置Axios拦截器
     */
    static setupAxiosInterceptors() {
        if (typeof axios === 'undefined') {
            return;
        }
        
        // 请求拦截器
        axios.interceptors.request.use(
            (config) => {
                // 添加请求ID用于跟踪
                config.headers['X-Request-ID'] = this.generateRequestId();
                return config;
            },
            (error) => {
                this.handleError(error, 'Request Error');
                return Promise.reject(error);
            }
        );
        
        // 响应拦截器
        axios.interceptors.response.use(
            (response) => {
                return response;
            },
            (error) => {
                if (error.response) {
                    // 服务器响应了错误状态码
                    this.handleApiError(error.response);
                } else if (error.request) {
                    // 请求已发出但没有收到响应
                    this.handleError(error, 'Network Error');
                } else {
                    // 其他错误
                    this.handleError(error, 'Request Setup Error');
                }
                return Promise.reject(error);
            }
        );
    }
    
    /**
     * 处理API错误
     */
    static handleApiError(response) {
        const { status, data } = response;
        
        let message = '请求失败';
        
        if (data && data.message) {
            message = data.message;
        } else {
            switch (status) {
                case 400:
                    message = '请求参数错误';
                    break;
                case 401:
                    message = '未授权，请重新登录';
                    break;
                case 403:
                    message = '禁止访问';
                    break;
                case 404:
                    message = '请求的资源不存在';
                    break;
                case 429:
                    message = '请求过于频繁，请稍后重试';
                    break;
                case 500:
                    message = '服务器内部错误';
                    break;
                case 502:
                    message = '网关错误';
                    break;
                case 503:
                    message = '服务暂时不可用';
                    break;
                default:
                    message = `请求失败 (${status})`;
            }
        }
        
        Utils.showMessage(message, 'error');
        
        // 报告API错误
        this.reportError({
            type: 'API Error',
            status,
            message,
            url: response.config?.url || '',
            method: response.config?.method || ''
        });
    }
    
    /**
     * 生成请求ID
     */
    static generateRequestId() {
        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}

/**
 * 连接管理器
 */
class ConnectionManager {
    static status = 'online';
    static listeners = [];
    
    /**
     * 初始化连接监控
     */
    static init() {
        // 监听网络状态变化
        window.addEventListener('online', () => {
            this.setStatus('online');
            Utils.showMessage('网络连接已恢复', 'success');
        });
        
        window.addEventListener('offline', () => {
            this.setStatus('offline');
            Utils.showMessage('网络连接已断开', 'warning', 0);
        });
        
        // 定期检查连接状态
        setInterval(() => {
            this.checkConnection();
        }, 30000); // 30秒检查一次
    }
    
    /**
     * 设置连接状态
     */
    static setStatus(status) {
        const oldStatus = this.status;
        this.status = status;
        
        if (oldStatus !== status) {
            this.notifyListeners(status);
        }
    }
    
    /**
     * 添加状态监听器
     */
    static addListener(callback) {
        this.listeners.push(callback);
    }
    
    /**
     * 通知监听器
     */
    static notifyListeners(status) {
        this.listeners.forEach(callback => {
            try {
                callback(status);
            } catch (err) {
                console.error('连接状态监听器错误:', err);
            }
        });
    }
    
    /**
     * 检查连接状态
     */
    static async checkConnection() {
        try {
            const response = await fetch('/api/health', {
                method: 'GET',
                cache: 'no-cache',
                timeout: 5000
            });
            
            if (response.ok) {
                this.setStatus('online');
            } else {
                this.setStatus('error');
            }
        } catch (err) {
            this.setStatus('offline');
        }
    }
}

// 初始化错误处理和连接监控
document.addEventListener('DOMContentLoaded', () => {
    ErrorHandler.init();
    ConnectionManager.init();
});

// 导出到全局
window.Utils = Utils;
window.ErrorHandler = ErrorHandler;
window.ConnectionManager = ConnectionManager;