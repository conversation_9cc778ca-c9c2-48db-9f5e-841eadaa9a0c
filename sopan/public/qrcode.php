<?php
/**
 * SOPAN QR码生成API
 * 直接使用phpqrcode库动态生成二维码图片
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

// 引入phpqrcode库
require_once dirname(__DIR__, 2) . '/extend/phpqrcode/qrlib.php';

// 设置错误报告级别
error_reporting(E_ERROR | E_WARNING | E_PARSE);

// 获取参数
$data = $_GET['data'] ?? $_POST['data'] ?? '';
$size = (int)($_GET['size'] ?? $_POST['size'] ?? 6);
$level = $_GET['level'] ?? $_POST['level'] ?? 'M';
$margin = (int)($_GET['margin'] ?? $_POST['margin'] ?? 2);

// 验证参数
if (empty($data)) {
    http_response_code(400);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => '缺少data参数'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证大小参数
if ($size < 1 || $size > 10) {
    $size = 6;
}

// 验证纠错级别
if (!in_array($level, ['L', 'M', 'Q', 'H'])) {
    $level = 'M';
}

// 验证边距
if ($margin < 0 || $margin > 10) {
    $margin = 2;
}

try {
    // 设置内容类型为PNG图片
    header('Content-Type: image/png');
    header('Cache-Control: public, max-age=3600'); // 缓存1小时
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
    
    // 生成并输出二维码
    QRcode::png($data, false, $level, $size, $margin);
    
} catch (Exception $e) {
    // 发生错误时输出错误信息
    http_response_code(500);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => '二维码生成失败：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>