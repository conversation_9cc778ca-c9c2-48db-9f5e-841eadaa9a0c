<?php
/**
 * SOPAN 前台入口文件
 * 所有前台页面请求的统一入口点
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

// 设置错误报告级别
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
ini_set('display_errors', 0);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 设置内存限制
ini_set('memory_limit', '128M');

try {
    // 加载引导文件
    require_once __DIR__ . '/bootstrap.php';
    
    // 获取请求路径（支持ThinkPHP风格的s参数）
    $requestUri = '';
    
    // 优先从 s 参数获取路径（Nginx重写后的参数）
    if (isset($_GET['s'])) {
        $requestUri = $_GET['s'];
    } else {
        // 备用方式：从 REQUEST_URI 获取
        $requestUri = $_SERVER['REQUEST_URI'] ?? '/';
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        
        // 移除脚本名称获取实际路径
        $basePath = dirname($scriptName);
        if ($basePath !== '/') {
            $requestUri = substr($requestUri, strlen($basePath));
        }
        
        // 移除查询参数
        $requestUri = strtok($requestUri, '?');
    }
    
    // 清理路径
    $requestUri = '/' . trim($requestUri, '/');
    if ($requestUri === '/') {
        $requestUri = '/';
    }
    
    // 特殊路径处理
    
    // 1. 安装程序特殊处理
    if ($requestUri === '/install') {
        // 检查是否已安装
        if (file_exists(__DIR__ . '/config/installed.lock')) {
            header('Location: /');
            exit('系统已安装');
        }
        
        // 检查install.php是否存在
        if (file_exists(__DIR__ . '/install.php')) {
            require_once __DIR__ . '/install.php';
            exit;
        } else {
            http_response_code(404);
            exit('安装程序不存在');
        }
    }
    
    // 2. 后台管理特殊处理（支持动态路径）
    // 加载配置类获取后台路径
    require_once __DIR__ . '/core/Config.php';
    $adminPath = Config::getAdminPath();
    
    if (preg_match('#^/' . preg_quote($adminPath, '#') . '(/.*)?$#', $requestUri)) {
        // 后台管理请求，加载后台路由
        require_once __DIR__ . '/routes/web.php';
        exit;
    }
    // 3. API请求特殊处理
    if (preg_match('#^/api/#', $requestUri)) {
        // 检查api.php是否存在
        if (file_exists(__DIR__ . '/api.php')) {
            // 设置环境变量供 api.php 使用
            $_SERVER['API_REQUEST_URI'] = $requestUri;
            require_once __DIR__ . '/api.php';
            exit;
        } else {
            http_response_code(404);
            exit('API接口不存在');
        }
    }
    
    // 4. QR码特殊处理
    if ($requestUri === '/qrcode') {
        // 检查qrcode.php是否存在
        if (file_exists(__DIR__ . '/public/qrcode.php')) {
            require_once __DIR__ . '/public/qrcode.php';
            exit;
        } else {
            http_response_code(404);
            exit('QR码生成器不存在');
        }
    }
    
    // 静态文件处理（如果需要）
    if (preg_match('#\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$#i', $requestUri)) {
        // 返回404，让web服务器处理静态文件
        http_response_code(404);
        exit;
    }
    
    // 记录访问日志
    logWebAccess($requestUri);
    
    // 加载前台路由
    require_once __DIR__ . '/routes/web.php';
    
} catch (Exception $e) {
    // 捕获所有异常
    error_log('Frontend Fatal Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
    
    // 显示友好的错误页面
    http_response_code(500);
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>系统错误 - SOPAN搜索</title>
        <script src="/sopan/public/assets/js/tailwind.min.js"></script>
    </head>
    <body class="bg-gray-50 min-h-screen flex items-center justify-center">
        <div class="text-center">
            <h1 class="text-6xl font-bold text-gray-300 mb-4">500</h1>
            <h2 class="text-2xl font-bold text-gray-900 mb-4">系统错误</h2>
            <p class="text-gray-600 mb-8">抱歉，网站暂时无法访问，请稍后重试</p>
            <a href="/" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                返回首页
            </a>
        </div>
    </body>
    </html>
    <?php
    
} catch (Error $e) {
    // 捕获PHP7+的Error
    error_log('Frontend Fatal Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
    
    http_response_code(500);
    echo '系统错误，请稍后重试';
}

/**
 * 记录Web访问日志
 * 
 * @param string $requestUri 请求URI
 */
function logWebAccess($requestUri) {
    try {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'GET',
            'uri' => $requestUri,
            'ip' => getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'referer' => $_SERVER['HTTP_REFERER'] ?? ''
        ];
        
        $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        
        // 写入日志文件
        $logDir = __DIR__ . '/storage/logs';
        if (!is_dir($logDir)) {
            @mkdir($logDir, 0755, true);
        }
        
        $logFile = $logDir . '/web_access_' . date('Y-m-d') . '.log';
        @file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
        
    } catch (Exception $e) {
        // 忽略日志记录错误
    }
}

/**
 * 获取客户端IP
 * 
 * @return string
 */
function getClientIp() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            return trim($ips[0]);
        }
    }
    
    return '127.0.0.1';
}