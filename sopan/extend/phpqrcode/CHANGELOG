* 1.0.0 build ********** 

  - first public release
  - help in readme, install
  - cleanup ans separation of QRtools and QRspec
  - now TCPDF binding requires minimal changes in TCPDF, having most of job
    done in QRtools tcpdfBarcodeArray
  - nicer QRtools::timeBenchmark output
  - license and copyright notices in files
  - indent cleanup - from tab to 4spc, keep it that way please :)
  - sf project, repository, wiki
  - simple code generator in index.php
  
* 1.1.0 build **********

  - added merge tool wich generate merged version of code
    located in phpqrcode.php
  - splited qrconst.php from qrlib.php
  
* 1.1.1 build **********

  - patch by <PERSON> allowing saving PNG and displaying it at the same time
  - added version info in VERSION file
  - modified merge tool to include version info into generated file
  - fixed e-mail in almost all head comments
  
* 1.1.2 build **********

  - full integration with TCPDF thanks to <PERSON>, it's author
  - fixed bug with alphanumeric encoding detection
  
* 1.1.3 build **********

  - short opening tags replaced with standard ones
  
* 1.1.4 build **********

  - added missing static keyword QRinput::check (found by <PERSON>, Onjax LLC)
