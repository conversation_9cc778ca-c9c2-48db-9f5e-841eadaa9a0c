<?php
/**
 * SOPAN 项目引导文件
 * 自动加载核心类库，初始化应用
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

// 设置错误报告级别
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义项目根目录
define('ROOT_PATH', __DIR__ . '/');
define('CORE_PATH', ROOT_PATH . 'core/');
define('APP_PATH', ROOT_PATH . 'app/');
define('CONFIG_PATH', ROOT_PATH . 'config/');
define('STORAGE_PATH', ROOT_PATH . 'storage/');
define('PUBLIC_PATH', ROOT_PATH . 'public/');

/**
 * 自动加载类文件
 * 
 * @param string $className 类名
 */
function autoload($className) {
    $paths = [
        CORE_PATH,
        APP_PATH . 'controllers/',
        APP_PATH . 'models/',
        APP_PATH . 'services/',
        ROOT_PATH . 'extend/'
    ];
    
    foreach ($paths as $path) {
        $file = $path . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
}

// 注册自动加载函数
spl_autoload_register('autoload');

/**
 * 异常处理器
 * 
 * @param Exception $exception 异常对象
 */
function exceptionHandler($exception) {
    // 记录错误日志
    error_log('Exception: ' . $exception->getMessage() . ' in ' . $exception->getFile() . ' on line ' . $exception->getLine());
    
    // 根据环境显示错误信息
    if (Config::get('app_debug', true)) {
        echo '<h1>Exception</h1>';
        echo '<p><strong>Message:</strong> ' . $exception->getMessage() . '</p>';
        echo '<p><strong>File:</strong> ' . $exception->getFile() . '</p>';
        echo '<p><strong>Line:</strong> ' . $exception->getLine() . '</p>';
        echo '<pre>' . $exception->getTraceAsString() . '</pre>';
    } else {
        Router::handle500($exception);
    }
}

/**
 * 错误处理器
 * 
 * @param int $errno 错误级别
 * @param string $errstr 错误信息
 * @param string $errfile 错误文件
 * @param int $errline 错误行号
 */
function errorHandler($errno, $errstr, $errfile, $errline) {
    if (!(error_reporting() & $errno)) {
        return false;
    }
    
    $error = "Error: [$errno] $errstr in $errfile on line $errline";
    error_log($error);
    
    // 严重错误抛出异常
    if ($errno === E_ERROR || $errno === E_CORE_ERROR || $errno === E_COMPILE_ERROR || $errno === E_USER_ERROR) {
        throw new ErrorException($errstr, 0, $errno, $errfile, $errline);
    }
    
    return true;
}

/**
 * 致命错误处理器
 */
function fatalErrorHandler() {
    $error = error_get_last();
    if ($error !== null && $error['type'] === E_ERROR) {
        $exception = new ErrorException($error['message'], 0, $error['type'], $error['file'], $error['line']);
        exceptionHandler($exception);
    }
}

// 注册错误处理器
set_exception_handler('exceptionHandler');
set_error_handler('errorHandler');
register_shutdown_function('fatalErrorHandler');

/**
 * 辅助函数：获取配置值
 * 
 * @param string $key 配置键名
 * @param mixed $default 默认值
 * @return mixed
 */
function config($key, $default = null) {
    return Config::get($key, $default);
}

/**
 * 辅助函数：生成URL
 * 
 * @param string $path 路径
 * @param array $params 参数
 * @return string
 */
function url($path, $params = []) {
    return Router::url($path, $params);
}

/**
 * 辅助函数：重定向
 * 
 * @param string $url URL
 * @param int $code 状态码
 */
function redirect($url, $code = 302) {
    Router::redirect($url, $code);
}

/**
 * 辅助函数：获取请求参数
 * 
 * @param string $key 参数名
 * @param mixed $default 默认值
 * @return mixed
 */
function request($key = null, $default = null) {
    if ($key === null) {
        return array_merge($_GET, $_POST);
    }
    
    return $_REQUEST[$key] ?? $default;
}

/**
 * 辅助函数：JSON响应
 * 
 * @param mixed $data 数据
 * @param int $code 状态码
 * @param string $message 消息
 */
function json_response($data = null, $code = 200, $message = 'success') {
    View::json($data, $code, $message);
}

/**
 * 辅助函数：记录日志
 * 
 * @param string $message 日志信息
 * @param string $level 日志级别
 * @param array $context 上下文
 */
function write_log($message, $level = 'info', $context = []) {
    $logFile = STORAGE_PATH . 'logs/' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
    $logMessage = "[{$timestamp}] {$level}: {$message}{$contextStr}" . PHP_EOL;
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * 应用启动函数
 */
function start_app() {
    try {
        // 启动会话
        if (!session_id()) {
            session_start();
        }
        
        // 加载配置
        Config::load();
        
        write_log('Application started', 'info');
        
    } catch (Exception $e) {
        write_log('Application start failed: ' . $e->getMessage(), 'error');
        throw $e;
    }
}

// 自动启动应用（如果不是命令行模式）
if (php_sapi_name() !== 'cli') {
    start_app();
}