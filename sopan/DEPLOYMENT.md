# SOPAN 网盘搜索项目部署指南

## 📋 项目概述

SOPAN 是一个基于原生PHP开发的专业网盘搜索与转存系统，集成了多种网盘平台的搜索和转存功能。

### 🎯 主要特性

- **原生PHP架构**：无需复杂框架，轻量级高性能
- **Vue CDN集成**：前端使用Vue.js CDN，无需Node.js构建环境  
- **多平台支持**：支持夸克网盘、百度网盘、阿里云盘等
- **实时搜索**：SSE流式搜索，实时推送结果
- **智能转存**：一键转存到目标网盘账户
- **完善监控**：错误处理、日志记录、系统监控
- **QR码生成**：动态生成分享链接二维码
- **后台管理**：完整的管理后台系统

## 🔧 环境要求

### 基础环境
- **PHP**: 7.4+ (推荐 8.0+)
- **MySQL**: 5.7+ / MariaDB 10.3+
- **Web服务器**: Nginx / Apache
- **操作系统**: Linux (推荐 CentOS 7+, Ubuntu 18.04+)

### PHP扩展
```bash
# 必需扩展
php-mysql
php-mysqli  
php-pdo
php-json
php-mbstring
php-curl
php-openssl
php-gd
php-zip

# 可选扩展（提升性能）
php-opcache
php-redis
```

## 📁 目录结构

```
sopan/
├── app/                    # 应用核心
│   ├── controllers/        # 控制器
│   ├── models/            # 数据模型
│   └── services/          # 业务服务
├── config/                # 配置文件
├── core/                  # 核心类库
├── database/             # 数据库文件
├── extend/               # 第三方扩展
├── logs/                 # 日志文件
├── public/               # 公共资源
├── routes/               # 路由配置  
├── storage/              # 存储目录
├── tests/                # 测试文件
├── views/                # 视图模板
├── api.php               # API入口
├── bootstrap.php         # 启动文件
└── index.php             # 前台入口
```

## 🚀 安装部署

### 1. 下载项目

```bash
# 克隆项目
git clone https://github.com/your-repo/sopan.git
cd sopan

# 或者直接下载压缩包解压
```

### 2. 配置Web服务器

#### Nginx 配置示例

**简洁配置方案 - 适合标准Web安装**

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 设置网站根目录
    root /var/www/sopan;
    
    # 默认首页文件
    index index.php index.html;
    
    # 字符集设置
    charset utf-8;
    
    # 访问日志
    access_log /var/log/nginx/sopan_access.log;
    error_log /var/log/nginx/sopan_error.log;
    
    # 简洁的重写规则（类似ThinkPHP）
    location / {
        if (!-e $request_filename){
            rewrite  ^(.*)$  /index.php?s=$1  last;   break;
        }
    }
    
    # PHP文件处理
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;  # 根据你的PHP版本调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 支持长时间执行（用于搜索功能）
        fastcgi_read_timeout 300;
    }
    
    # 静态文件处理
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 安全设置 - 禁止访问敏感文件
    location ~ /\.(ht|git|svn) {
        deny all;
    }
    
    location ~* \.(sql|log|md)$ {
        deny all;
    }
}
```

**配置步骤：**

1. **创建配置文件**
```bash
sudo nano /etc/nginx/sites-available/sopan
```

2. **启用站点配置**
```bash
sudo ln -s /etc/nginx/sites-available/sopan /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

3. **设置目录权限**
```bash
sudo chown -R www-data:www-data /var/www/sopan
sudo chmod -R 755 /var/www/sopan
```

4. **访问网站**
```
# 前台首页（搜索界面）
http://your-domain.com/

# 后台管理（默认路径）
http://your-domain.com/admin

# 安装程序（仅在未安装时可访问）
http://your-domain.com/install
```

**后台路径自定义：**
- 安装完成后，可在后台 "系统设置" 中修改后台访问路径
- 路径只能包含字母、数字、下划线和中横线
- 不能与系统保留路径冲突（api, install, qrcode 等）
- 修改后新的后台地址为：`http://your-domain.com/新路径`

**重要说明：**
- `root` 目录设置为 `/var/www/sopan`（项目根目录）
- 使用类似ThinkPHP的简洁重写规则
- **前后台分离架构**：
  - `/` - 前台首页（默认显示搜索界面）
  - `/admin`（可自定义）- 后台管理入口
  - 后台路径可在系统设置中自定义，避免被恶意访问
- 所有请求都由 `index.php` 统一处理：
  - `/install` 请求会被转发到 `install.php`
  - `/api/*` 请求会被转发到 `api.php`
  - `/qrcode` 请求会被转发到 `public/qrcode.php`
  - 后台请求根据配置的路径动态转发到后台路由
  - 其他请求由前台路由处理
- 安装完成后，系统会自动配置数据库和系统参数

### 3. 一键安装（推荐）

**🎯 最简单的安装方式：**

1. **上传项目文件到服务器**
```bash
# 将项目文件上传到 /var/www/sopan 目录
sudo chown -R www-data:www-data /var/www/sopan
sudo chmod -R 755 /var/www/sopan
```

2. **配置Nginx（使用上面的配置）**

3. **访问安装页面**
```
http://your-domain.com/install
```

4. **按照安装向导操作**
- 环境检查（自动检测PHP环境和扩展）
- 网站配置（填写网站名称、搜索API配置）
- 数据库配置（填写数据库连接信息）
- 管理员账户（设置管理员用户名和密码）
- 自动完成安装

**⚠️ 重要提醒：**
- **安装前**：`config/` 目录应该是空的或不存在
- **安装时**：安装程序会自动生成所有必需的配置文件：
  - `config/app.php` - 应用基础配置
  - `config/database.php` - 数据库连接配置
  - `config/search.php` - 搜索功能配置
  - `config/installed.lock` - 安装标记文件
- **安装后**：如需修改配置，请直接编辑 `config/` 目录下的文件
- **重新安装**：删除 `config/installed.lock` 文件后可重新安装  
- 数据库配置（填写数据库连接信息）
- 管理员账户（设置管理员用户名和密码）
- 完成安装（自动创建数据库表和配置文件）

**🔥 就这么简单！系统会自动：**
- 检测服务器环境
- 创建数据库和数据表
- 生成配置文件
- 设置管理员账户
- 配置系统参数

### 4. 手动安装（高级用户）

如果需要自定义配置，可以选择手动安装：
```

#### Apache 配置示例

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/sopan/public
    
    <Directory /var/www/sopan/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        
        # URL重写
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>
    
    # 禁止访问敏感目录
    <DirectoryMatch "/(config|logs|storage|tests)/">
        Require all denied
    </DirectoryMatch>
    
    # 静态资源缓存
    <FilesMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </FilesMatch>
</VirtualHost>
```

### 3. 配置数据库

```bash
# 登录MySQL
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE sopan CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'sopan_user'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON sopan.* TO 'sopan_user'@'localhost';
FLUSH PRIVILEGES;
```

导入数据库结构：
```bash
mysql -u sopan_user -p sopan < database/sopan.sql
```

### 4. 配置应用

复制配置文件：
```bash
cp config/app.example.php config/app.php
```

编辑配置文件 `config/app.php`：
```php
<?php
return [
    'app' => [
        'name' => 'SOPAN网盘搜索',
        'environment' => 'production', // development | production
        'debug' => false, // 生产环境设为 false
        'url' => 'https://your-domain.com',
        'timezone' => 'Asia/Shanghai'
    ],
    
    'database' => [
        'host' => 'localhost',
        'port' => 3306,
        'username' => 'sopan_user',
        'password' => 'your_strong_password',
        'database' => 'sopan',
        'charset' => 'utf8mb4'
    ],
    
    'search' => [
        'panseeker_api_url' => 'http://your-panseeker-api.com',
        'api_key' => 'your_panseeker_api_key',
        'timeout' => 30,
        'max_results' => 100
    ],
    
    'transfer' => [
        'platforms' => [
            'quark' => [
                'username' => 'your_quark_username',
                'password' => 'your_quark_password'
            ],
            'baidu' => [
                'username' => 'your_baidu_username', 
                'password' => 'your_baidu_password'
            ]
        ]
    ]
];
```

### 5. 设置权限

```bash
# 设置目录权限
chown -R www-data:www-data /var/www/sopan
find /var/www/sopan -type d -exec chmod 755 {} \;
find /var/www/sopan -type f -exec chmod 644 {} \;

# 设置可写目录
chmod -R 775 /var/www/sopan/logs
chmod -R 775 /var/www/sopan/storage
chmod -R 775 /var/www/sopan/public/uploads
```

### 6. 创建管理员账户

```bash
php /var/www/sopan/scripts/create_admin.php --username=admin --password=your_admin_password --email=<EMAIL>
```

## 🔒 安全配置

### 1. 环境变量配置

创建 `.env` 文件：
```bash
# 数据库配置
DB_HOST=localhost
DB_USERNAME=sopan_user
DB_PASSWORD=your_strong_password
DB_DATABASE=sopan

# API密钥
PANSEEKER_API_KEY=your_panseeker_api_key
ADMIN_SECRET_KEY=your_admin_secret_key

# 加密密钥
APP_ENCRYPT_KEY=your_32_character_encryption_key
```

### 2. 防火墙配置

```bash
# 仅开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

### 3. SSL配置

使用Let's Encrypt获取免费SSL证书：
```bash
# 安装certbot
apt-get install certbot python3-certbot-nginx

# 获取证书
certbot --nginx -d your-domain.com

# 自动续期
crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控配置

### 1. 日志监控

配置 logrotate：
```bash
# /etc/logrotate.d/sopan
/var/www/sopan/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 2. 性能监控

安装系统监控工具：
```bash
# 安装htop和iotop
apt-get install htop iotop

# 配置PHP-FPM状态页面
echo "pm.status_path = /fpm-status" >> /etc/php/8.0/fpm/pool.d/www.conf
```

### 3. 错误监控

配置错误邮件通知：
```php
// config/app.php
'error_reporting' => [
    'email_notifications' => true,
    'admin_email' => '<EMAIL>',
    'error_threshold' => 10 // 每小时错误数阈值
]
```

## 🔧 维护指南

### 日常维护任务

```bash
# 清理日志文件
php /var/www/sopan/scripts/cleanup_logs.php --days=30

# 优化数据库
php /var/www/sopan/scripts/optimize_database.php

# 检查系统状态
php /var/www/sopan/scripts/health_check.php

# 备份数据库
mysqldump -u sopan_user -p sopan > backup_$(date +%Y%m%d).sql
```

### 升级指南

```bash
# 1. 备份当前版本
cp -r /var/www/sopan /var/www/sopan_backup

# 2. 下载新版本
wget https://github.com/your-repo/sopan/archive/v2.0.0.tar.gz

# 3. 解压并替换文件
tar -xzf v2.0.0.tar.gz

# 4. 运行升级脚本
php /var/www/sopan/scripts/upgrade.php

# 5. 重启服务
systemctl reload nginx
systemctl restart php8.0-fpm
```

## 🛠️ 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 验证用户权限
   - 查看MySQL日志

2. **API请求超时**
   - 检查网络连接
   - 调整超时设置
   - 查看错误日志

3. **转存失败** 
   - 验证网盘账户信息
   - 检查网盘API状态
   - 查看转存日志

4. **前端资源加载失败**
   - 检查CDN可用性
   - 验证静态资源路径
   - 查看网络控制台

### 日志位置

- 应用日志: `/var/www/sopan/logs/`
- Nginx日志: `/var/log/nginx/`
- PHP错误日志: `/var/log/php/`
- MySQL日志: `/var/log/mysql/`

## 📞 支持与帮助

- **项目文档**: [https://docs.sopan.com](https://docs.sopan.com)
- **问题反馈**: [https://github.com/your-repo/sopan/issues](https://github.com/your-repo/sopan/issues)  
- **技术支持**: <EMAIL>

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。