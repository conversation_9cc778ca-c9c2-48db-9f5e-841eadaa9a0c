-- ============================================
-- SOPAN 网盘搜索系统完整数据库初始化脚本
-- 版本: 1.0.0
-- 创建时间: 2024-01-01
-- 说明: 包含SOPAN系统运行所需的全部数据表和初始配置
-- ============================================

SET FOREIGN_KEY_CHECKS = 0;
SET NAMES utf8mb4;

-- ============================================
-- 1. 站点配置表
-- ============================================
CREATE TABLE IF NOT EXISTS `site_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(50) NOT NULL COMMENT '配置键名',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) DEFAULT 'text' COMMENT '配置类型(text,textarea,number,boolean,select)',
  `config_group` varchar(30) DEFAULT 'basic' COMMENT '配置分组',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `is_system` tinyint(1) DEFAULT 0 COMMENT '是否系统配置',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0禁用,1启用)',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ============================================
-- 2. 管理员表
-- ============================================
CREATE TABLE IF NOT EXISTS `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '管理员用户名',
  `password` varchar(255) NOT NULL COMMENT '密码哈希',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `role` varchar(20) DEFAULT 'admin' COMMENT '角色(super_admin,admin)',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0禁用,1启用)',
  `login_times` int(11) DEFAULT 0 COMMENT '登录次数',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- ============================================
-- 3. 搜索日志表
-- ============================================
CREATE TABLE IF NOT EXISTS `search_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `keyword` varchar(200) NOT NULL COMMENT '搜索关键词',
  `user_ip` varchar(45) DEFAULT NULL COMMENT '用户IP',
  `user_agent` text COMMENT '用户代理',
  `search_type` varchar(20) DEFAULT 'all' COMMENT '搜索类型',
  `result_count` int(11) DEFAULT 0 COMMENT '结果数量',
  `response_time` int(11) DEFAULT 0 COMMENT '响应时间(毫秒)',
  `is_success` tinyint(1) DEFAULT 1 COMMENT '是否成功',
  `search_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '搜索时间',
  PRIMARY KEY (`id`),
  KEY `idx_keyword` (`keyword`),
  KEY `idx_search_time` (`search_time`),
  KEY `idx_user_ip` (`user_ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索日志表';

-- ============================================
-- 4. 搜索会话表（异步搜索）
-- ============================================
CREATE TABLE IF NOT EXISTS `search_sessions` (
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `keyword` varchar(255) NOT NULL COMMENT '搜索关键词',
  `type_filter` varchar(20) DEFAULT 'all' COMMENT '类型过滤',
  `status` enum('searching','completed','error','cancelled') DEFAULT 'searching' COMMENT '搜索状态',
  `start_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `total_results` int(11) DEFAULT 0 COMMENT '总结果数',
  `progress` tinyint(3) DEFAULT 0 COMMENT '进度百分比',
  `user_ip` varchar(45) DEFAULT NULL COMMENT '用户IP',
  `user_agent` text COMMENT '用户代理',
  PRIMARY KEY (`session_id`),
  KEY `idx_keyword_time` (`keyword`,`start_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索会话表';

-- ============================================
-- 5. 搜索结果缓存表
-- ============================================
CREATE TABLE IF NOT EXISTS `search_results_cache` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `source` varchar(50) NOT NULL COMMENT '来源',
  `result_data` json NOT NULL COMMENT '结果数据',
  `platform_type` varchar(20) DEFAULT NULL COMMENT '平台类型',
  `match_score` decimal(5,2) DEFAULT NULL COMMENT '匹配分数',
  `create_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expire_time` timestamp DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_platform_type` (`platform_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索结果缓存表';

-- ============================================
-- 6. 转存记录表
-- ============================================
CREATE TABLE IF NOT EXISTS `transfer_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfer_id` varchar(64) NOT NULL COMMENT '转存ID',
  `url` text NOT NULL COMMENT '原始链接',
  `code` varchar(20) DEFAULT NULL COMMENT '提取码',
  `platform` varchar(20) NOT NULL COMMENT '目标平台',
  `title` varchar(500) DEFAULT NULL COMMENT '资源标题',
  `file_size` varchar(20) DEFAULT NULL COMMENT '文件大小',
  `user_ip` varchar(45) DEFAULT NULL COMMENT '用户IP',
  `status` enum('processing','success','failed','error') DEFAULT 'processing' COMMENT '转存状态',
  `message` text COMMENT '状态消息',
  `retry_count` tinyint(3) DEFAULT 0 COMMENT '重试次数',
  `create_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transfer_id` (`transfer_id`),
  KEY `idx_user_ip_time` (`user_ip`,`create_time`),
  KEY `idx_status` (`status`),
  KEY `idx_platform` (`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='转存记录表';

-- ============================================
-- 7. 热门关键词表
-- ============================================
CREATE TABLE IF NOT EXISTS `hot_keywords` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `keyword` varchar(200) NOT NULL COMMENT '关键词',
  `search_count` int(11) DEFAULT 1 COMMENT '搜索次数',
  `last_search_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后搜索时间',
  `created_date` date DEFAULT NULL COMMENT '创建日期',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_keyword` (`keyword`),
  KEY `idx_count_desc` (`search_count` DESC),
  KEY `idx_created_date` (`created_date`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='热门关键词表';

-- ============================================
-- 8. 系统日志表
-- ============================================
CREATE TABLE IF NOT EXISTS `system_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `level` varchar(20) NOT NULL COMMENT '日志级别',
  `type` varchar(30) DEFAULT 'system' COMMENT '日志类型',
  `message` varchar(1000) NOT NULL COMMENT '日志消息',
  `context` json DEFAULT NULL COMMENT '上下文数据',
  `stack_trace` text COMMENT '堆栈跟踪',
  `ip` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `request_id` varchar(50) DEFAULT NULL COMMENT '请求ID',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_level` (`level`),
  KEY `idx_type` (`type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_ip` (`ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- ============================================
-- 9. 错误报告表
-- ============================================
CREATE TABLE IF NOT EXISTS `error_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `error_type` varchar(50) NOT NULL COMMENT '错误类型',
  `error_message` text NOT NULL COMMENT '错误消息',
  `error_url` varchar(500) DEFAULT NULL COMMENT '错误页面',
  `user_agent` text COMMENT '用户代理',
  `user_ip` varchar(45) DEFAULT NULL COMMENT '用户IP',
  `status` enum('pending','resolved','ignored') DEFAULT 'pending' COMMENT '处理状态',
  `resolved_at` timestamp NULL DEFAULT NULL COMMENT '解决时间',
  `resolved_by` varchar(50) DEFAULT NULL COMMENT '解决人',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_error_type` (`error_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错误报告表';

-- ============================================
-- 10. QR码记录表
-- ============================================
CREATE TABLE IF NOT EXISTS `qr_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `qr_hash` varchar(64) NOT NULL COMMENT 'QR码哈希',
  `original_url` text NOT NULL COMMENT '原始URL',
  `qr_data` text NOT NULL COMMENT 'QR码数据',
  `access_count` int(11) DEFAULT 0 COMMENT '访问次数',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_qr_hash` (`qr_hash`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='QR码记录表';

-- ============================================
-- 11. 搜索提示词表
-- ============================================
CREATE TABLE IF NOT EXISTS `search_suggestions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `keyword` varchar(100) NOT NULL COMMENT '提示词内容',
  `description` varchar(200) DEFAULT NULL COMMENT '提示词描述',
  `click_count` int(11) DEFAULT 0 COMMENT '点击次数',
  `search_count` int(11) DEFAULT 0 COMMENT '搜索次数',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `is_hot` tinyint(1) DEFAULT 0 COMMENT '是否热门',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_keyword` (`keyword`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索提示词表';

-- ============================================
-- 12. 广告位管理表
-- ============================================
CREATE TABLE IF NOT EXISTS `advertisements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `position` varchar(50) NOT NULL COMMENT '广告位置',
  `title` varchar(100) DEFAULT NULL COMMENT '广告标题',
  `content` text COMMENT '广告内容',
  `ad_type` varchar(20) DEFAULT 'text' COMMENT '广告类型',
  `link_url` varchar(500) DEFAULT NULL COMMENT '链接地址',
  `target` varchar(10) DEFAULT '_blank' COMMENT '链接打开方式',
  `image_url` varchar(500) DEFAULT NULL COMMENT '图片地址',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `click_count` int(11) DEFAULT 0 COMMENT '点击次数',
  `show_count` int(11) DEFAULT 0 COMMENT '展示次数',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_position` (`position`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告位管理表';

-- ============================================
-- 13. 友情链接表
-- ============================================
CREATE TABLE IF NOT EXISTS `friend_links` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `site_name` varchar(100) NOT NULL COMMENT '网站名称',
  `site_url` varchar(500) NOT NULL COMMENT '网站链接',
  `site_logo` varchar(500) DEFAULT NULL COMMENT '网站LOGO',
  `description` varchar(200) DEFAULT NULL COMMENT '网站描述',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `link_type` varchar(20) DEFAULT 'text' COMMENT '链接类型',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='友情链接表';

-- ============================================
-- 14. 管理员操作日志表
-- ============================================
CREATE TABLE IF NOT EXISTS `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) DEFAULT NULL COMMENT '管理员ID',
  `username` varchar(50) DEFAULT NULL COMMENT '操作用户名',
  `action` varchar(50) NOT NULL COMMENT '操作动作',
  `module` varchar(50) DEFAULT NULL COMMENT '操作模块',
  `description` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `user_ip` varchar(45) DEFAULT NULL COMMENT '用户IP',
  `user_agent` text COMMENT '用户代理',
  `execution_time` int(11) DEFAULT 0 COMMENT '执行时间',
  `status` varchar(20) DEFAULT 'success' COMMENT '操作状态',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_action` (`action`),
  KEY `idx_module` (`module`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员操作日志表';

-- ============================================
-- 插入基础配置数据
-- ============================================

-- 基础设置
INSERT IGNORE INTO `site_config` (`config_key`, `config_value`, `config_desc`, `config_group`, `is_system`) VALUES
('site_name', 'SOPAN搜索', '网站名称', 'basic', 1),
('site_description', '专业的网盘资源搜索引擎', '网站描述', 'basic', 1),
('site_keywords', '网盘搜索,资源搜索,文件搜索,SOPAN', '网站关键词', 'basic', 1),
('site_url', '', '网站地址', 'basic', 1),
('admin_path', 'admin', '后台访问路径', 'basic', 1);

-- API设置
INSERT IGNORE INTO `site_config` (`config_key`, `config_value`, `config_desc`, `config_group`, `is_system`) VALUES
('panseeker_api_url', '', 'PanSeeker API地址', 'api', 1),
('panseeker_api_key', '', 'PanSeeker API密钥', 'api', 1);

-- 搜索设置
INSERT IGNORE INTO `site_config` (`config_key`, `config_value`, `config_desc`, `config_group`, `is_system`) VALUES
('search_timeout', '30', '搜索超时时间(秒)', 'search', 0),
('max_results_per_page', '20', '每页最大结果数', 'search', 0),
('enable_search_log', '1', '启用搜索日志', 'search', 0);

-- 系统设置
INSERT IGNORE INTO `site_config` (`config_key`, `config_value`, `config_desc`, `config_group`, `is_system`) VALUES
('enable_error_report', '1', '启用错误报告', 'system', 0),
('cache_expire_time', '3600', '缓存过期时间(秒)', 'system', 0),
('qr_code_expire_time', '86400', 'QR码过期时间(秒)', 'system', 0);

-- 网盘设置
INSERT IGNORE INTO `site_config` (`config_key`, `config_value`, `config_desc`, `config_group`, `is_system`) VALUES
('quark_status', '1', '夸克网盘启用状态', 'netdisk', 0),
('aliyun_status', '1', '阿里云盘启用状态', 'netdisk', 0),
('baidu_status', '1', '百度网盘启用状态', 'netdisk', 0),
('uc_status', '0', 'UC网盘启用状态', 'netdisk', 0);

-- ============================================
-- 插入示例热门关键词
-- ============================================
INSERT IGNORE INTO `hot_keywords` (`keyword`, `search_count`, `created_date`) VALUES
('电影', 10, CURDATE()),
('音乐', 8, CURDATE()),
('软件', 6, CURDATE()),
('游戏', 5, CURDATE()),
('教程', 4, CURDATE());

-- ============================================
-- 插入搜索提示词示例数据
-- ============================================
INSERT IGNORE INTO `search_suggestions` (`keyword`, `description`, `sort_order`, `is_hot`, `status`) VALUES
('最新电影', '最新上映的电影资源', 1, 1, 1),
('热门电视剧', '当前热播的电视剧', 2, 1, 1),
('纪录片', '高质量纪录片资源', 3, 0, 1),
('音乐专辑', '最新音乐专辑', 4, 0, 1),
('学习资料', '各类学习资源', 5, 0, 1),
('软件工具', '实用软件工具', 6, 0, 1),
('游戏资源', '游戏相关资源', 7, 0, 1),
('图书资料', '电子书籍资源', 8, 0, 1);

SET FOREIGN_KEY_CHECKS = 1;

-- ============================================
-- 安装完成标记
-- ============================================
-- 此SQL文件执行完成后，系统将完成数据库初始化