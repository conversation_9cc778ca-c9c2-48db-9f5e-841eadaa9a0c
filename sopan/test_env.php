<?php
// SOPAN 环境检测文件
echo "<h2>SOPAN 环境检测</h2>";
echo "<p>PHP版本：" . PHP_VERSION . "</p>";
echo "<p>当前时间：" . date('Y-m-d H:i:s') . "</p>";

// 检查必需的扩展
echo "<h3>PHP扩展检查：</h3>";
$extensions = ['pdo_mysql', 'json', 'mbstring', 'curl', 'openssl'];
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? "✅ 已安装" : "❌ 未安装";
    $color = extension_loaded($ext) ? "green" : "red";
    echo "<p style='color: {$color}'>{$ext}: {$status}</p>";
}

// 检查文件权限
echo "<h3>文件检查：</h3>";
$files = [
    'install.php' => file_exists(__DIR__ . '/install.php'),
    'database/sopan.sql' => file_exists(__DIR__ . '/database/sopan.sql'),
    'config目录' => is_dir(__DIR__ . '/config'),
    'storage目录' => is_dir(__DIR__ . '/storage')
];

foreach ($files as $file => $exists) {
    $status = $exists ? "✅ 存在" : "❌ 不存在";
    $color = $exists ? "green" : "red";
    echo "<p style='color: {$color}'>{$file}: {$status}</p>";
}

// 检查目录权限
echo "<h3>目录权限检查：</h3>";
$writable = is_writable(__DIR__);
echo "<p style='color: " . ($writable ? "green" : "red") . "'>项目根目录可写: " . ($writable ? "✅ 是" : "❌ 否") . "</p>";

if (is_dir(__DIR__ . '/config')) {
    $configWritable = is_writable(__DIR__ . '/config');
    echo "<p style='color: " . ($configWritable ? "green" : "red") . "'>config目录可写: " . ($configWritable ? "✅ 是" : "❌ 否") . "</p>";
}

if (is_dir(__DIR__ . '/storage')) {
    $storageWritable = is_writable(__DIR__ . '/storage');
    echo "<p style='color: " . ($storageWritable ? "green" : "red") . "'>storage目录可写: " . ($storageWritable ? "✅ 是" : "❌ 否") . "</p>";
}

// 测试数据库连接（如果有配置）
echo "<h3>其他信息：</h3>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>当前脚本路径: " . __FILE__ . "</p>";
echo "<p>请求URI: " . $_SERVER['REQUEST_URI'] . "</p>";
?>