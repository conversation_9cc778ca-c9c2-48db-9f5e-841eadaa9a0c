<?php
// SOPAN 安装调试页面
echo "<h2>SOPAN 安装调试</h2>";
echo "<p>开始调试安装过程...</p>";

try {
    // 1. 检查安装程序文件
    echo "<h3>1. 检查安装程序文件</h3>";
    $installFile = __DIR__ . '/install.php';
    if (file_exists($installFile)) {
        echo "<p style='color: green'>✅ install.php 存在</p>";
        echo "<p>文件大小: " . filesize($installFile) . " 字节</p>";
        echo "<p>文件权限: " . substr(sprintf('%o', fileperms($installFile)), -4) . "</p>";
    } else {
        echo "<p style='color: red'>❌ install.php 不存在</p>";
    }

    // 2. 检查数据库文件
    echo "<h3>2. 检查数据库文件</h3>";
    $sqlFile = __DIR__ . '/database/sopan.sql';
    if (file_exists($sqlFile)) {
        echo "<p style='color: green'>✅ sopan.sql 存在</p>";
        echo "<p>文件大小: " . filesize($sqlFile) . " 字节</p>";
        
        // 检查SQL文件内容
        $sqlContent = file_get_contents($sqlFile);
        $tableCount = substr_count($sqlContent, 'CREATE TABLE');
        echo "<p>包含数据表数量: {$tableCount}</p>";
    } else {
        echo "<p style='color: red'>❌ sopan.sql 不存在</p>";
    }

    // 3. 测试安装文件语法
    echo "<h3>3. 测试安装文件语法</h3>";
    
    // 检查PHP命令行工具是否可用
    $phpCommand = null;
    $possibleCommands = ['php', '/usr/bin/php', '/usr/local/bin/php', 'php8.4', 'php8.1', 'php8.0'];
    
    foreach ($possibleCommands as $cmd) {
        $testResult = @shell_exec($cmd . ' --version 2>/dev/null');
        if ($testResult && strpos($testResult, 'PHP') !== false) {
            $phpCommand = $cmd;
            break;
        }
    }
    
    if ($phpCommand) {
        echo "<p style='color: green'>✅ 找到PHP命令: {$phpCommand}</p>";
        
        // 使用找到的PHP命令进行语法检查
        ob_start();
        $syntaxCheck = @shell_exec($phpCommand . ' -l ' . escapeshellarg($installFile) . ' 2>&1');
        ob_end_clean();
        
        if ($syntaxCheck && strpos($syntaxCheck, 'No syntax errors') !== false) {
            echo "<p style='color: green'>✅ install.php 语法正确</p>";
        } elseif ($syntaxCheck) {
            echo "<p style='color: red'>❌ install.php 语法错误:</p>";
            echo "<pre style='background: #f0f0f0; padding: 10px;'>" . htmlspecialchars($syntaxCheck) . "</pre>";
        } else {
            echo "<p style='color: orange'>⚠️ 无法执行语法检查</p>";
        }
    } else {
        echo "<p style='color: orange'>⚠️ PHP命令行工具未找到，跳过语法检查</p>";
        echo "<p style='color: blue'>💡 这不会影响Web安装程序的正常运行</p>";
        
        // 尝试简单的文件完整性检查
        $installContent = file_get_contents($installFile);
        if (strpos($installContent, '<?php') === 0 && strpos($installContent, 'handleInstallation') !== false) {
            echo "<p style='color: green'>✅ install.php 文件结构看起来正确</p>";
        } else {
            echo "<p style='color: red'>❌ install.php 文件可能有问题</p>";
        }
    }

    // 4. 检查配置目录状态（新的配置文件管理方式）
    echo "<h3>4. 检查配置目录状态</h3>";
    
    $configDir = __DIR__ . '/config';
    if (is_dir($configDir)) {
        echo "<p style='color: green'>✅ config目录存在</p>";
        
        // 检查配置文件
        $configFiles = ['app.php', 'database.php', 'search.php', 'installed.lock'];
        $existingFiles = [];
        
        foreach ($configFiles as $file) {
            $filePath = $configDir . '/' . $file;
            if (file_exists($filePath)) {
                $existingFiles[] = $file;
                echo "<p style='color: orange'>⚠️ {$file} 已存在</p>";
            } else {
                echo "<p style='color: green'>✅ {$file} 不存在（正确）</p>";
            }
        }
        
        if (!empty($existingFiles)) {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<p><strong>注意：</strong>发现已存在的配置文件，这可能表明：</p>";
            echo "<ol>";
            echo "<li>系统已经安装过</li>";
            echo "<li>上次安装未完成但生成了部分配置文件</li>";
            echo "</ol>";
            echo "<p><strong>建议：</strong>如需重新安装，请先备份重要配置，然后删除这些文件</p>";
            echo "</div>";
        } else {
            echo "<p style='color: green'><strong>✅ 配置目录状态正确：适合进行全新安装</strong></p>";
        }
        
        echo "<p>目录权限: " . (is_writable($configDir) ? "✅ 可写" : "❌ 不可写") . "</p>";
    } else {
        echo "<p style='color: green'>✅ config目录不存在（正确，安装时会自动创建）</p>";
    }
    
    // 5. 检查配置模板目录
    echo "<h3>5. 检查配置模板目录</h3>";
    $templateDir = __DIR__ . '/config.example';
    if (is_dir($templateDir)) {
        echo "<p style='color: green'>✅ config.example目录存在</p>";
        $templateFiles = glob($templateDir . '/*.example');
        echo "<p>模板文件数量: " . count($templateFiles) . "</p>";
        foreach ($templateFiles as $file) {
            echo "<p style='color: blue'>📄 " . basename($file) . "</p>";
        }
    } else {
        echo "<p style='color: orange'>⚠️ config.example目录不存在</p>";
    }

    // 6. 模拟安装页面访问
    echo "<h3>6. 模拟安装页面访问</h3>";
    
    // 检查HTTP头
    echo "<p>当前请求方法: " . $_SERVER['REQUEST_METHOD'] . "</p>";
    echo "<p>请求URI: " . $_SERVER['REQUEST_URI'] . "</p>";
    
    // 7. 检查安装日志
    echo "<h3>7. 检查安装日志</h3>";
    $logDir = __DIR__ . '/logs';
    if (is_dir($logDir)) {
        $logFiles = glob($logDir . '/install_*.log');
        if ($logFiles) {
            echo "<p style='color: green'>✅ 找到安装日志文件:</p>";
            foreach ($logFiles as $logFile) {
                $logContent = file_get_contents($logFile);
                echo "<h4>" . basename($logFile) . "</h4>";
                echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: auto;'>" . htmlspecialchars($logContent) . "</pre>";
            }
        } else {
            echo "<p style='color: orange'>⚠️ 没有找到安装日志文件</p>";
        }
    } else {
        echo "<p style='color: orange'>⚠️ 日志目录不存在</p>";
    }

    // 8. 测试关键函数
    echo "<h3>8. 测试关键函数</h3>";
    
    // 检查是否能成功定义基本函数
    try {
        // 简单的数据库连接测试函数
        function testDatabaseConnectionDebug($host = 'localhost', $port = 3306, $username = '', $password = '', $database = '') {
            if (empty($username)) {
                return "用户名不能为空";
            }
            
            try {
                $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_TIMEOUT => 5
                ]);
                return "数据库连接测试成功";
            } catch (PDOException $e) {
                return "数据库连接失败: " . $e->getMessage();
            }
        }
        
        echo "<p style='color: green'>✅ 基础函数定义成功</p>";
        
        // 测试数据库连接函数（使用测试数据）
        $dbTestResult = testDatabaseConnectionDebug('localhost', 3306, 'test', 'test', 'test');
        echo "<p>数据库连接测试结果: <code style='background: #f0f0f0; padding: 2px 5px;'>{$dbTestResult}</code></p>";
        
        echo "<div style='background: #e6f7ff; border-left: 4px solid #1890ff; padding: 10px; margin: 10px 0;'>";
        echo "<p><strong>💡 提示：</strong>上面的数据库连接测试使用的是测试参数，失败是正常的。</p>";
        echo "<p>在安装过程中，请使用您的实际数据库参数。</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p style='color: red'>❌ 函数测试失败: " . $e->getMessage() . "</p>";
    }

    // 9. PHP环境详细检查
    echo "<h3>9. PHP环境详细检查</h3>";
    
    echo "<div style='background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>PHP版本:</strong> " . PHP_VERSION . "</p>";
    echo "<p><strong>PHP SAPI:</strong> " . php_sapi_name() . "</p>";
    echo "<p><strong>内存限制:</strong> " . ini_get('memory_limit') . "</p>";
    echo "<p><strong>执行时间限制:</strong> " . ini_get('max_execution_time') . "秒</p>";
    echo "<p><strong>上传文件大小限制:</strong> " . ini_get('upload_max_filesize') . "</p>";
    echo "<p><strong>POST数据大小限制:</strong> " . ini_get('post_max_size') . "</p>";
    echo "</div>";
    
    // 检查必需的PHP扩展
    echo "<h4>PHP扩展检查：</h4>";
    $requiredExtensions = [
        'pdo_mysql' => 'MySQL数据库支持',
        'json' => 'JSON数据处理',
        'mbstring' => '多字节字符串支持',
        'curl' => 'HTTP请求支持',
        'openssl' => 'SSL/TLS加密支持',
        'gd' => '图像处理支持',
        'zip' => '压缩文件支持'
    ];
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; margin: 10px 0;'>";
    foreach ($requiredExtensions as $ext => $desc) {
        $loaded = extension_loaded($ext);
        $status = $loaded ? "✅ 已安装" : "❌ 未安装";
        $color = $loaded ? "green" : "red";
        
        echo "<div style='background: white; border-left: 4px solid {$color}; padding: 10px; border-radius: 3px;'>";
        echo "<p style='margin: 0; font-weight: bold;'>{$ext}</p>";
        echo "<p style='margin: 5px 0 0 0; color: {$color};'>{$status}</p>";
        echo "<p style='margin: 5px 0 0 0; font-size: 0.9em; color: #666;'>{$desc}</p>";
        echo "</div>";
    }
    echo "</div>";
    
    // 10. Web服务器信息
    echo "<h3>10. Web服务器信息</h3>";
    echo "<p>Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "</p>";
    echo "<p>Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? '未知') . "</p>";
    echo "<p>Script Filename: " . ($_SERVER['SCRIPT_FILENAME'] ?? '未知') . "</p>";
    echo "<p>Server Name: " . ($_SERVER['SERVER_NAME'] ?? '未知') . "</p>";
    echo "<p>Request URI: " . ($_SERVER['REQUEST_URI'] ?? '未知') . "</p>";
    
    // 11. 直接测试安装页面访问
    echo "<h3>11. 直接访问测试</h3>";
    echo "<div style='background: #f0f8ff; border: 1px solid #0066cc; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p style='margin: 0 0 10px 0;'><strong>🔗 安装页面链接：</strong></p>";
    echo "<p style='margin: 5px 0;'><a href='/install' target='_blank' style='color: #0066cc; text-decoration: underline; font-weight: bold;'>点击直接访问安装页面 (/install)</a> - <span style='color: #52c41a; font-weight: bold;'>推荐</span></p>";
    echo "<p style='margin: 5px 0;'><a href='/install.php' target='_blank' style='color: #0066cc; text-decoration: underline;'>点击访问 install.php 文件</a> - <span style='color: #666;'>备用</span></p>";
    echo "<div style='background: #e6f7ff; padding: 10px; margin-top: 10px; border-radius: 3px;'>";
    echo "<p style='margin: 0; font-size: 0.9em;'><strong>💡 说明：</strong></p>";
    echo "<p style='margin: 5px 0 0 0; font-size: 0.9em;'>• <code>/install</code> 是主要访问地址，通过简洁的Nginx重写规则处理</p>";
    echo "<p style='margin: 5px 0 0 0; font-size: 0.9em;'>• <code>/install.php</code> 是直接访问安装文件的备用方式</p>";
    echo "<p style='margin: 5px 0 0 0; font-size: 0.9em;'>• 使用ThinkPHP风格的简洁重写配置：<code>rewrite ^(.*)$ /index.php?s=$1</code></p>";
    echo "</div>";
    echo "</div>";

    echo "<hr>";
    echo "<h3>🎯 总结建议</h3>";
    echo "<div style='background: #e8f4fd; border-left: 4px solid #1890ff; padding: 15px; margin: 10px 0;'>";
    echo "<h4>配置文件管理新规则：</h4>";
    echo "<ol style='margin-left: 20px;'>";
    echo "<li><strong>安装前</strong>：<code>config/</code> 目录应该是空的或不存在</li>";
    echo "<li><strong>安装时</strong>：安装程序会自动创建所有必需的配置文件</li>";
    echo "<li><strong>安装后</strong>：可以直接编辑 <code>config/</code> 目录下的文件</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #fff2e8; border-left: 4px solid #fa8c16; padding: 15px; margin: 10px 0;'>";
    echo "<h4>常见问题解决：</h4>";
    echo "<ol style='margin-left: 20px;'>";
    echo "<li>如果系统已安装，请删除 <code>config/installed.lock</code> 文件</li>";
    echo "<li>如果有语法错误，请修复 <code>install.php</code> 文件</li>";
    echo "<li>检查 Nginx 配置是否正确处理 <code>/install</code> 路径</li>";
    echo "<li>查看上面的日志文件了解具体错误</li>";
    echo "<li>确保数据库连接参数正确</li>";
    echo "</ol>";
    echo "</div>";

} catch (Exception $e) {
    echo "<p style='color: red'>调试过程出错: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3 { color: #333; }
pre { white-space: pre-wrap; word-wrap: break-word; }
</style>