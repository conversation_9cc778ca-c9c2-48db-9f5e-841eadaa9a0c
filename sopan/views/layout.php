<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title><?= $this->escape($page_title ?? '网盘搜索') ?></title>
    <meta name="description" content="<?= $this->escape($meta_description ?? '') ?>">
    <meta name="keywords" content="<?= $this->escape($meta_keywords ?? '') ?>">
    
    <!-- SEO优化 -->
    <meta name="robots" content="index,follow">
    <meta name="author" content="SOPAN Project">
    <link rel="canonical" href="<?= $this->escape($_SERVER['REQUEST_URI'] ?? '') ?>">
    
    <!-- 移动端优化 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    
    <!-- Tailwind CSS - 本地化 -->
    <script src="<?= $base_url ?>/public/assets/js/tailwind.min.js"></script>
    
    <!-- Vue 3 - 本地化 -->
    <script src="<?= $base_url ?>/public/assets/js/vue.global.min.js"></script>
    
    <!-- Axios - 本地化 -->
    <script src="<?= $base_url ?>/public/assets/js/axios.min.js"></script>
    
    <!-- SOPAN 工具类 -->
    <script src="<?= $base_url ?>/assets/js/utils.js"></script>
    
    <!-- Font Awesome 图标库 - 本地化 -->
    <link rel="stylesheet" href="<?= $base_url ?>/public/assets/css/fontawesome.min.css">
    

    
    <!-- 自定义样式 -->
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 加载动画 */
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* SSE连接状态指示器 */
        .connection-status {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            color: white;
            display: none;
        }
        
        .connection-status.connected {
            background-color: #10b981;
            display: block;
        }
        
        .connection-status.connecting {
            background-color: #f59e0b;
            display: block;
        }
        
        .connection-status.disconnected {
            background-color: #ef4444;
            display: block;
        }
        
        /* 搜索结果动画 */
        .search-result-item {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.5s ease-out forwards;
        }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 平台标签样式 */
        .platform-tag {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .platform-tag.quark {
            background-color: #3b82f6;
            color: white;
        }
        
        .platform-tag.baidu {
            background-color: #2563eb;
            color: white;
        }
        
        .platform-tag.aliyun {
            background-color: #ff6600;
            color: white;
        }
        
        .platform-tag.uc {
            background-color: #059669;
            color: white;
        }
        
        /* 搜索进度条 */
        .search-progress {
            width: 100%;
            height: 4px;
            background-color: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
        }
        
        .search-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #10b981);
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        /* 响应式表格 */
        @media (max-width: 768px) {
            .mobile-hidden {
                display: none;
            }
            
            .search-result-item {
                padding: 12px;
            }
            
            .search-input {
                font-size: 16px; /* 防止iOS缩放 */
            }
        }
        
        /* 暗色模式支持 */
        @media (prefers-color-scheme: dark) {
            .dark-mode {
                background-color: #1f2937;
                color: #f9fafb;
            }
        }
    </style>
    
    <!-- 网站图标 -->
    <?php if (!empty($site_logo)): ?>
    <link rel="icon" type="image/x-icon" href="<?= $this->escape($site_logo) ?>">
    <?php endif; ?>
    
    <!-- 预加载重要资源 - 本地化 -->
    <link rel="preload" href="<?= $base_url ?>/public/assets/js/vue.global.min.js" as="script">
    <link rel="preload" href="<?= $base_url ?>/public/assets/js/axios.min.js" as="script">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 连接状态指示器 -->
    <div id="connectionStatus" class="connection-status">
        <span id="connectionText">连接中...</span>
    </div>
    
    <!-- 页面头部 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo和标题 -->
                <div class="flex items-center">
                    <a href="<?= $base_url ?>/" class="flex items-center space-x-3">
                        <?php if (!empty($site_logo)): ?>
                            <img src="<?= $this->escape($site_logo) ?>" alt="<?= $this->escape($site_name) ?>" class="h-8 w-auto">
                        <?php endif; ?>
                        <h1 class="text-xl font-bold text-gray-900">
                            <?= $this->escape($site_name) ?>
                        </h1>
                    </a>
                </div>
                
                <!-- 导航菜单 -->
                <nav class="hidden md:flex space-x-8">
                    <a href="<?= $base_url ?>/" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                        首页
                    </a>
                    <a href="<?= $base_url ?>/about" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                        关于
                    </a>
                    <a href="<?= $base_url ?>/help" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                        帮助
                    </a>
                    <a href="<?= $base_url ?>/contact" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                        联系
                    </a>
                </nav>
                
                <!-- 移动端菜单按钮 -->
                <div class="md:hidden">
                    <button id="mobileMenuBtn" class="text-gray-600 hover:text-gray-900 p-2">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 移动端菜单 -->
        <div id="mobileMenu" class="hidden md:hidden bg-white border-t border-gray-200">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="<?= $base_url ?>/" class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900">首页</a>
                <a href="<?= $base_url ?>/about" class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900">关于</a>
                <a href="<?= $base_url ?>/help" class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900">帮助</a>
                <a href="<?= $base_url ?>/contact" class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900">联系</a>
            </div>
        </div>
    </header>
    
    <!-- 主要内容区域 -->
    <main class="flex-1">
        <?php $this->section('content', '页面内容'); ?>
    </main>
    
    <!-- 页面底部 -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- 网站信息 -->
                <div class="col-span-1 md:col-span-2">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <?= $this->escape($site_name) ?>
                    </h3>
                    <p class="text-gray-600 text-sm mb-4">
                        <?= $this->escape($site_description) ?>
                    </p>
                    <?php if (!empty($icp_number)): ?>
                    <p class="text-gray-500 text-xs">
                        ICP备案号：<?= $this->escape($icp_number) ?>
                    </p>
                    <?php endif; ?>
                </div>
                
                <!-- 功能特色 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">功能特色</h4>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li>• 多平台搜索</li>
                        <li>• 实时结果推送</li>
                        <li>• 智能转存</li>
                        <li>• 批量操作</li>
                    </ul>
                </div>
                
                <!-- 联系方式 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">联系方式</h4>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li>
                            <a href="<?= $base_url ?>/help" class="hover:text-blue-600">使用帮助</a>
                        </li>
                        <li>
                            <a href="<?= $base_url ?>/contact" class="hover:text-blue-600">意见反馈</a>
                        </li>
                        <li>
                            <a href="<?= $base_url ?>/about" class="hover:text-blue-600">关于我们</a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 版权信息 -->
            <div class="border-t border-gray-200 mt-8 pt-6 text-center">
                <p class="text-sm text-gray-500">
                    © <?= $current_year ?> <?= $this->escape($site_name) ?>. All rights reserved.
                </p>
            </div>
        </div>
    </footer>
    
    <!-- 通用JavaScript -->
    <script>
        // 全局配置
        window.SOPAN_CONFIG = {
            baseUrl: '<?= $base_url ?>',
            apiUrl: '<?= $api_url ?>',
            siteName: '<?= $this->escape($site_name) ?>'
        };
        
        // 移动端菜单切换
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');
            
            if (mobileMenuBtn && mobileMenu) {
                mobileMenuBtn.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }
            
            // 点击外部关闭菜单
            document.addEventListener('click', function(e) {
                if (mobileMenu && !mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                    mobileMenu.classList.add('hidden');
                }
            });
        });
        
        // 等待工具类加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 工具类已在utils.js中定义，这里只需要确保初始化
            if (typeof Utils !== 'undefined') {
                console.log('SOPAN工具类加载完成');
            }
        });
        
        // 连接状态管理
        const ConnectionManager = {
            status: 'disconnected',
            statusElement: document.getElementById('connectionStatus'),
            textElement: document.getElementById('connectionText'),
            
            setStatus: function(status, text) {
                this.status = status;
                this.statusElement.className = `connection-status ${status}`;
                this.textElement.textContent = text || this.getStatusText(status);
            },
            
            getStatusText: function(status) {
                const texts = {
                    connecting: '连接中...',
                    connected: '已连接',
                    disconnected: '连接断开',
                    error: '连接错误'
                };
                return texts[status] || status;
            },
            
            hide: function() {
                this.statusElement.style.display = 'none';
            }
        };
    </script>
    
    <!-- 页面特定JavaScript -->
    <?php $this->section('scripts', ''); ?>
</body>
</html>