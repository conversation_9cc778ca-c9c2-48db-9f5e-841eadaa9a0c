<?php $this->setLayout('admin/layout'); ?>

<?php $this->startSection('title'); ?>仪表板 - SOPAN后台管理<?php $this->endSection(); ?>

<?php $this->startSection('content'); ?>
<div id="dashboardApp">
    <!-- 页面标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">仪表板</h1>
        <p class="mt-2 text-gray-600">欢迎回来！查看系统运行状态和统计数据</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- 总搜索次数 -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm font-medium">总搜索次数</p>
                    <p class="text-3xl font-bold">{{ formatNumber(stats.total_searches) }}</p>
                </div>
                <div class="bg-blue-400 rounded-full p-3">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </div>
            </div>
            <p class="mt-4 text-blue-100 text-sm">
                <span class="font-medium">今日: {{ formatNumber(stats.today_searches) }}</span>
                <span class="ml-2">昨日: {{ formatNumber(stats.yesterday_searches) }}</span>
            </p>
        </div>
        
        <!-- 转存成功次数 -->
        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm font-medium">转存成功次数</p>
                    <p class="text-3xl font-bold">{{ formatNumber(stats.total_transfers) }}</p>
                </div>
                <div class="bg-green-400 rounded-full p-3">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                </div>
            </div>
            <p class="mt-4 text-green-100 text-sm">
                <span class="font-medium">成功率: {{ (stats.success_rate * 100).toFixed(1) }}%</span>
                <span class="ml-2">今日: {{ formatNumber(stats.today_transfers) }}</span>
            </p>
        </div>
        
        <!-- 用户访问量 -->
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm font-medium">用户访问量</p>
                    <p class="text-3xl font-bold">{{ formatNumber(stats.total_visits) }}</p>
                </div>
                <div class="bg-purple-400 rounded-full p-3">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
            </div>
            <p class="mt-4 text-purple-100 text-sm">
                <span class="font-medium">在线: {{ stats.online_users }}</span>
                <span class="ml-2">今日: {{ formatNumber(stats.today_visits) }}</span>
            </p>
        </div>
        
        <!-- 系统状态 -->
        <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100 text-sm font-medium">系统状态</p>
                    <p class="text-2xl font-bold">{{ systemInfo.status_text }}</p>
                </div>
                <div class="bg-orange-400 rounded-full p-3">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,9H13V7H11M11,17H13V11H11V17Z"/>
                    </svg>
                </div>
            </div>
            <p class="mt-4 text-orange-100 text-sm">
                <span class="font-medium">CPU: {{ systemInfo.cpu_usage }}%</span>
                <span class="ml-2">内存: {{ systemInfo.memory_usage }}%</span>
            </p>
        </div>
    </div>
    
    <!-- 图表和数据展示 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- 搜索趋势图表 -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">搜索趋势（最近7天）</h3>
            <div class="h-64 flex items-end justify-between space-x-2">
                <div v-for="(day, index) in searchTrend" 
                     :key="index"
                     class="flex flex-col items-center space-y-2">
                    <div class="bg-blue-500 rounded-t hover:bg-blue-600 transition-colors cursor-pointer relative group"
                         :style="{ height: (day.count / maxSearchCount * 200) + 'px', width: '30px' }"
                         :title="`${day.date}: ${day.count}次搜索`">
                        <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                            {{ day.count }}
                        </div>
                    </div>
                    <span class="text-xs text-gray-500 transform -rotate-45">{{ day.date }}</span>
                </div>
            </div>
        </div>
        
        <!-- 转存平台分布 -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">转存平台分布</h3>
            <div class="space-y-4">
                <div v-for="platform in platformStats" :key="platform.name" class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 rounded-full" :class="platform.color"></div>
                        <span class="text-gray-700 font-medium">{{ platform.name }}</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600">{{ platform.count }}次</span>
                        <div class="w-24 bg-gray-200 rounded-full h-2">
                            <div class="h-2 rounded-full" 
                                 :class="platform.color"
                                 :style="{ width: (platform.percentage) + '%' }"></div>
                        </div>
                        <span class="text-sm text-gray-500 w-12">{{ platform.percentage }}%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近活动和系统信息 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 最近搜索记录 -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">最近搜索记录</h3>
            <div class="space-y-3">
                <div v-for="search in recentSearches" :key="search.id" 
                     class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                    <div class="flex-1">
                        <p class="font-medium text-gray-900">{{ search.keyword }}</p>
                        <p class="text-sm text-gray-500">{{ search.search_time }} · IP: {{ search.user_ip }}</p>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              :class="search.result_count > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                            {{ search.result_count }}个结果
                        </span>
                    </div>
                </div>
                <div v-if="recentSearches.length === 0" class="text-center py-8 text-gray-500">
                    暂无搜索记录
                </div>
            </div>
        </div>
        
        <!-- 系统信息 -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">系统信息</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">PHP版本</span>
                    <span class="font-medium">{{ systemInfo.php_version }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">系统负载</span>
                    <span class="font-medium">{{ systemInfo.load_average }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">磁盘使用</span>
                    <div class="flex items-center space-x-2">
                        <div class="w-16 bg-gray-200 rounded-full h-2">
                            <div class="h-2 rounded-full bg-orange-500" 
                                 :style="{ width: systemInfo.disk_usage + '%' }"></div>
                        </div>
                        <span class="text-sm font-medium">{{ systemInfo.disk_usage }}%</span>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">数据库大小</span>
                    <span class="font-medium">{{ systemInfo.db_size }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">运行时间</span>
                    <span class="font-medium">{{ systemInfo.uptime }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">网盘状态</span>
                    <div class="flex space-x-2">
                        <span v-for="status in systemInfo.platform_status" :key="status.name"
                              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                              :class="status.online ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                            {{ status.name }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
const { createApp } = Vue;

createApp({
    data() {
        return {
            stats: <?= json_encode($stats) ?>,
            systemInfo: <?= json_encode($system_info) ?>,
            searchTrend: <?= json_encode($search_trend) ?>,
            platformStats: <?= json_encode($platform_stats) ?>,
            recentSearches: <?= json_encode($recent_searches) ?>,
            isLoading: false,
            autoRefresh: true,
            refreshInterval: null
        };
    },
    
    computed: {
        maxSearchCount() {
            return Math.max(...this.searchTrend.map(day => day.count)) || 100;
        }
    },
    
    mounted() {
        this.startAutoRefresh();
    },
    
    beforeUnmount() {
        this.stopAutoRefresh();
    },
    
    methods: {
        formatNumber(num) {
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + '万';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        },
        
        async refreshData() {
            if (this.isLoading) return;
            
            this.isLoading = true;
            try {
                const response = await axios.get(`${window.SOPAN_CONFIG.baseUrl}/admin/dashboard/data`);
                if (response.data.success) {
                    const data = response.data.data;
                    this.stats = data.stats;
                    this.systemInfo = data.system_info;
                    this.searchTrend = data.search_trend;
                    this.platformStats = data.platform_stats;
                    this.recentSearches = data.recent_searches;
                }
            } catch (error) {
                console.error('刷新数据失败:', error);
            } finally {
                this.isLoading = false;
            }
        },
        
        startAutoRefresh() {
            if (this.autoRefresh && !this.refreshInterval) {
                this.refreshInterval = setInterval(() => {
                    this.refreshData();
                }, 30000); // 30秒刷新一次
            }
        },
        
        stopAutoRefresh() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
        },
        
        toggleAutoRefresh() {
            this.autoRefresh = !this.autoRefresh;
            if (this.autoRefresh) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        }
    }
}).mount('#dashboardApp');
</script>
<?php $this->endSection(); ?>