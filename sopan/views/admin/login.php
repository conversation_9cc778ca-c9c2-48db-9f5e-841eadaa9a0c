<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - <?= $this->escape($site_name) ?></title>
    
    <!-- Tailwind CSS 本地化 -->
    <script src="/sopan/public/assets/js/tailwind.min.js"></script>
    
    <!-- Font Awesome 本地化 -->
    <link rel="stylesheet" href="/sopan/public/assets/css/fontawesome.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group input {
            transition: all 0.3s ease;
        }
        
        .input-group input:focus {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .btn-login {
            background: linear-gradient(45deg, #667eea, #764ba2);
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(118, 75, 162, 0.3);
        }
        
        .error-alert {
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 15s infinite linear;
        }
        
        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <!-- 浮动形状背景 -->
    <div class="floating-shapes">
        <div class="shape w-16 h-16" style="left: 10%; animation-delay: 0s;"></div>
        <div class="shape w-12 h-12" style="left: 20%; animation-delay: 2s;"></div>
        <div class="shape w-20 h-20" style="left: 30%; animation-delay: 4s;"></div>
        <div class="shape w-8 h-8" style="left: 40%; animation-delay: 6s;"></div>
        <div class="shape w-14 h-14" style="left: 50%; animation-delay: 8s;"></div>
        <div class="shape w-18 h-18" style="left: 60%; animation-delay: 10s;"></div>
        <div class="shape w-10 h-10" style="left: 70%; animation-delay: 12s;"></div>
        <div class="shape w-16 h-16" style="left: 80%; animation-delay: 14s;"></div>
        <div class="shape w-12 h-12" style="left: 90%; animation-delay: 16s;"></div>
    </div>
    
    <div class="w-full max-w-md relative z-10">
        <!-- 登录卡片 -->
        <div class="login-card rounded-2xl shadow-2xl p-8">
            <!-- Logo和标题 -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-cog text-2xl text-white"></i>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">管理员登录</h1>
                <p class="text-white text-opacity-80">欢迎回来，请登录您的账户</p>
            </div>
            
            <!-- 错误提示 -->
            <?php if (!empty($error)): ?>
            <div class="error-alert bg-red-500 bg-opacity-20 border border-red-500 border-opacity-50 text-white px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <?= $this->escape($error) ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- 登录表单 -->
            <form method="POST" action="" class="space-y-6">
                <!-- 用户名 -->
                <div class="input-group">
                    <label class="block text-white text-sm font-medium mb-2">
                        <i class="fas fa-user mr-2"></i>用户名
                    </label>
                    <input type="text" 
                           name="username" 
                           placeholder="请输入用户名"
                           class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:border-white focus:bg-opacity-30"
                           required>
                </div>
                
                <!-- 密码 -->
                <div class="input-group">
                    <label class="block text-white text-sm font-medium mb-2">
                        <i class="fas fa-lock mr-2"></i>密码
                    </label>
                    <input type="password" 
                           name="password" 
                           placeholder="请输入密码"
                           class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:border-white focus:bg-opacity-30"
                           required>
                </div>
                
                <!-- 记住我 -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center text-white text-sm">
                        <input type="checkbox" name="remember" class="mr-2 rounded">
                        记住我
                    </label>
                    <a href="#" class="text-white text-opacity-80 hover:text-opacity-100 text-sm">
                        忘记密码？
                    </a>
                </div>
                
                <!-- 登录按钮 -->
                <button type="submit" class="btn-login w-full py-3 px-4 text-white font-medium rounded-lg focus:outline-none focus:ring-4 focus:ring-white focus:ring-opacity-50">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    登录
                </button>
            </form>
            
            <!-- 底部链接 -->
            <div class="mt-8 text-center">
                <a href="<?= $base_url ?>/" class="text-white text-opacity-80 hover:text-opacity-100 text-sm">
                    <i class="fas fa-arrow-left mr-1"></i>
                    返回首页
                </a>
            </div>
        </div>
        
        <!-- 版权信息 -->
        <div class="text-center mt-8 text-white text-opacity-60 text-sm">
            © <?= date('Y') ?> <?= $this->escape($site_name) ?>. All rights reserved.
        </div>
    </div>
    
    <script>
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.querySelector('input[name="username"]').value.trim();
            const password = document.querySelector('input[name="password"]').value;
            
            if (!username || !password) {
                e.preventDefault();
                showError('请填写完整的登录信息');
                return false;
            }
            
            if (username.length < 3) {
                e.preventDefault();
                showError('用户名长度至少3个字符');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                showError('密码长度至少6个字符');
                return false;
            }
        });
        
        function showError(message) {
            // 移除现有错误提示
            const existingError = document.querySelector('.error-alert');
            if (existingError) {
                existingError.remove();
            }
            
            // 创建新的错误提示
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-alert bg-red-500 bg-opacity-20 border border-red-500 border-opacity-50 text-white px-4 py-3 rounded-lg mb-6';
            errorDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    ${message}
                </div>
            `;
            
            // 插入到表单前面
            const form = document.querySelector('form');
            form.parentNode.insertBefore(errorDiv, form);
            
            // 3秒后自动消失
            setTimeout(() => {
                errorDiv.style.opacity = '0';
                setTimeout(() => {
                    errorDiv.remove();
                }, 300);
            }, 3000);
        }
        
        // 输入框焦点效果
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });
        
        // 防止重复提交
        let isSubmitting = false;
        document.querySelector('form').addEventListener('submit', function() {
            if (isSubmitting) {
                return false;
            }
            
            isSubmitting = true;
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>登录中...';
            button.disabled = true;
            
            // 5秒后恢复按钮状态（防止卡住）
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                isSubmitting = false;
            }, 5000);
        });
    </script>
</body>
</html>