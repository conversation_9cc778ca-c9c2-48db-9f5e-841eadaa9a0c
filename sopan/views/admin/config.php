<?php $this->setLayout('admin/layout'); ?>

<?php $this->startSection('title'); ?>网盘配置 - SOPAN后台管理<?php $this->endSection(); ?>

<?php $this->startSection('content'); ?>
<div id="configApp">
    <!-- 页面标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">网盘配置</h1>
        <p class="mt-2 text-gray-600">管理各个网盘平台的账号配置和转存设置</p>
    </div>
    
    <!-- 平台状态卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div v-for="platform in platforms" :key="platform.key" 
             class="bg-white rounded-lg shadow-lg p-6 border-l-4"
             :class="platform.status === '1' ? 'border-green-500' : 'border-red-500'">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">{{ platform.name }}</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="platform.status === '1' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                    {{ platform.status === '1' ? '已启用' : '未启用' }}
                </span>
            </div>
            <p class="text-gray-600 mb-4">{{ platform.description }}</p>
            <div class="flex space-x-2">
                <button @click="editPlatform(platform)" 
                        class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                    配置
                </button>
                <button @click="testPlatform(platform)" 
                        class="flex-1 bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                    测试
                </button>
            </div>
        </div>
    </div>
    
    <!-- 配置表单模态框 -->
    <div v-if="showConfigModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- 标题 -->
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">{{ currentPlatform.name }}配置</h3>
                    <button @click="closeConfigModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <!-- 配置表单 -->
                <form @submit.prevent="saveConfig">
                    <!-- 基础设置 -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-3">基础设置</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="flex items-center">
                                    <input v-model="configForm.status" type="checkbox" class="form-checkbox text-blue-600">
                                    <span class="ml-2 text-gray-700">启用此平台</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 夸克网盘配置 -->
                    <div v-if="currentPlatform.key === 'quark'" class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-3">夸克网盘配置</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Cookie</label>
                                <textarea v-model="configForm.cookie" 
                                         placeholder="请输入夸克网盘的Cookie信息"
                                         class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                         rows="4"></textarea>
                                <p class="mt-1 text-sm text-gray-500">从浏览器开发者工具中获取Cookie信息</p>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">转存目录</label>
                                    <input v-model="configForm.file_path" 
                                           type="text" 
                                           placeholder="/SOPAN转存"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">临时目录</label>
                                    <input v-model="configForm.file_path_temp" 
                                           type="text" 
                                           placeholder="/SOPAN临时"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">禁用关键词</label>
                                <input v-model="configForm.banned_keywords" 
                                       type="text" 
                                       placeholder="广告,推广,违规,用逗号分隔"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <p class="mt-1 text-sm text-gray-500">包含这些关键词的文件将被自动删除</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 百度网盘配置 -->
                    <div v-if="currentPlatform.key === 'baidu'" class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-3">百度网盘配置</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Cookie</label>
                                <textarea v-model="configForm.cookie" 
                                         placeholder="请输入百度网盘的Cookie信息"
                                         class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                         rows="4"></textarea>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">转存目录</label>
                                    <input v-model="configForm.file_path" 
                                           type="text" 
                                           placeholder="SOPAN转存"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">临时目录</label>
                                    <input v-model="configForm.file_path_temp" 
                                           type="text" 
                                           placeholder="SOPAN临时"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">禁用关键词</label>
                                <input v-model="configForm.banned_keywords" 
                                       type="text" 
                                       placeholder="广告,推广,违规,用逗号分隔"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 阿里云盘配置 -->
                    <div v-if="currentPlatform.key === 'aliyun'" class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-3">阿里云盘配置</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Refresh Token</label>
                                <textarea v-model="configForm.refresh_token" 
                                         placeholder="请输入阿里云盘的Refresh Token"
                                         class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                         rows="3"></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">转存目录</label>
                                <input v-model="configForm.file_path" 
                                       type="text" 
                                       placeholder="SOPAN转存"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 按钮组 -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                        <button type="button" 
                                @click="closeConfigModal" 
                                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                            取消
                        </button>
                        <button type="submit" 
                                :disabled="isSaving"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors">
                            {{ isSaving ? '保存中...' : '保存配置' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 测试结果模态框 -->
    <div v-if="showTestModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">测试结果</h3>
                    <button @click="closeTestModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div v-if="testResult.loading" class="text-center py-8">
                    <div class="loading-spinner mx-auto mb-4"></div>
                    <p class="text-gray-600">测试中，请稍候...</p>
                </div>
                
                <div v-else class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center"
                             :class="testResult.success ? 'bg-green-100' : 'bg-red-100'">
                            <svg v-if="testResult.success" class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <svg v-else class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium" :class="testResult.success ? 'text-green-600' : 'text-red-600'">
                                {{ testResult.success ? '测试成功' : '测试失败' }}
                            </p>
                            <p class="text-sm text-gray-600">{{ testResult.message }}</p>
                        </div>
                    </div>
                    
                    <div v-if="testResult.details" class="bg-gray-50 rounded-md p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">详细信息</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li v-for="detail in testResult.details" :key="detail">{{ detail }}</li>
                        </ul>
                    </div>
                </div>
                
                <div class="flex justify-end mt-6">
                    <button @click="closeTestModal" 
                            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
const { createApp } = Vue;

createApp({
    data() {
        return {
            platforms: <?= json_encode($platforms) ?>,
            showConfigModal: false,
            showTestModal: false,
            currentPlatform: null,
            configForm: {
                status: false,
                cookie: '',
                refresh_token: '',
                file_path: '',
                file_path_temp: '',
                banned_keywords: ''
            },
            testResult: {
                loading: false,
                success: false,
                message: '',
                details: []
            },
            isSaving: false
        };
    },
    
    methods: {
        editPlatform(platform) {
            this.currentPlatform = platform;
            this.resetConfigForm();
            this.loadPlatformConfig(platform.key);
            this.showConfigModal = true;
        },
        
        resetConfigForm() {
            this.configForm = {
                status: false,
                cookie: '',
                refresh_token: '',
                file_path: '',
                file_path_temp: '',
                banned_keywords: ''
            };
        },
        
        async loadPlatformConfig(platformKey) {
            try {
                const response = await axios.get(`${window.SOPAN_CONFIG.baseUrl}/admin/config/platform/${platformKey}`);
                if (response.data.success) {
                    const config = response.data.data;
                    this.configForm.status = config.status === '1';
                    this.configForm.cookie = config.cookie || '';
                    this.configForm.refresh_token = config.refresh_token || '';
                    this.configForm.file_path = config.file_path || '';
                    this.configForm.file_path_temp = config.file_path_temp || '';
                    this.configForm.banned_keywords = config.banned_keywords || '';
                }
            } catch (error) {
                console.error('加载配置失败:', error);
                Utils.showMessage('加载配置失败', 'error');
            }
        },
        
        async saveConfig() {
            this.isSaving = true;
            try {
                const configData = {
                    ...this.configForm,
                    status: this.configForm.status ? '1' : '0'
                };
                
                const response = await axios.post(
                    `${window.SOPAN_CONFIG.baseUrl}/admin/config/platform/${this.currentPlatform.key}`,
                    configData
                );
                
                if (response.data.success) {
                    Utils.showMessage('配置保存成功', 'success');
                    this.closeConfigModal();
                    this.loadPlatforms(); // 重新加载平台列表
                } else {
                    Utils.showMessage(response.data.message || '保存失败', 'error');
                }
            } catch (error) {
                console.error('保存配置失败:', error);
                Utils.showMessage('保存配置失败', 'error');
            } finally {
                this.isSaving = false;
            }
        },
        
        closeConfigModal() {
            this.showConfigModal = false;
            this.currentPlatform = null;
            this.resetConfigForm();
        },
        
        async testPlatform(platform) {
            this.currentPlatform = platform;
            this.testResult = {
                loading: true,
                success: false,
                message: '',
                details: []
            };
            this.showTestModal = true;
            
            try {
                const response = await axios.post(`${window.SOPAN_CONFIG.baseUrl}/admin/config/test/${platform.key}`);
                
                this.testResult.loading = false;
                this.testResult.success = response.data.success;
                this.testResult.message = response.data.message;
                this.testResult.details = response.data.details || [];
                
            } catch (error) {
                console.error('测试平台失败:', error);
                this.testResult.loading = false;
                this.testResult.success = false;
                this.testResult.message = '测试请求失败';
            }
        },
        
        closeTestModal() {
            this.showTestModal = false;
            this.currentPlatform = null;
            this.testResult = {
                loading: false,
                success: false,
                message: '',
                details: []
            };
        },
        
        async loadPlatforms() {
            try {
                const response = await axios.get(`${window.SOPAN_CONFIG.baseUrl}/admin/config/platforms`);
                if (response.data.success) {
                    this.platforms = response.data.data;
                }
            } catch (error) {
                console.error('加载平台列表失败:', error);
            }
        }
    }
}).mount('#configApp');
</script>
<?php $this->endSection(); ?>