<?php $this->setLayout('admin/layout'); ?>

<?php $this->startSection('title'); ?>转存记录 - SOPAN后台管理<?php $this->endSection(); ?>

<?php $this->startSection('content'); ?>
<div id="transferApp">
    <!-- 页面标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">转存记录</h1>
        <p class="mt-2 text-gray-600">查看和管理所有网盘转存记录</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">总转存次数</p>
                    <p class="text-2xl font-bold text-blue-600">{{ stats.total_transfers }}</p>
                </div>
                <div class="bg-blue-100 p-3 rounded-full">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">成功转存</p>
                    <p class="text-2xl font-bold text-green-600">{{ stats.success_transfers }}</p>
                </div>
                <div class="bg-green-100 p-3 rounded-full">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">失败转存</p>
                    <p class="text-2xl font-bold text-red-600">{{ stats.failed_transfers }}</p>
                </div>
                <div class="bg-red-100 p-3 rounded-full">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-600 text-sm">成功率</p>
                    <p class="text-2xl font-bold text-purple-600">{{ (stats.success_rate * 100).toFixed(1) }}%</p>
                </div>
                <div class="bg-purple-100 p-3 rounded-full">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 筛选和搜索 -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                <select v-model="filters.status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">全部状态</option>
                    <option value="success">成功</option>
                    <option value="failed">失败</option>
                    <option value="pending">处理中</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">平台</label>
                <select v-model="filters.platform" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">全部平台</option>
                    <option value="baidu">百度网盘</option>
                    <option value="quark">夸克网盘</option>
                    <option value="aliyun">阿里云盘</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">日期</label>
                <input v-model="filters.date" type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                <div class="flex">
                    <input v-model="filters.search" type="text" placeholder="搜索原链接或IP..." class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button @click="searchTransfers" class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700">
                        搜索
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 转存记录表格 -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原链接</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标平台</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="transfer in transfers" :key="transfer.id" class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ formatDateTime(transfer.start_time) }}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <div class="max-w-xs truncate" :title="transfer.original_url">
                                {{ transfer.original_url }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ getPlatformName(transfer.target_platform) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                  :class="getStatusClass(transfer.transfer_status)">
                                {{ getStatusText(transfer.transfer_status) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ transfer.user_ip }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button @click="viewDetail(transfer)" class="text-blue-600 hover:text-blue-900 mr-3">
                                详情
                            </button>
                            <button v-if="transfer.transfer_status === 'failed'" 
                                    @click="retryTransfer(transfer)" 
                                    class="text-green-600 hover:text-green-900 mr-3">
                                重试
                            </button>
                            <button @click="deleteTransfer(transfer)" class="text-red-600 hover:text-red-900">
                                删除
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        <div v-if="pagination.total > 0" class="bg-gray-50 px-6 py-3 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    共 {{ pagination.total }} 条记录
                </div>
                <div class="flex space-x-1">
                    <button @click="changePage(pagination.current_page - 1)" 
                            :disabled="pagination.current_page <= 1"
                            class="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50">
                        上一页
                    </button>
                    <button @click="changePage(pagination.current_page + 1)" 
                            :disabled="pagination.current_page >= pagination.last_page"
                            class="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50">
                        下一页
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
const { createApp } = Vue;

createApp({
    data() {
        return {
            transfers: <?= json_encode($transfers) ?>,
            stats: <?= json_encode($stats) ?>,
            pagination: <?= json_encode($pagination) ?>,
            filters: {
                status: '',
                platform: '',
                date: '',
                search: ''
            }
        };
    },
    
    methods: {
        formatDateTime(dateTime) {
            return new Date(dateTime).toLocaleString('zh-CN');
        },
        
        getPlatformName(platform) {
            const names = {
                'baidu': '百度网盘',
                'quark': '夸克网盘',
                'aliyun': '阿里云盘'
            };
            return names[platform] || platform;
        },
        
        getStatusClass(status) {
            const classes = {
                'success': 'bg-green-100 text-green-800',
                'failed': 'bg-red-100 text-red-800',
                'pending': 'bg-yellow-100 text-yellow-800',
                'processing': 'bg-blue-100 text-blue-800'
            };
            return classes[status] || 'bg-gray-100 text-gray-800';
        },
        
        getStatusText(status) {
            const texts = {
                'success': '成功',
                'failed': '失败',
                'pending': '等待中',
                'processing': '处理中'
            };
            return texts[status] || status;
        },
        
        async searchTransfers() {
            // 搜索转存记录
            try {
                const response = await axios.get(`${window.SOPAN_CONFIG.baseUrl}/admin/transfers`, {
                    params: this.filters
                });
                if (response.data.success) {
                    this.transfers = response.data.data.transfers;
                    this.pagination = response.data.data.pagination;
                }
            } catch (error) {
                Utils.showMessage('搜索失败', 'error');
            }
        },
        
        viewDetail(transfer) {
            // 查看转存详情
            console.log('查看详情:', transfer);
        },
        
        async retryTransfer(transfer) {
            if (confirm('确定要重试此转存吗？')) {
                try {
                    const response = await axios.post(`${window.SOPAN_CONFIG.baseUrl}/admin/transfers/${transfer.id}/retry`);
                    if (response.data.success) {
                        Utils.showMessage('重试请求已提交', 'success');
                        this.searchTransfers();
                    } else {
                        Utils.showMessage(response.data.message, 'error');
                    }
                } catch (error) {
                    Utils.showMessage('重试失败', 'error');
                }
            }
        },
        
        async deleteTransfer(transfer) {
            if (confirm('确定要删除此转存记录吗？')) {
                try {
                    const response = await axios.delete(`${window.SOPAN_CONFIG.baseUrl}/admin/transfers/${transfer.id}`);
                    if (response.data.success) {
                        Utils.showMessage('删除成功', 'success');
                        this.searchTransfers();
                    } else {
                        Utils.showMessage(response.data.message, 'error');
                    }
                } catch (error) {
                    Utils.showMessage('删除失败', 'error');
                }
            }
        },
        
        async changePage(page) {
            this.pagination.current_page = page;
            await this.searchTransfers();
        }
    }
}).mount('#transferApp');
</script>
<?php $this->endSection(); ?>