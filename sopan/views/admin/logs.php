<?php $this->setLayout('admin/layout'); ?>

<?php $this->startSection('title'); ?>系统日志 - SOPAN后台管理<?php $this->endSection(); ?>

<?php $this->startSection('content'); ?>
<div id="logsApp">
    <!-- 页面标题和操作 -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">系统日志</h1>
            <p class="mt-2 text-gray-600">查看和管理系统运行日志</p>
        </div>
        <div class="flex space-x-3">
            <button @click="refreshLogs" 
                    :disabled="isLoading"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors">
                <svg v-if="!isLoading" class="w-4 h-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <div v-else class="loading-spinner w-4 h-4 mr-2 inline-block"></div>
                刷新
            </button>
            <button @click="clearLogs" 
                    class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                清理日志
            </button>
        </div>
    </div>
    
    <!-- 日志类型和筛选 -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <!-- 日志类型选择 -->
            <div class="flex space-x-2">
                <button v-for="type in logTypes" :key="type.key"
                        @click="currentLogType = type.key"
                        class="px-4 py-2 rounded-md text-sm font-medium transition-colors"
                        :class="currentLogType === type.key 
                            ? 'bg-blue-600 text-white' 
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'">
                    {{ type.name }}
                    <span v-if="type.count > 0" class="ml-1 px-1.5 py-0.5 rounded-full text-xs"
                          :class="currentLogType === type.key ? 'bg-blue-500' : 'bg-gray-300'">
                        {{ type.count }}
                    </span>
                </button>
            </div>
            
            <!-- 筛选选项 -->
            <div class="flex items-center space-x-4">
                <!-- 日期筛选 -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-gray-600">日期：</label>
                    <input v-model="dateFilter" 
                           type="date" 
                           class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <!-- 级别筛选 -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-gray-600">级别：</label>
                    <select v-model="levelFilter" 
                            class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">全部</option>
                        <option value="error">错误</option>
                        <option value="warning">警告</option>
                        <option value="info">信息</option>
                        <option value="debug">调试</option>
                    </select>
                </div>
                
                <!-- 搜索 -->
                <div class="flex items-center space-x-2">
                    <input v-model="searchKeyword" 
                           type="text" 
                           placeholder="搜索日志内容..."
                           class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <button @click="searchLogs" 
                            class="px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm transition-colors">
                        搜索
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 日志列表 -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <!-- 表头 -->
        <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
            <div class="grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="col-span-2">时间</div>
                <div class="col-span-1">级别</div>
                <div class="col-span-1">类型</div>
                <div class="col-span-6">内容</div>
                <div class="col-span-1">IP地址</div>
                <div class="col-span-1">操作</div>
            </div>
        </div>
        
        <!-- 日志条目 -->
        <div class="divide-y divide-gray-200 max-h-96 overflow-y-auto">
            <div v-for="log in filteredLogs" :key="log.id" 
                 class="px-6 py-4 hover:bg-gray-50 transition-colors">
                <div class="grid grid-cols-12 gap-4 items-center">
                    <!-- 时间 -->
                    <div class="col-span-2 text-sm text-gray-900">
                        {{ formatDateTime(log.created_at) }}
                    </div>
                    
                    <!-- 级别 -->
                    <div class="col-span-1">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                              :class="getLevelClass(log.level)">
                            {{ getLevelText(log.level) }}
                        </span>
                    </div>
                    
                    <!-- 类型 -->
                    <div class="col-span-1">
                        <span class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">
                            {{ log.type }}
                        </span>
                    </div>
                    
                    <!-- 内容 -->
                    <div class="col-span-6">
                        <div class="text-sm text-gray-900 truncate" :title="log.message">
                            {{ log.message }}
                        </div>
                        <div v-if="log.context" class="text-xs text-gray-500 mt-1 truncate">
                            {{ log.context }}
                        </div>
                    </div>
                    
                    <!-- IP地址 -->
                    <div class="col-span-1 text-xs text-gray-500">
                        {{ log.ip || '-' }}
                    </div>
                    
                    <!-- 操作 -->
                    <div class="col-span-1">
                        <button @click="viewLogDetail(log)" 
                                class="text-blue-600 hover:text-blue-900 text-xs">
                            详情
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 空状态 -->
            <div v-if="filteredLogs.length === 0" class="px-6 py-12 text-center">
                <svg class="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <p class="mt-4 text-gray-500">暂无日志记录</p>
            </div>
        </div>
        
        <!-- 分页 -->
        <div v-if="pagination.total > pagination.per_page" 
             class="bg-gray-50 px-6 py-3 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示第 {{ pagination.from }} 到 {{ pagination.to }} 条记录，共 {{ pagination.total }} 条
                </div>
                <div class="flex space-x-1">
                    <button @click="changePage(pagination.current_page - 1)" 
                            :disabled="pagination.current_page <= 1"
                            class="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100">
                        上一页
                    </button>
                    <button v-for="page in paginationPages" :key="page"
                            @click="changePage(page)"
                            class="px-3 py-1 text-sm border border-gray-300 rounded-md"
                            :class="page === pagination.current_page ? 'bg-blue-600 text-white border-blue-600' : 'hover:bg-gray-100'">
                        {{ page }}
                    </button>
                    <button @click="changePage(pagination.current_page + 1)" 
                            :disabled="pagination.current_page >= pagination.last_page"
                            class="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100">
                        下一页
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 日志详情模态框 -->
    <div v-if="showDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">日志详情</h3>
                    <button @click="closeDetailModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div v-if="selectedLog" class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">时间</label>
                            <p class="text-sm text-gray-900">{{ formatDateTime(selectedLog.created_at) }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">级别</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                  :class="getLevelClass(selectedLog.level)">
                                {{ getLevelText(selectedLog.level) }}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">类型</label>
                            <p class="text-sm text-gray-900">{{ selectedLog.type }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">IP地址</label>
                            <p class="text-sm text-gray-900">{{ selectedLog.ip || '-' }}</p>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">消息内容</label>
                        <p class="text-sm text-gray-900 bg-gray-50 p-4 rounded-md">{{ selectedLog.message }}</p>
                    </div>
                    
                    <div v-if="selectedLog.context">
                        <label class="block text-sm font-medium text-gray-700 mb-1">上下文信息</label>
                        <pre class="text-sm text-gray-900 bg-gray-50 p-4 rounded-md overflow-x-auto">{{ selectedLog.context }}</pre>
                    </div>
                    
                    <div v-if="selectedLog.stack_trace">
                        <label class="block text-sm font-medium text-gray-700 mb-1">堆栈跟踪</label>
                        <pre class="text-xs text-gray-700 bg-red-50 p-4 rounded-md overflow-x-auto max-h-64 overflow-y-auto">{{ selectedLog.stack_trace }}</pre>
                    </div>
                </div>
                
                <div class="flex justify-end mt-6">
                    <button @click="closeDetailModal" 
                            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
const { createApp } = Vue;

createApp({
    data() {
        return {
            logs: <?= json_encode($logs) ?>,
            logTypes: <?= json_encode($log_types) ?>,
            pagination: <?= json_encode($pagination) ?>,
            currentLogType: 'all',
            dateFilter: '',
            levelFilter: '',
            searchKeyword: '',
            isLoading: false,
            showDetailModal: false,
            selectedLog: null
        };
    },
    
    computed: {
        filteredLogs() {
            let filtered = [...this.logs];
            
            // 按类型筛选
            if (this.currentLogType !== 'all') {
                filtered = filtered.filter(log => log.type === this.currentLogType);
            }
            
            // 按级别筛选
            if (this.levelFilter) {
                filtered = filtered.filter(log => log.level === this.levelFilter);
            }
            
            // 按日期筛选
            if (this.dateFilter) {
                filtered = filtered.filter(log => 
                    log.created_at.startsWith(this.dateFilter)
                );
            }
            
            // 按关键词搜索
            if (this.searchKeyword.trim()) {
                const keyword = this.searchKeyword.trim().toLowerCase();
                filtered = filtered.filter(log => 
                    log.message.toLowerCase().includes(keyword) ||
                    (log.context && log.context.toLowerCase().includes(keyword))
                );
            }
            
            return filtered;
        },
        
        paginationPages() {
            const pages = [];
            const current = this.pagination.current_page;
            const last = this.pagination.last_page;
            
            let start = Math.max(1, current - 2);
            let end = Math.min(last, current + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
        }
    },
    
    methods: {
        formatDateTime(dateTime) {
            return new Date(dateTime).toLocaleString('zh-CN');
        },
        
        getLevelClass(level) {
            const classes = {
                'error': 'bg-red-100 text-red-800',
                'warning': 'bg-yellow-100 text-yellow-800',
                'info': 'bg-blue-100 text-blue-800',
                'debug': 'bg-gray-100 text-gray-800'
            };
            return classes[level] || 'bg-gray-100 text-gray-800';
        },
        
        getLevelText(level) {
            const texts = {
                'error': '错误',
                'warning': '警告',
                'info': '信息',
                'debug': '调试'
            };
            return texts[level] || level;
        },
        
        async refreshLogs() {
            this.isLoading = true;
            try {
                const response = await axios.get(`${window.SOPAN_CONFIG.baseUrl}/admin/logs`, {
                    params: {
                        type: this.currentLogType,
                        level: this.levelFilter,
                        date: this.dateFilter,
                        search: this.searchKeyword,
                        page: this.pagination.current_page
                    }
                });
                
                if (response.data.success) {
                    this.logs = response.data.data.logs;
                    this.pagination = response.data.data.pagination;
                    this.logTypes = response.data.data.log_types;
                }
            } catch (error) {
                console.error('刷新日志失败:', error);
                Utils.showMessage('刷新日志失败', 'error');
            } finally {
                this.isLoading = false;
            }
        },
        
        async searchLogs() {
            this.pagination.current_page = 1;
            await this.refreshLogs();
        },
        
        async changePage(page) {
            if (page >= 1 && page <= this.pagination.last_page) {
                this.pagination.current_page = page;
                await this.refreshLogs();
            }
        },
        
        async clearLogs() {
            if (!confirm('确定要清理日志吗？此操作不可恢复！')) {
                return;
            }
            
            try {
                const response = await axios.post(`${window.SOPAN_CONFIG.baseUrl}/admin/logs/clear`, {
                    type: this.currentLogType,
                    date: this.dateFilter
                });
                
                if (response.data.success) {
                    Utils.showMessage('日志清理成功', 'success');
                    await this.refreshLogs();
                } else {
                    Utils.showMessage(response.data.message || '清理失败', 'error');
                }
            } catch (error) {
                console.error('清理日志失败:', error);
                Utils.showMessage('清理日志失败', 'error');
            }
        },
        
        viewLogDetail(log) {
            this.selectedLog = log;
            this.showDetailModal = true;
        },
        
        closeDetailModal() {
            this.showDetailModal = false;
            this.selectedLog = null;
        }
    },
    
    watch: {
        currentLogType() {
            this.refreshLogs();
        },
        
        levelFilter() {
            this.refreshLogs();
        },
        
        dateFilter() {
            this.refreshLogs();
        }
    }
}).mount('#logsApp');
</script>
<?php $this->endSection(); ?>