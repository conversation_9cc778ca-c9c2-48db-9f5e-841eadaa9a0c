<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->escape($page_title ?? '后台管理') ?> - <?= $this->escape($site_name) ?></title>
    
    <!-- Tailwind CSS - 本地化 -->
    <script src="/sopan/public/assets/js/tailwind.min.js"></script>
    
    <!-- Vue 3 - 本地化 -->
    <script src="/sopan/public/assets/js/vue.global.min.js"></script>
    
    <!-- Axios - 本地化 -->
    <script src="/sopan/public/assets/js/axios.min.js"></script>
    
    <!-- Font Awesome 图标库 - 本地化 -->
    <link rel="stylesheet" href="/sopan/public/assets/css/fontawesome.min.css">
    
    <!-- Chart.js - 本地化 -->
    <script src="/sopan/public/assets/js/chart.min.js"></script>
    
    <style>
        /* 自定义样式 */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        
        .main-content {
            transition: margin-left 0.3s ease-in-out;
        }
        
        /* 深色侧边栏 */
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 活跃菜单项 */
        .menu-item.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-right: 3px solid #fff;
        }
        
        /* 数据卡片 */
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .stat-card.blue {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stat-card.green {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        
        .stat-card.orange {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        
        /* 表格样式 */
        .admin-table {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        /* 响应式隐藏 */
        @media (max-width: 768px) {
            .mobile-hide {
                display: none;
            }
        }
        
        /* 加载动画 */
        .spinner {
            border: 2px solid #f3f4f6;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div id="adminApp">
        <!-- 侧边栏 -->
        <div :class="['fixed inset-y-0 left-0 z-50 w-64 sidebar sidebar-transition', sidebarOpen ? 'transform-none' : 'transform -translate-x-full']">
            <div class="flex items-center justify-center h-16 text-white border-b border-white/20">
                <h1 class="text-xl font-bold">
                    <i class="fas fa-cog mr-2"></i>
                    <?= $this->escape($site_name) ?>
                </h1>
            </div>
            
            <nav class="mt-8">
                <div class="px-4 space-y-2">
                    <a href="<?= $admin_url ?>/dashboard" 
                       :class="['flex items-center px-4 py-3 text-white hover:bg-white/20 rounded-lg menu-item', currentPage === 'dashboard' ? 'active' : '']">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        仪表板
                    </a>
                    
                    <a href="<?= $admin_url ?>/config" 
                       :class="['flex items-center px-4 py-3 text-white hover:bg-white/20 rounded-lg menu-item', currentPage === 'config' ? 'active' : '']">
                        <i class="fas fa-cloud mr-3"></i>
                        网盘配置
                    </a>
                    
                    <a href="<?= $admin_url ?>/settings" 
                       :class="['flex items-center px-4 py-3 text-white hover:bg-white/20 rounded-lg menu-item', currentPage === 'settings' ? 'active' : '']">
                        <i class="fas fa-cogs mr-3"></i>
                        系统设置
                    </a>
                    
                    <a href="<?= $admin_url ?>/logs" 
                       :class="['flex items-center px-4 py-3 text-white hover:bg-white/20 rounded-lg menu-item', currentPage === 'logs' ? 'active' : '']">
                        <i class="fas fa-file-alt mr-3"></i>
                        系统日志
                    </a>
                    
                    <a href="<?= $admin_url ?>/transfers" 
                       :class="['flex items-center px-4 py-3 text-white hover:bg-white/20 rounded-lg menu-item', currentPage === 'transfers' ? 'active' : '']">
                        <i class="fas fa-exchange-alt mr-3"></i>
                        转存记录
                    </a>
                </div>
            </nav>
            
            <!-- 底部用户信息 -->
            <div class="absolute bottom-0 w-64 p-4 border-t border-white/20">
                <div class="flex items-center text-white">
                    <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-user text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="text-sm font-medium"><?= $current_admin['username'] ?? '管理员' ?></div>
                        <div class="text-xs opacity-75"><?= $current_admin['role'] ?? 'admin' ?></div>
                    </div>
                    <a href="<?= $admin_url ?>/logout" class="text-white/80 hover:text-white ml-2" title="退出登录">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 移动端遮罩 -->
        <div v-if="sidebarOpen" @click="sidebarOpen = false" 
             class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"></div>
        
        <!-- 主内容区域 -->
        <div :class="['main-content', 'lg:ml-64']">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex justify-between items-center px-4 py-4">
                    <div class="flex items-center">
                        <!-- 移动端菜单按钮 -->
                        <button @click="sidebarOpen = !sidebarOpen" 
                                class="lg:hidden text-gray-600 hover:text-gray-900 mr-4">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        
                        <h2 class="text-xl font-semibold text-gray-800">
                            <?= $this->escape($page_title ?? '后台管理') ?>
                        </h2>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- 通知按钮 -->
                        <button class="text-gray-600 hover:text-gray-900 relative">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                                3
                            </span>
                        </button>
                        
                        <!-- 用户菜单 -->
                        <div class="relative" v-data="{ open: false }">
                            <button @click="open = !open" 
                                    class="flex items-center text-gray-600 hover:text-gray-900">
                                <img class="w-8 h-8 rounded-full bg-gray-300" 
                                     src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'%3E%3Cpath stroke='%23999' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'/%3E%3C/svg%3E" 
                                     alt="Avatar">
                                <span class="ml-2 text-sm font-medium mobile-hide"><?= $current_admin['username'] ?? '管理员' ?></span>
                                <i class="fas fa-chevron-down ml-1 text-xs mobile-hide"></i>
                            </button>
                            
                            <div v-show="open" @click.away="open = false" 
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1">
                                <a href="<?= $admin_url ?>/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i>个人设置
                                </a>
                                <a href="<?= $admin_url ?>/logout" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 主要内容 -->
            <main class="p-6">
                <?php $this->section('content', '页面内容'); ?>
            </main>
            
            <!-- 页脚 -->
            <footer class="bg-white border-t border-gray-200 py-4 px-6">
                <div class="flex flex-col md:flex-row justify-between items-center text-sm text-gray-500">
                    <div>
                        © <?= date('Y') ?> <?= $this->escape($site_name) ?>. All rights reserved.
                    </div>
                    <div class="mt-2 md:mt-0">
                        Version 1.0.0 | PHP <?= PHP_VERSION ?>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- 通用JavaScript -->
    <script>
        // 全局配置
        window.ADMIN_CONFIG = {
            baseUrl: '<?= $base_url ?>',
            adminUrl: '<?= $admin_url ?>',
            apiUrl: '<?= $api_url ?>',
            siteName: '<?= $this->escape($site_name) ?>'
        };
        

    </script>
    
    <!-- Vue.js 应用 -->
    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    sidebarOpen: false,
                    currentPage: this.getCurrentPage(),
                    loading: false,
                    notifications: []
                };
            },
            
            mounted() {
                // 监听窗口大小变化
                window.addEventListener('resize', () => {
                    if (window.innerWidth >= 1024) {
                        this.sidebarOpen = false;
                    }
                });
                
                // 检查通知
                this.checkNotifications();
            },
            
            methods: {
                getCurrentPage() {
                    const path = window.location.pathname;
                    if (path.includes('/dashboard')) return 'dashboard';
                    if (path.includes('/config')) return 'config';
                    if (path.includes('/settings')) return 'settings';
                    if (path.includes('/logs')) return 'logs';
                    if (path.includes('/transfers')) return 'transfers';
                    return 'dashboard';
                },
                
                async checkNotifications() {
                    try {
                        // 这里可以添加检查通知的逻辑
                        console.log('Checking notifications...');
                    } catch (error) {
                        console.error('Failed to check notifications:', error);
                    }
                },
                
                showMessage(message, type = 'info') {
                    const notification = {
                        id: Date.now(),
                        message,
                        type,
                        show: true
                    };
                    
                    this.notifications.push(notification);
                    
                    setTimeout(() => {
                        notification.show = false;
                        setTimeout(() => {
                            const index = this.notifications.indexOf(notification);
                            if (index > -1) {
                                this.notifications.splice(index, 1);
                            }
                        }, 300);
                    }, 3000);
                },
                
                formatDate(dateString) {
                    return new Date(dateString).toLocaleString('zh-CN');
                },
                
                formatFileSize(bytes) {
                    if (!bytes) return '0 B';
                    const k = 1024;
                    const sizes = ['B', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }
            }
        }).mount('#adminApp');
    </script>
    
    <!-- 页面特定脚本 -->
    <?php $this->section('scripts', ''); ?>
    

</body>
</html>