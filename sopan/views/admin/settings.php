<?php $this->setLayout('admin/layout'); ?>

<?php $this->startSection('title'); ?>系统设置 - SOPAN后台管理<?php $this->endSection(); ?>

<?php $this->startSection('content'); ?>
<div id="settingsApp">
    <!-- 页面标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">系统设置</h1>
        <p class="mt-2 text-gray-600">管理系统基础配置、API设置和安全设置等</p>
    </div>
    
    <!-- 设置标签页 -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <!-- 标签导航 -->
        <div class="border-b border-gray-200">
            <nav class="flex -mb-px">
                <button v-for="tab in tabs" :key="tab.key"
                        @click="currentTab = tab.key"
                        class="px-6 py-3 text-sm font-medium border-b-2 transition-colors"
                        :class="currentTab === tab.key 
                            ? 'border-blue-500 text-blue-600 bg-blue-50' 
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                    {{ tab.name }}
                </button>
            </nav>
        </div>
        
        <!-- 标签内容 -->
        <div class="p-6">
            <form @submit.prevent="saveSettings" v-show="!isLoading">
                <!-- 基础设置 -->
                <div v-if="currentTab === 'basic'" class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">基础设置</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">网站名称</label>
                            <input v-model="configs.basic.site_name" 
                                   type="text" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">网站关键词</label>
                            <input v-model="configs.basic.site_keywords" 
                                   type="text" 
                                   placeholder="用逗号分隔多个关键词"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">网站描述</label>
                        <textarea v-model="configs.basic.site_description" 
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                </div>
                
                <!-- API设置 -->
                <div v-if="currentTab === 'api'" class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">API设置</h3>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Panseeker API地址</label>
                        <input v-model="configs.api.panseeker_api_url" 
                               type="url" 
                               placeholder="https://api.panseeker.com/api/search"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-sm text-gray-500">用于搜索网盘资源的API接口地址</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API密钥</label>
                        <div class="relative">
                            <input v-model="configs.api.panseeker_api_key" 
                                   :type="showApiKey ? 'text' : 'password'"
                                   placeholder="请输入API密钥"
                                   class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <button type="button" 
                                    @click="showApiKey = !showApiKey"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <svg v-if="showApiKey" class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L9.878 9.878zm0 0a3 3 0 013-3m-3 3l-8.47 8.47M7.05 4.05L20.95 17.95"></path>
                                </svg>
                                <svg v-else class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">如果API需要认证，请输入相应的密钥</p>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <h4 class="text-sm font-medium text-yellow-800">注意</h4>
                                <p class="text-sm text-yellow-700 mt-1">API配置更改后可能需要几分钟才能生效，请耐心等待。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 安全设置 -->
                <div v-if="currentTab === 'security'" class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">安全设置</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">每IP每日最大搜索次数</label>
                            <input v-model="configs.security.max_search_per_ip" 
                                   type="number" 
                                   min="1"
                                   max="10000"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <p class="mt-1 text-sm text-gray-500">防止恶意刷量，0表示不限制</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">搜索间隔限制（秒）</label>
                            <input v-model="configs.security.search_interval" 
                                   type="number" 
                                   min="0"
                                   max="60"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <p class="mt-1 text-sm text-gray-500">同一IP连续搜索的最小间隔时间</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="flex items-center">
                                <input v-model="configs.security.enable_ip_whitelist" 
                                       type="checkbox" 
                                       class="form-checkbox text-blue-600">
                                <span class="ml-2 text-gray-700">启用IP白名单</span>
                            </label>
                            <p class="mt-1 text-sm text-gray-500">只允许白名单中的IP访问</p>
                        </div>
                        
                        <div>
                            <label class="flex items-center">
                                <input v-model="configs.security.enable_captcha" 
                                       type="checkbox" 
                                       class="form-checkbox text-blue-600">
                                <span class="ml-2 text-gray-700">启用验证码</span>
                            </label>
                            <p class="mt-1 text-sm text-gray-500">搜索时需要输入验证码</p>
                        </div>
                    </div>
                </div>
                
                <!-- 系统设置 -->
                <div v-if="currentTab === 'system'" class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">系统设置</h3>
                    
                    <!-- 后台路径设置 -->
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
                        <h4 class="text-sm font-medium text-blue-800 mb-3">后台访问路径</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">后台访问路径</label>
                                <div class="flex">
                                    <span class="inline-flex items-center px-3 py-2 border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm rounded-l-md">
                                        {{ window.location.origin }}/
                                    </span>
                                    <input v-model="configs.system.admin_path" 
                                           type="text" 
                                           pattern="[a-zA-Z0-9_-]+"
                                           placeholder="admin"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <p class="mt-1 text-sm text-blue-600">当前后台地址：{{ window.location.origin + '/' + configs.system.admin_path }}</p>
                            </div>
                            <div class="flex items-end">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">路径验证</label>
                                    <button type="button" 
                                            @click="validateAdminPath"
                                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                                        验证路径
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="flex items-start space-x-2">
                                <svg class="w-5 h-5 text-yellow-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <div class="text-sm text-yellow-700">
                                    <p><strong>注意：</strong>修改后台路径后，请使用新的地址访问后台。路径只能包含字母、数字、下划线和中横线。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="flex items-center">
                                <input v-model="configs.system.enable_cache" 
                                       type="checkbox" 
                                       class="form-checkbox text-blue-600">
                                <span class="ml-2 text-gray-700">启用搜索结果缓存</span>
                            </label>
                            <p class="mt-1 text-sm text-gray-500">缓存搜索结果以提高响应速度</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">缓存过期时间（秒）</label>
                            <input v-model="configs.system.cache_expire" 
                                   type="number" 
                                   min="60"
                                   max="86400"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="flex items-center">
                                <input v-model="configs.system.enable_log" 
                                       type="checkbox" 
                                       class="form-checkbox text-blue-600">
                                <span class="ml-2 text-gray-700">启用详细日志</span>
                            </label>
                            <p class="mt-1 text-sm text-gray-500">记录详细的操作日志</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">日志保留天数</label>
                            <input v-model="configs.system.log_retention_days" 
                                   type="number" 
                                   min="1"
                                   max="365"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>
                
                <!-- 保存按钮 -->
                <div class="mt-8 flex justify-end space-x-3">
                    <button type="button" 
                            @click="loadSettings" 
                            class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        重置
                    </button>
                    <button type="submit" 
                            :disabled="isSaving"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
                        {{ isSaving ? '保存中...' : '保存设置' }}
                    </button>
                </div>
            </form>
            
            <!-- 加载状态 -->
            <div v-if="isLoading" class="text-center py-12">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-gray-600">加载配置中...</p>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
const { createApp } = Vue;

createApp({
    data() {
        return {
            currentTab: 'basic',
            isLoading: true,
            isSaving: false,
            showApiKey: false,
            tabs: [
                { key: 'basic', name: '基础设置' },
                { key: 'api', name: 'API设置' },
                { key: 'security', name: '安全设置' },
                { key: 'system', name: '系统设置' }
            ],
            configs: {
                basic: {
                    site_name: '',
                    site_description: '',
                    site_keywords: ''
                },
                api: {
                    panseeker_api_url: '',
                    panseeker_api_key: ''
                },
                security: {
                    max_search_per_ip: 100,
                    search_interval: 1,
                    enable_ip_whitelist: false,
                    enable_captcha: false
                },
                system: {
                    admin_path: 'admin',
                    enable_cache: true,
                    cache_expire: 3600,
                    enable_log: true,
                    log_retention_days: 30
                }
            }
        };
    },
    
    mounted() {
        this.loadSettings();
    },
    
    methods: {
        async loadSettings() {
            this.isLoading = true;
            try {
                // 使用PHP传递的设置数据（如果有的话）
                <?php if (isset($settings)): ?>
                const serverSettings = <?= json_encode($settings) ?>;
                this.parseServerSettings(serverSettings);
                <?php endif; ?>
            } catch (error) {
                console.error('加载设置失败:', error);
                Utils.showMessage('加载设置失败', 'error');
            } finally {
                this.isLoading = false;
            }
        },
        
        parseServerSettings(serverSettings) {
            // 解析服务器返回的设置数据
            Object.keys(serverSettings).forEach(key => {
                // 基础设置
                if (['site_name', 'site_description', 'site_keywords'].includes(key)) {
                    this.configs.basic[key] = serverSettings[key] || this.configs.basic[key];
                }
                // API设置
                else if (['panseeker_api_url', 'panseeker_api_key'].includes(key)) {
                    this.configs.api[key] = serverSettings[key] || this.configs.api[key];
                }
                // 系统设置
                else if (['admin_path', 'cache_expire_time', 'enable_search_log', 'enable_error_report'].includes(key)) {
                    if (key === 'cache_expire_time') {
                        this.configs.system.cache_expire = parseInt(serverSettings[key]) || this.configs.system.cache_expire;
                    } else if (key === 'enable_search_log') {
                        this.configs.system.enable_log = serverSettings[key] === '1';
                    } else if (key === 'enable_error_report') {
                        this.configs.system.enable_log = serverSettings[key] === '1';
                    } else {
                        this.configs.system[key] = serverSettings[key] || this.configs.system[key];
                    }
                }
            });
        },
        
        async saveSettings() {
            this.isSaving = true;
            try {
                const configData = {};
                
                // 构建要保存的配置数据
                Object.keys(this.configs).forEach(group => {
                    Object.keys(this.configs[group]).forEach(key => {
                        configData[key] = {
                            value: this.configs[group][key],
                            group: group
                        };
                    });
                });
                
                const response = await axios.post(`${window.SOPAN_CONFIG.baseUrl}/admin/settings`, {
                    ...configData
                });
                
                if (response.data.success) {
                    Utils.showMessage('设置保存成功', 'success');
                    
                    // 如果修改了后台路径，则重定向到新地址
                    if (response.data.redirect_url) {
                        setTimeout(() => {
                            window.location.href = response.data.redirect_url;
                        }, 1500);
                        Utils.showMessage('后台路径已修改，正在跳转...', 'info');
                    }
                } else {
                    Utils.showMessage(response.data.message || '保存失败', 'error');
                }
            } catch (error) {
                console.error('保存设置失败:', error);
                Utils.showMessage('保存设置失败', 'error');
            } finally {
                this.isSaving = false;
            }
        },
        
        async validateAdminPath() {
            const path = this.configs.system.admin_path;
            if (!path) {
                Utils.showMessage('请输入后台路径', 'warning');
                return;
            }
            
            if (!/^[a-zA-Z0-9_-]+$/.test(path)) {
                Utils.showMessage('后台路径只能包含字母、数字、下划线和中横线', 'error');
                return;
            }
            
            const reservedPaths = ['api', 'install', 'qrcode', 'public', 'assets', 'search', 'about', 'help', 'contact', 'terms', 'privacy', 'sitemap'];
            if (reservedPaths.includes(path.toLowerCase())) {
                Utils.showMessage('该路径为系统保留路径，请选择其他名称', 'error');
                return;
            }
            
            Utils.showMessage('路径验证通过，可以使用', 'success');
        }
    }
}).mount('#settingsApp');
</script>
<?php $this->endSection(); ?>