<?php $this->setLayout('admin/layout'); ?>

<?php $this->startSection('title'); ?>错误报告 - SOPAN后台管理<?php $this->endSection(); ?>

<?php $this->startSection('content'); ?>
<div id="errorReportsApp">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">错误报告</h1>
            <p class="mt-2 text-gray-600">查看和管理前端错误报告</p>
        </div>
        <div class="flex space-x-3">
            <button @click="refreshReports" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                刷新
            </button>
        </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">总错误数</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.total_reports }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">未解决</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.unresolved_reports }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">今日新增</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.today_reports }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 筛选 -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-4">
                <select v-model="filters.type" class="px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">所有类型</option>
                    <option value="JavaScript Error">JavaScript错误</option>
                    <option value="Promise Error">Promise错误</option>
                    <option value="API Error">API错误</option>
                    <option value="Network Error">网络错误</option>
                </select>
                
                <select v-model="filters.resolved" class="px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">所有状态</option>
                    <option value="0">未解决</option>
                    <option value="1">已解决</option>
                </select>
                
                <input v-model="filters.date" type="date" class="px-3 py-2 border border-gray-300 rounded-md">
            </div>
        </div>
    </div>

    <!-- 错误报告列表 -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="divide-y divide-gray-200">
            <div v-for="report in reports" :key="report.id" class="p-6 hover:bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                  :class="report.is_resolved ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                                {{ report.is_resolved ? '已解决' : '未解决' }}
                            </span>
                            <span class="text-sm text-gray-500">{{ report.error_type }}</span>
                            <span class="text-sm text-gray-500">{{ formatDateTime(report.created_at) }}</span>
                        </div>
                        
                        <p class="text-sm text-gray-900 font-medium mb-2">{{ report.error_message }}</p>
                        
                        <div class="text-xs text-gray-500 space-y-1">
                            <p v-if="report.page_url"><strong>页面:</strong> {{ report.page_url }}</p>
                            <p><strong>IP:</strong> {{ report.user_ip }}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <button @click="viewDetail(report)" 
                                class="text-blue-600 hover:text-blue-900 text-sm">
                            详情
                        </button>
                        <button v-if="!report.is_resolved" 
                                @click="resolveError(report)" 
                                class="text-green-600 hover:text-green-900 text-sm">
                            标记已解决
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div v-if="showDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold">错误详情</h3>
            <button @click="closeDetail" class="text-gray-400 hover:text-gray-600">×</button>
        </div>
        
        <div v-if="selectedReport" class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-1">错误类型</label>
                    <p class="text-sm">{{ selectedReport.error_type }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">发生时间</label>
                    <p class="text-sm">{{ formatDateTime(selectedReport.created_at) }}</p>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-1">错误消息</label>
                <p class="text-sm bg-gray-50 p-3 rounded">{{ selectedReport.error_message }}</p>
            </div>
            
            <div v-if="selectedReport.stack_trace">
                <label class="block text-sm font-medium mb-1">堆栈跟踪</label>
                <pre class="text-xs bg-red-50 p-3 rounded max-h-64 overflow-auto">{{ selectedReport.stack_trace }}</pre>
            </div>
        </div>
        
        <div class="flex justify-end mt-6">
            <button @click="closeDetail" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                关闭
            </button>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
const { createApp } = Vue;

createApp({
    data() {
        return {
            reports: <?= json_encode($reports) ?>,
            stats: <?= json_encode($stats) ?>,
            filters: {
                type: '',
                resolved: '',
                date: ''
            },
            showDetailModal: false,
            selectedReport: null
        };
    },
    
    methods: {
        formatDateTime(dateTime) {
            return new Date(dateTime).toLocaleString('zh-CN');
        },
        
        async refreshReports() {
            try {
                const response = await axios.get(`${window.SOPAN_CONFIG.baseUrl}/admin/error-reports`, {
                    params: this.filters
                });
                
                if (response.data.success) {
                    this.reports = response.data.data.reports;
                    this.stats = response.data.data.stats;
                }
            } catch (error) {
                Utils.showMessage('刷新失败', 'error');
            }
        },
        
        viewDetail(report) {
            this.selectedReport = report;
            this.showDetailModal = true;
        },
        
        closeDetail() {
            this.showDetailModal = false;
            this.selectedReport = null;
        },
        
        async resolveError(report) {
            const note = prompt('请输入解决方案说明（可选）:');
            if (note === null) return;
            
            try {
                const response = await axios.post(`${window.SOPAN_CONFIG.baseUrl}/admin/error-reports/${report.id}/resolve`, {
                    resolution_note: note
                });
                
                if (response.data.success) {
                    report.is_resolved = 1;
                    report.resolution_note = note;
                    Utils.showMessage('错误已标记为已解决', 'success');
                } else {
                    Utils.showMessage(response.data.message, 'error');
                }
            } catch (error) {
                Utils.showMessage('操作失败', 'error');
            }
        }
    },
    
    watch: {
        'filters.type'() { this.refreshReports(); },
        'filters.resolved'() { this.refreshReports(); },
        'filters.date'() { this.refreshReports(); }
    }
}).mount('#errorReportsApp');
</script>
<?php $this->endSection(); ?>