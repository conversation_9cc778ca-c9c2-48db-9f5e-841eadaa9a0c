<?php $this->setLayout('layout'); ?>

<?php $this->startSection('content'); ?>
<div class="min-h-96 flex items-center justify-center py-12">
    <div class="text-center">
        <div class="mb-8">
            <h1 class="text-9xl font-bold text-gray-300"><?= $error_code ?? '500' ?></h1>
        </div>
        
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">
                <?php if (($error_code ?? 500) == 404): ?>
                    页面未找到
                <?php else: ?>
                    系统错误
                <?php endif; ?>
            </h2>
            <p class="text-gray-600 max-w-md mx-auto">
                <?= $this->escape($error_message ?? '抱歉，页面出现了错误') ?>
            </p>
        </div>
        
        <div class="space-x-4">
            <a href="<?= $base_url ?>/" 
               class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                返回首页
            </a>
            <button onclick="history.back()" 
                    class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                返回上一页
            </button>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>