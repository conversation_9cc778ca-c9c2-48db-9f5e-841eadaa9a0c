<?php $this->setLayout('layout'); ?>

<?php $this->startSection('content'); ?>
<div id="searchApp">
    <!-- 搜索栏 -->
    <section class="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <form @submit.prevent="newSearch" class="flex items-center space-x-4">
                <div class="flex-1 relative">
                    <input v-model="searchKeyword" type="text" placeholder="请输入搜索关键词..." 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="flex items-center space-x-2">
                    <select v-model="searchType" class="px-3 py-3 border border-gray-300 rounded-lg">
                        <option value="all">全部</option>
                        <option value="quark">夸克网盘</option>
                        <option value="baidu">百度网盘</option>
                        <option value="aliyun">阿里云盘</option>
                    </select>
                    <button type="submit" :disabled="!searchKeyword.trim() || isSearching"
                            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50">
                        <span v-if="!isSearching">搜索</span>
                        <div v-else class="loading-spinner"></div>
                    </button>
                </div>
            </form>
        </div>
    </section>

    <!-- 搜索状态 -->
    <section class="bg-gray-50 border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h1 class="text-lg font-medium">搜索结果：{{ keyword }}</h1>
                    <div v-if="searchStatus" class="text-sm text-gray-600">
                        {{ searchStatusText }}
                    </div>
                </div>
                <div v-if="isSearching" class="flex items-center space-x-2">
                    <div class="search-progress w-32">
                        <div class="search-progress-bar" :style="{width: progress + '%'}"></div>
                    </div>
                    <span class="text-sm text-gray-600">{{ progress }}%</span>
                </div>
            </div>
        </div>
    </section>

    <!-- 平台筛选 -->
    <section class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">平台筛选：</span>
                <button v-for="type in filterTypes" :key="type.value"
                        @click="activeFilter = type.value"
                        :class="['px-3 py-1 text-sm rounded-full border', 
                                activeFilter === type.value ? 'bg-blue-100 border-blue-500 text-blue-700' : 'border-gray-300 text-gray-600 hover:border-gray-400']">
                    {{ type.label }} ({{ getResultCount(type.value) }})
                </button>
            </div>
        </div>
    </section>

    <!-- 搜索结果 -->
    <section class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- 结果列表 -->
            <div v-if="filteredResults.length > 0" class="space-y-4">
                <div v-for="(result, index) in filteredResults" :key="index"
                     class="search-result-item bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h3 class="font-medium text-gray-900">{{ result.title }}</h3>
                                <span :class="['platform-tag', result.platform]">{{ result.platform }}</span>
                            </div>
                            <div class="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                                <span>大小：{{ result.size }}</span>
                                <span>时间：{{ result.create_time }}</span>
                                <span>匹配度：{{ (result.match_score * 100).toFixed(0) }}%</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ result.code }}</code>
                                <button @click="copyLink(result.url)" 
                                        class="text-blue-600 hover:text-blue-800 text-sm">复制链接</button>
                                <button @click="generateQRCode(result)" 
                                        class="text-green-600 hover:text-green-800 text-sm">生成二维码</button>
                                <button @click="showTransfer(result)" 
                                        class="text-orange-600 hover:text-orange-800 text-sm">转存</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div v-else-if="!isSearching" class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.497.78-6.23 2.09l-.093.083m-.093-.083l.093.083A7.962 7.962 0 0112 15m0 0c2.34 0 4.497.78 6.23 2.09l.093.083m0 0A7.962 7.962 0 0112 21.001M12 21.001c0-2.34.78-4.497 2.09-6.23l.083-.093m-.083.093l.083-.093A7.962 7.962 0 0112 21.001"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">暂无搜索结果</h3>
                <p class="mt-1 text-sm text-gray-500">{{ searchCompleted ? '未找到相关资源，请尝试其他关键词' : '正在搜索中...' }}</p>
            </div>
        </div>
    </section>
</div>

<!-- QR码模态框 -->
<div v-if="showQRModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeQRModal">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">二维码</h3>
                <button @click="closeQRModal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div v-if="currentQRCode" class="text-center">
                <!-- 二维码图片 -->
                <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                    <img :src="currentQRCode.qr_url" 
                         :alt="'二维码 - ' + currentQRCode.result.title"
                         class="mx-auto max-w-full h-auto border border-gray-200 rounded">
                </div>
                
                <!-- 资源信息 -->
                <div class="mb-4 text-left">
                    <h4 class="font-medium text-gray-900 mb-2">资源信息</h4>
                    <div class="space-y-1 text-sm text-gray-600">
                        <p><span class="font-medium">标题：</span>{{ currentQRCode.result.title }}</p>
                        <p><span class="font-medium">平台：</span>{{ currentQRCode.result.platform }}</p>
                        <p v-if="currentQRCode.result.code"><span class="font-medium">提取码：</span>{{ currentQRCode.result.code }}</p>
                        <p><span class="font-medium">大小：</span>{{ currentQRCode.result.size }}</p>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-3">
                    <button @click="downloadQRCode" 
                            class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        下载二维码
                    </button>
                    <button @click="copyLink(currentQRCode.result.url)" 
                            class="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                        复制链接
                    </button>
                </div>
                
                <p class="mt-3 text-xs text-gray-500">
                    使用手机扫描二维码可快速获取网盘链接和提取码
                </p>
            </div>
        </div>
    </div>
</div>
<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
const { createApp } = Vue;

createApp({
    data() {
        return {
            keyword: '<?= $this->escape($keyword) ?>',
            searchKeyword: '<?= $this->escape($keyword) ?>',
            searchType: '<?= $this->escape($search_type) ?>',
            activeFilter: 'all',
            isSearching: false,
            searchCompleted: false,
            progress: 0,
            searchStatus: '',
            sessionId: '',
            eventSource: null,
            results: [],
            showQRModal: false,
            currentQRCode: null,
            isGeneratingQR: false,
            filterTypes: [
                { value: 'all', label: '全部' },
                { value: 'quark', label: '夸克网盘' },
                { value: 'baidu', label: '百度网盘' },
                { value: 'aliyun', label: '阿里云盘' }
            ]
        };
    },

    computed: {
        filteredResults() {
            if (this.activeFilter === 'all') return this.results;
            return this.results.filter(r => r.platform === this.activeFilter);
        },
        searchStatusText() {
            if (this.isSearching) return `正在搜索中... (${this.results.length} 个结果)`;
            if (this.searchCompleted) return `搜索完成，共找到 ${this.results.length} 个结果`;
            return '';
        }
    },

    mounted() {
        this.startSearch();
    },

    beforeUnmount() {
        this.closeConnection();
    },

    methods: {
        async startSearch() {
            try {
                this.isSearching = true;
                this.progress = 0;
                this.results = [];
                
                const response = await axios.post(`${window.SOPAN_CONFIG.apiUrl}/search/start`, {
                    keyword: this.keyword,
                    type: this.searchType
                });

                if (response.data.success) {
                    this.sessionId = response.data.data.session_id;
                    this.connectSSE();
                } else {
                    this.isSearching = false;
                    Utils.showMessage(response.data.message || '搜索启动失败', 'error');
                }
            } catch (error) {
                this.isSearching = false;
                console.error('Search start error:', error);
                
                // 使用ErrorHandler处理错误
                if (error.response) {
                    const message = error.response.data?.message || '搜索服务异常';
                    Utils.showMessage(message, 'error');
                } else if (error.request) {
                    Utils.showMessage('网络连接失败，请检查网络后重试', 'error');
                } else {
                    Utils.showMessage('搜索启动失败，请稍后重试', 'error');
                }
            }
        },

        connectSSE() {
            this.closeConnection();
            
            const sseUrl = `${window.SOPAN_CONFIG.apiUrl}/search/stream/${this.sessionId}`;
            this.eventSource = new EventSource(sseUrl);

            this.eventSource.onopen = () => {
                console.log('SSE连接已建立');
            };

            this.eventSource.addEventListener('connected', (event) => {
                const data = JSON.parse(event.data);
                console.log('搜索会话已连接:', data);
            });
            
            this.eventSource.addEventListener('status', (event) => {
                const status = JSON.parse(event.data);
                this.progress = status.progress || 0;
                this.searchStatus = status.status;
            });

            this.eventSource.addEventListener('results', (event) => {
                const newResults = JSON.parse(event.data);
                // 添加新结果到列表
                this.results.push(...newResults.map(result => ({
                    title: result.title,
                    url: result.url,
                    code: result.code,
                    size: result.size || '未知',
                    platform: this.getPlatformName(result.platform_type),
                    platform_type: result.platform_type,
                    match_score: result.match_score / 100,
                    create_time: '刚刚'
                })));
            });

            this.eventSource.addEventListener('finished', (event) => {
                const data = JSON.parse(event.data);
                this.isSearching = false;
                this.searchCompleted = true;
                this.progress = 100;
                
                if (data.status === 'completed') {
                    Utils.showMessage(`搜索完成，共找到 ${data.total_results} 个结果`, 'success');
                } else if (data.status === 'error') {
                    Utils.showMessage('搜索过程中出现错误', 'error');
                }
                
                this.closeConnection();
            });
            
            this.eventSource.addEventListener('heartbeat', (event) => {
                // 心跳事件，保持连接活跃
            });
            
            this.eventSource.addEventListener('close', (event) => {
                this.closeConnection();
            });

            this.eventSource.onerror = (error) => {
                console.error('SSE连接错误:', error);
                this.isSearching = false;
                this.closeConnection();
                
                // 检查网络状态
                if (navigator.onLine) {
                    Utils.showMessage('搜索连接中断，正在尝试重连...', 'warning');
                    // 尝试重新连接（简单的重试机制）
                    setTimeout(() => {
                        if (!this.isSearching) {
                            this.connectSSE();
                        }
                    }, 3000);
                } else {
                    Utils.showMessage('网络连接已断开，请检查网络连接', 'error');
                }
            };
        },

        closeConnection() {
            if (this.eventSource) {
                this.eventSource.close();
                this.eventSource = null;
            }
        },

        newSearch() {
            const keyword = this.searchKeyword.trim();
            if (!keyword) return;
            
            const url = new URL(window.location);
            url.searchParams.set('q', keyword);
            url.searchParams.set('type', this.searchType);
            window.location.href = url.toString();
        },

        getResultCount(platform) {
            if (platform === 'all') return this.results.length;
            return this.results.filter(r => r.platform === platform).length;
        },

        getPlatformName(platformType) {
            const platformMap = {
                'quark': 'quark',
                'baidu': 'baidu', 
                'aliyun': 'aliyun',
                'ali': 'aliyun',
                '115': '115'
            };
            return platformMap[platformType] || 'unknown';
        },

        copyLink(url) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    Utils.showMessage('链接已复制到剪贴板', 'success');
                }).catch(() => {
                    this.fallbackCopyTextToClipboard(url);
                });
            } else {
                this.fallbackCopyTextToClipboard(url);
            }
        },

        fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.top = '0';
            textArea.style.left = '0';
            textArea.style.width = '2em';
            textArea.style.height = '2em';
            textArea.style.padding = '0';
            textArea.style.border = 'none';
            textArea.style.outline = 'none';
            textArea.style.boxShadow = 'none';
            textArea.style.background = 'transparent';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                Utils.showMessage('链接已复制到剪贴板', 'success');
            } catch (err) {
                Utils.showMessage('复制失败，请手动复制', 'error');
                console.error('复制失败:', err);
            }
            
            document.body.removeChild(textArea);
        },

        showTransfer(result) {
            // 显示转存对话框
            if (confirm(`确定要转存 "${result.title}" 吗？`)) {
                this.transferResource(result);
            }
        },

        async transferResource(result) {
            try {
                Utils.showMessage('正在转存，请稍候...', 'info');
                
                const response = await axios.post(`${window.SOPAN_CONFIG.apiUrl}/transfer/start`, {
                    url: result.url,
                    code: result.code,
                    platform: result.platform_type,
                    title: result.title
                });
                
                if (response.data.success) {
                    Utils.showMessage('转存成功！', 'success');
                } else {
                    Utils.showMessage(response.data.message || '转存失败', 'error');
                }
            } catch (error) {
                console.error('转存失败:', error);
                
                if (error.response) {
                    const message = error.response.data?.message || '转存服务异常';
                    Utils.showMessage(message, 'error');
                } else if (error.request) {
                    Utils.showMessage('网络连接失败，请检查网络后重试', 'error');
                } else {
                    Utils.showMessage('转存失败，请稍后重试', 'error');
                }
            }
        },

        async generateQRCode(result) {
            if (this.isGeneratingQR) return;
            
            this.isGeneratingQR = true;
            
            try {
                Utils.showMessage('正在生成二维码...', 'info');
                
                const response = await axios.post(`${window.SOPAN_CONFIG.apiUrl}/qrcode/netdisk`, {
                    url: result.url,
                    code: result.code,
                    title: result.title
                });
                
                if (response.data.success) {
                    this.currentQRCode = {
                        qr_url: response.data.data.qr_url,
                        share_text: response.data.data.share_text,
                        result: result
                    };
                    this.showQRModal = true;
                    Utils.showMessage('二维码生成成功！', 'success');
                } else {
                    Utils.showMessage(response.data.message || '二维码生成失败', 'error');
                }
            } catch (error) {
                console.error('二维码生成失败:', error);
                
                if (error.response) {
                    const message = error.response.data?.message || '二维码服务异常';
                    Utils.showMessage(message, 'error');
                } else if (error.request) {
                    Utils.showMessage('网络连接失败，请检查网络后重试', 'error');
                } else {
                    Utils.showMessage('二维码生成失败，请稍后重试', 'error');
                }
            } finally {
                this.isGeneratingQR = false;
            }
        },

        closeQRModal() {
            this.showQRModal = false;
            this.currentQRCode = null;
        },

        downloadQRCode() {
            if (this.currentQRCode && this.currentQRCode.qr_url) {
                const link = document.createElement('a');
                link.href = this.currentQRCode.qr_url;
                link.download = `qrcode_${this.currentQRCode.result.title || 'resource'}.png`;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }
    }
}).mount('#searchApp');
</script>
<?php $this->endSection(); ?>