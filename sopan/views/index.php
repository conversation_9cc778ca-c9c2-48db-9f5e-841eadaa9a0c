<?php $this->setLayout('layout'); ?>

<?php $this->startSection('content'); ?>
<div id="indexApp">
    <!-- 主搜索区域 -->
    <section class="bg-gradient-to-br from-blue-50 to-indigo-100 py-16 lg:py-24">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <!-- 网站标题和描述 -->
            <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
                <?= $this->escape($site_name) ?>
            </h1>
            <p class="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
                <?= $this->escape($site_description) ?>
            </p>
            
            <!-- 搜索表单 -->
            <div class="max-w-2xl mx-auto">
                <form @submit.prevent="handleSearch" class="relative">
                    <div class="relative">
                        <input
                            v-model="searchKeyword"
                            type="text"
                            placeholder="请输入要搜索的关键词..."
                            class="search-input w-full px-6 py-4 text-lg border-2 border-gray-300 rounded-full focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                            maxlength="100"
                            autocomplete="off"
                        >
                        <button
                            type="submit"
                            :disabled="!searchKeyword.trim() || isSearching"
                            class="absolute right-2 top-2 bottom-2 px-8 bg-blue-600 text-white rounded-full hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                        >
                            <span v-if="!isSearching" class="flex items-center space-x-2">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <span>搜索</span>
                            </span>
                            <div v-else class="loading-spinner mx-auto"></div>
                        </button>
                    </div>
                    
                    <!-- 搜索建议 -->
                    <div v-if="showSuggestions && searchSuggestions.length > 0" 
                         class="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                        <div v-for="(suggestion, index) in searchSuggestions" 
                             :key="index"
                             @click="selectSuggestion(suggestion.keyword)"
                             class="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0">
                            <div class="font-medium">{{ suggestion.keyword }}</div>
                            <div v-if="suggestion.description" class="text-sm text-gray-500">{{ suggestion.description }}</div>
                        </div>
                    </div>
                </form>
                
                <!-- 搜索类型选择 -->
                <div class="mt-6 flex flex-wrap justify-center gap-2">
                    <label v-for="type in searchTypes" :key="type.value" 
                           class="inline-flex items-center">
                        <input v-model="searchType" 
                               :value="type.value" 
                               type="radio" 
                               class="form-radio text-blue-600">
                        <span class="ml-2 text-sm text-gray-700">{{ type.label }}</span>
                    </label>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 功能特色展示 -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">功能特色</h2>
                <p class="text-lg text-gray-600">专业的网盘资源搜索与转存服务</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">多平台搜索</h3>
                    <p class="text-gray-600">支持夸克、百度、阿里云盘等主流网盘平台</p>
                </div>
                
                <div class="text-center">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">实时推送</h3>
                    <p class="text-gray-600">搜索结果实时推送，搜到一个显示一个</p>
                </div>
                
                <div class="text-center">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">智能转存</h3>
                    <p class="text-gray-600">一键转存到多个网盘平台，智能去广告</p>
                </div>
                
                <div class="text-center">
                    <div class="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">安全可靠</h3>
                    <p class="text-gray-600">无需登录，保护隐私，安全便捷</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 热门搜索关键词 -->
    <section v-if="hotKeywords.length > 0" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">热门搜索</h2>
                <p class="text-gray-600">大家都在搜什么</p>
            </div>
            
            <div class="flex flex-wrap justify-center gap-3">
                <button v-for="keyword in hotKeywords" 
                        :key="keyword.keyword"
                        @click="searchKeyword = keyword.keyword; handleSearch()"
                        class="px-4 py-2 bg-white border border-gray-300 rounded-full text-gray-700 hover:border-blue-500 hover:text-blue-600 transition-all duration-200">
                    {{ keyword.keyword }}
                    <span class="ml-2 text-xs text-gray-500">({{ keyword.search_count }})</span>
                </button>
            </div>
        </div>
    </section>
    
    <!-- 搜索提示词 -->
    <section v-if="suggestions.length > 0" class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">推荐搜索</h2>
                <p class="text-gray-600">试试这些热门资源</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div v-for="suggestion in suggestions" 
                     :key="suggestion.keyword"
                     @click="searchKeyword = suggestion.keyword; handleSearch()"
                     class="p-6 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-all duration-200">
                    <h3 class="font-semibold text-gray-900 mb-2">{{ suggestion.keyword }}</h3>
                    <p class="text-gray-600 text-sm">{{ suggestion.description }}</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 网站统计 -->
    <section v-if="showStats && stats" class="py-16 bg-blue-600 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold mb-4">服务统计</h2>
                <p class="text-blue-100">为用户提供专业的搜索服务</p>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="text-3xl font-bold mb-2">{{ formatNumber(stats.total_searches) }}</div>
                    <div class="text-blue-100">总搜索次数</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold mb-2">{{ formatNumber(stats.today_searches) }}</div>
                    <div class="text-blue-100">今日搜索</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold mb-2">{{ formatNumber(stats.total_transfers) }}</div>
                    <div class="text-blue-100">总转存次数</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold mb-2">{{ formatNumber(stats.today_transfers) }}</div>
                    <div class="text-blue-100">今日转存</div>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
const { createApp } = Vue;

createApp({
    data() {
        return {
            searchKeyword: '',
            searchType: 'all',
            isSearching: false,
            showSuggestions: false,
            searchSuggestions: [],
            searchTypes: [
                { value: 'all', label: '全部' },
                { value: 'quark', label: '夸克网盘' },
                { value: 'baidu', label: '百度网盘' },
                { value: 'aliyun', label: '阿里云盘' },
                { value: 'uc', label: 'UC网盘' }
            ],
            hotKeywords: <?= json_encode($hot_keywords ?? []) ?>,
            suggestions: <?= json_encode($suggestions ?? []) ?>,
            stats: <?= json_encode($stats ?? null) ?>,
            showStats: <?= json_encode($show_stats ?? true) ?>
        };
    },
    
    mounted() {
        // 监听输入事件，显示搜索建议
        this.$nextTick(() => {
            const input = document.querySelector('.search-input');
            if (input) {
                input.addEventListener('input', this.debouncedGetSuggestions);
                input.addEventListener('focus', () => {
                    if (this.searchKeyword.trim().length >= 2) {
                        this.showSuggestions = true;
                    }
                });
                input.addEventListener('blur', () => {
                    // 延迟隐藏，允许点击建议
                    setTimeout(() => {
                        this.showSuggestions = false;
                    }, 200);
                });
            }
        });
    },
    
    computed: {
        debouncedGetSuggestions() {
            return Utils.debounce(this.getSuggestions, 300);
        }
    },
    
    methods: {
        async handleSearch() {
            const keyword = this.searchKeyword.trim();
            if (!keyword) {
                Utils.showMessage('请输入搜索关键词', 'warning');
                return;
            }
            
            if (keyword.length < 2) {
                Utils.showMessage('搜索关键词至少需要2个字符', 'warning');
                return;
            }
            
            this.isSearching = true;
            
            try {
                // 构建搜索URL
                const searchUrl = `${window.SOPAN_CONFIG.baseUrl}/search?q=${encodeURIComponent(keyword)}&type=${this.searchType}`;
                window.location.href = searchUrl;
            } catch (error) {
                console.error('Search error:', error);
                Utils.showMessage('搜索出错，请稍后重试', 'error');
            } finally {
                this.isSearching = false;
            }
        },
        
        async getSuggestions() {
            const keyword = this.searchKeyword.trim();
            if (keyword.length < 2) {
                this.showSuggestions = false;
                return;
            }
            
            try {
                const response = await axios.get(`${window.SOPAN_CONFIG.apiUrl}/search/suggestions`, {
                    params: { q: keyword, limit: 8 }
                });
                
                if (response.data.success) {
                    this.searchSuggestions = response.data.data || [];
                    this.showSuggestions = this.searchSuggestions.length > 0;
                }
            } catch (error) {
                console.error('Get suggestions error:', error);
                this.showSuggestions = false;
            }
        },
        
        selectSuggestion(keyword) {
            this.searchKeyword = keyword;
            this.showSuggestions = false;
            this.handleSearch();
        },
        
        formatNumber(num) {
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + '万';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }
    }
}).mount('#indexApp');
</script>
<?php $this->endSection(); ?>