# SOPAN 配置文件说明

## 配置文件结构

安装完成后，系统会在 `config/` 目录下生成以下配置文件：

- `app.php` - 应用基础配置
- `database.php` - 数据库连接配置  
- `search.php` - 搜索功能配置
- `installed.lock` - 安装标记文件

## 配置文件模板

`config.example/` 目录包含了配置文件模板，仅供开发参考使用。

## 注意事项

1. **安装前**：`config/` 目录应该是空的或不存在
2. **安装时**：安装程序会自动创建所有必需的配置文件
3. **安装后**：如需修改配置，请直接编辑 `config/` 目录下的文件

## 重新安装

如需重新安装系统：
1. 删除 `config/installed.lock` 文件
2. 访问 `/install` 页面重新安装
3. 系统会重新生成所有配置文件