<?php
/**
 * SOPAN 测试数据库初始化脚本
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class TestDatabaseInitializer {
    private $db;
    private $config;
    
    public function __construct() {
        // 加载测试配置
        require_once dirname(__DIR__, 2) . '/bootstrap.php';
        
        $this->config = Config::getInstance();
        
        // 连接到MySQL服务器（不指定数据库）
        $dbConfig = $this->config->get('database');
        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};charset={$dbConfig['charset']}";
        
        try {
            $this->db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
        } catch (PDOException $e) {
            die("无法连接到数据库服务器: " . $e->getMessage() . "\n");
        }
    }
    
    /**
     * 初始化测试数据库
     */
    public function initialize() {
        echo "初始化SOPAN测试数据库...\n";
        
        $dbName = $this->config->get('database.database');
        
        try {
            // 1. 删除现有测试数据库（如果存在）
            $this->db->exec("DROP DATABASE IF EXISTS `{$dbName}`");
            echo "✓ 已删除现有测试数据库\n";
            
            // 2. 创建测试数据库
            $this->db->exec("CREATE DATABASE `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "✓ 已创建测试数据库: {$dbName}\n";
            
            // 3. 选择数据库
            $this->db->exec("USE `{$dbName}`");
            
            // 4. 创建表结构
            $this->createTables();
            
            // 5. 插入测试数据
            $this->insertTestData();
            
            echo "✓ 测试数据库初始化完成\n\n";
            
        } catch (PDOException $e) {
            die("数据库初始化失败: " . $e->getMessage() . "\n");
        }
    }
    
    /**
     * 创建表结构
     */
    private function createTables() {
        echo "创建表结构...\n";
        
        $tables = [
            // 管理员表
            'admins' => "
                CREATE TABLE admins (
                    id int(11) NOT NULL AUTO_INCREMENT,
                    username varchar(50) NOT NULL,
                    password varchar(255) NOT NULL,
                    email varchar(100) DEFAULT NULL,
                    role varchar(20) DEFAULT 'admin',
                    status tinyint(1) DEFAULT 1,
                    last_login_time datetime DEFAULT NULL,
                    last_login_ip varchar(45) DEFAULT NULL,
                    failed_attempts int(11) DEFAULT 0,
                    locked_until datetime DEFAULT NULL,
                    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    UNIQUE KEY username (username)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // 搜索日志表
            'search_logs' => "
                CREATE TABLE search_logs (
                    id int(11) NOT NULL AUTO_INCREMENT,
                    keyword varchar(255) NOT NULL,
                    user_ip varchar(45) NOT NULL,
                    user_agent varchar(500) DEFAULT NULL,
                    search_type varchar(50) DEFAULT 'all',
                    result_count int(11) DEFAULT 0,
                    response_time decimal(10,3) DEFAULT NULL,
                    is_success tinyint(1) DEFAULT 1,
                    search_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    KEY idx_keyword (keyword),
                    KEY idx_user_ip (user_ip),
                    KEY idx_search_time (search_time)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // 转存记录表
            'transfer_records' => "
                CREATE TABLE transfer_records (
                    id int(11) NOT NULL AUTO_INCREMENT,
                    transfer_id varchar(100) NOT NULL,
                    url text NOT NULL,
                    code varchar(20) DEFAULT NULL,
                    platform varchar(20) NOT NULL,
                    title varchar(255) DEFAULT NULL,
                    user_ip varchar(45) NOT NULL,
                    status enum('processing','success','failed','cancelled') DEFAULT 'processing',
                    message text,
                    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    UNIQUE KEY transfer_id (transfer_id),
                    KEY idx_user_ip (user_ip),
                    KEY idx_status (status),
                    KEY idx_create_time (create_time)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // 系统日志表
            'system_logs' => "
                CREATE TABLE system_logs (
                    id int(11) NOT NULL AUTO_INCREMENT,
                    level enum('emergency','alert','critical','error','warning','notice','info','debug') NOT NULL,
                    type varchar(50) NOT NULL,
                    message text NOT NULL,
                    context json DEFAULT NULL,
                    ip varchar(45) DEFAULT NULL,
                    request_id varchar(100) DEFAULT NULL,
                    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    KEY idx_level (level),
                    KEY idx_type (type),
                    KEY idx_created_at (created_at),
                    KEY idx_ip (ip)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // 错误报告表
            'error_reports' => "
                CREATE TABLE error_reports (
                    id int(11) NOT NULL AUTO_INCREMENT,
                    error_type varchar(100) NOT NULL,
                    error_message text NOT NULL,
                    stack_trace longtext,
                    page_url varchar(500) DEFAULT NULL,
                    user_agent varchar(500) DEFAULT NULL,
                    user_ip varchar(45) NOT NULL,
                    client_timestamp varchar(30) DEFAULT NULL,
                    additional_info json DEFAULT NULL,
                    is_resolved tinyint(1) DEFAULT 0,
                    resolution_note text,
                    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    KEY idx_error_type (error_type),
                    KEY idx_user_ip (user_ip),
                    KEY idx_created_at (created_at),
                    KEY idx_is_resolved (is_resolved)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // 配置表
            'configs' => "
                CREATE TABLE configs (
                    id int(11) NOT NULL AUTO_INCREMENT,
                    section varchar(50) NOT NULL,
                    config_key varchar(100) NOT NULL,
                    config_value text,
                    description text,
                    type enum('string','int','float','bool','json','text') DEFAULT 'string',
                    is_encrypted tinyint(1) DEFAULT 0,
                    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    UNIQUE KEY unique_config (section,config_key),
                    KEY idx_section (section)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "
        ];
        
        foreach ($tables as $tableName => $sql) {
            $this->db->exec($sql);
            echo "  ✓ {$tableName}\n";
        }
    }
    
    /**
     * 插入测试数据
     */
    private function insertTestData() {
        echo "插入测试数据...\n";
        
        // 插入管理员测试数据
        $this->db->exec("
            INSERT INTO admins (username, password, email, role, status) VALUES
            ('admin', '" . password_hash('admin123', PASSWORD_DEFAULT) . "', '<EMAIL>', 'admin', 1),
            ('test_user', '" . password_hash('test123', PASSWORD_DEFAULT) . "', '<EMAIL>', 'user', 1)
        ");
        echo "  ✓ 管理员数据\n";
        
        // 插入搜索日志测试数据
        $this->db->exec("
            INSERT INTO search_logs (keyword, user_ip, search_type, result_count, response_time, is_success) VALUES
            ('测试关键词1', '*************', 'all', 10, 1.25, 1),
            ('测试关键词2', '*************', 'baidu', 5, 2.10, 1),
            ('测试关键词3', '*************', 'quark', 0, 0.50, 0)
        ");
        echo "  ✓ 搜索日志数据\n";
        
        // 插入系统配置测试数据
        $this->db->exec("
            INSERT INTO configs (section, config_key, config_value, description, type) VALUES
            ('app', 'site_name', 'SOPAN测试站点', '网站名称', 'string'),
            ('app', 'site_description', '专业的网盘搜索工具', '网站描述', 'string'),
            ('search', 'max_results', '100', '最大搜索结果数', 'int'),
            ('transfer', 'timeout', '60', '转存超时时间(秒)', 'int')
        ");
        echo "  ✓ 配置数据\n";
        
        // 插入系统日志测试数据
        $this->db->exec("
            INSERT INTO system_logs (level, type, message, ip) VALUES
            ('info', 'system', '系统启动', '127.0.0.1'),
            ('warning', 'search', '搜索超时', '*************'),
            ('error', 'transfer', '转存失败', '*************')
        ");
        echo "  ✓ 系统日志数据\n";
    }
    
    /**
     * 清理测试数据库
     */
    public function cleanup() {
        echo "清理测试数据库...\n";
        
        $dbName = $this->config->get('database.database');
        
        try {
            $this->db->exec("DROP DATABASE IF EXISTS `{$dbName}`");
            echo "✓ 已删除测试数据库: {$dbName}\n";
        } catch (PDOException $e) {
            echo "清理数据库失败: " . $e->getMessage() . "\n";
        }
    }
}

// 如果直接运行此脚本
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $initializer = new TestDatabaseInitializer();
    
    $command = $argv[1] ?? 'init';
    
    switch ($command) {
        case 'init':
            $initializer->initialize();
            break;
        case 'cleanup':
            $initializer->cleanup();
            break;
        default:
            echo "用法: php test_db_init.php [init|cleanup]\n";
            echo "  init    - 初始化测试数据库\n";
            echo "  cleanup - 清理测试数据库\n";
            break;
    }
}