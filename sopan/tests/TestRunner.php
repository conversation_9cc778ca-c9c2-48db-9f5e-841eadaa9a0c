<?php
/**
 * SOPAN 测试运行器
 * 运行所有测试用例并生成报告
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class TestRunner {
    private $testSuite = [];
    private $results = [];
    private $startTime;
    
    public function __construct() {
        $this->startTime = microtime(true);
    }
    
    /**
     * 添加测试类
     */
    public function addTestClass($className) {
        $this->testSuite[] = $className;
    }
    
    /**
     * 运行所有测试
     */
    public function runAll() {
        echo "SOPAN Test Suite\n";
        echo str_repeat("=", 60) . "\n";
        echo "Starting tests at " . date('Y-m-d H:i:s') . "\n\n";
        
        foreach ($this->testSuite as $className) {
            $this->runTestClass($className);
        }
        
        $this->generateSummaryReport();
    }
    
    /**
     * 运行单个测试类
     */
    private function runTestClass($className) {
        if (!class_exists($className)) {
            echo "ERROR: Test class {$className} not found\n";
            return;
        }
        
        try {
            $testCase = new $className();
            if (!($testCase instanceof BaseTestCase)) {
                echo "ERROR: {$className} must extend BaseTestCase\n";
                return;
            }
            
            ob_start();
            $testCase->runTests();
            $output = ob_get_clean();
            
            echo $output;
            
            // 收集结果
            $reflection = new ReflectionClass($testCase);
            $results = $reflection->getProperty('testResults');
            $results->setAccessible(true);
            $this->results[$className] = $results->getValue($testCase);
            
        } catch (Exception $e) {
            echo "ERROR running {$className}: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * 生成总结报告
     */
    private function generateSummaryReport() {
        $totalTests = 0;
        $passedTests = 0;
        $failedTests = 0;
        $errorTests = 0;
        
        foreach ($this->results as $className => $classResults) {
            foreach ($classResults as $method => $result) {
                $totalTests++;
                switch ($result['status']) {
                    case 'PASS':
                        $passedTests++;
                        break;
                    case 'FAIL':
                        $failedTests++;
                        break;
                    case 'ERROR':
                        $errorTests++;
                        break;
                }
            }
        }
        
        $endTime = microtime(true);
        $duration = round($endTime - $this->startTime, 3);
        
        echo str_repeat("=", 60) . "\n";
        echo "TEST SUITE SUMMARY\n";
        echo str_repeat("=", 60) . "\n";
        echo "Total Tests: {$totalTests}\n";
        echo "Passed:      {$passedTests}\n";
        echo "Failed:      {$failedTests}\n";
        echo "Errors:      {$errorTests}\n";
        echo "Duration:    {$duration}s\n";
        
        if ($totalTests > 0) {
            $successRate = round(($passedTests / $totalTests) * 100, 2);
            echo "Success Rate: {$successRate}%\n";
        }
        
        if ($failedTests > 0 || $errorTests > 0) {
            echo "\nFAILED/ERROR TESTS:\n";
            echo str_repeat("-", 40) . "\n";
            foreach ($this->results as $className => $classResults) {
                foreach ($classResults as $method => $result) {
                    if ($result['status'] !== 'PASS') {
                        echo "{$className}::{$method} - {$result['status']}: {$result['message']}\n";
                    }
                }
            }
        }
        
        // 生成JUnit XML报告（可选）
        $this->generateJUnitReport();
        
        echo "\nTest run completed.\n";
    }
    
    /**
     * 生成JUnit XML报告
     */
    private function generateJUnitReport() {
        $xml = new SimpleXMLElement('<testsuites/>');
        
        foreach ($this->results as $className => $classResults) {
            $testsuite = $xml->addChild('testsuite');
            $testsuite->addAttribute('name', $className);
            $testsuite->addAttribute('tests', count($classResults));
            
            $failures = 0;
            $errors = 0;
            
            foreach ($classResults as $method => $result) {
                $testcase = $testsuite->addChild('testcase');
                $testcase->addAttribute('name', $method);
                $testcase->addAttribute('classname', $className);
                $testcase->addAttribute('time', $result['time']);
                
                if ($result['status'] === 'FAIL') {
                    $failure = $testcase->addChild('failure', htmlspecialchars($result['message']));
                    $failure->addAttribute('message', 'Test Failed');
                    $failures++;
                } elseif ($result['status'] === 'ERROR') {
                    $error = $testcase->addChild('error', htmlspecialchars($result['message']));
                    $error->addAttribute('message', 'Test Error');
                    $errors++;
                }
            }
            
            $testsuite->addAttribute('failures', $failures);
            $testsuite->addAttribute('errors', $errors);
        }
        
        $reportDir = dirname(__DIR__) . '/tests/reports';
        if (!is_dir($reportDir)) {
            mkdir($reportDir, 0755, true);
        }
        
        $xml->asXML($reportDir . '/junit-results.xml');
        echo "JUnit report generated: tests/reports/junit-results.xml\n";
    }
}

// 自动加载器
spl_autoload_register(function ($className) {
    $directories = [
        dirname(__DIR__) . '/core/',
        dirname(__DIR__) . '/app/controllers/',
        dirname(__DIR__) . '/app/services/',
        dirname(__DIR__) . '/tests/',
        dirname(__DIR__) . '/tests/unit/',
        dirname(__DIR__) . '/tests/integration/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $className . '.php';
        if (file_exists($file)) {
            require_once $file;
            break;
        }
    }
});

// 如果直接运行这个文件，自动发现并运行所有测试
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $runner = new TestRunner();
    
    // 自动发现测试类
    $testDirs = [
        dirname(__DIR__) . '/tests/unit/',
        dirname(__DIR__) . '/tests/integration/'
    ];
    
    foreach ($testDirs as $dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '*Test.php');
            foreach ($files as $file) {
                $className = basename($file, '.php');
                require_once $file;
                $runner->addTestClass($className);
            }
        }
    }
    
    $runner->runAll();
}