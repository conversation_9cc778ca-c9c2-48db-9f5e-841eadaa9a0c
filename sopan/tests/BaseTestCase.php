<?php
/**
 * SOPAN 基础测试类
 * 提供测试框架和通用测试方法
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

abstract class BaseTestCase {
    protected $db;
    protected $config;
    protected $testResults = [];
    protected $currentTest = '';
    protected $startTime;
    
    public function __construct() {
        $this->startTime = microtime(true);
        $this->setupTestEnvironment();
    }
    
    /**
     * 设置测试环境
     */
    protected function setupTestEnvironment() {
        // 设置测试模式
        if (!defined('SOPAN_TEST_MODE')) {
            define('SOPAN_TEST_MODE', true);
        }
        
        // 加载配置和数据库
        require_once dirname(__DIR__) . '/bootstrap.php';
        
        $this->config = Config::getInstance();
        $this->db = Database::getInstance();
        
        // 切换到测试数据库
        $this->setupTestDatabase();
    }
    
    /**
     * 设置测试数据库
     */
    protected function setupTestDatabase() {
        try {
            // 创建测试数据库配置
            $testDbConfig = [
                'host' => $this->config->get('database.host', 'localhost'),
                'port' => $this->config->get('database.port', 3306),
                'username' => $this->config->get('database.username'),
                'password' => $this->config->get('database.password'),
                'database' => $this->config->get('database.database') . '_test',
                'charset' => 'utf8mb4'
            ];
            
            // 重新连接到测试数据库
            $this->db = new Database($testDbConfig);
            
        } catch (Exception $e) {
            $this->fail("Failed to setup test database: " . $e->getMessage());
        }
    }
    
    /**
     * 运行所有测试
     */
    public function runTests() {
        $this->output("Running tests for " . get_class($this) . "\n");
        $this->output(str_repeat("=", 50) . "\n");
        
        $methods = get_class_methods($this);
        $testMethods = array_filter($methods, function($method) {
            return strpos($method, 'test') === 0;
        });
        
        foreach ($testMethods as $method) {
            $this->runTest($method);
        }
        
        $this->generateReport();
    }
    
    /**
     * 运行单个测试
     */
    protected function runTest($method) {
        $this->currentTest = $method;
        $this->output("Running {$method}... ");
        
        try {
            $this->setUp();
            $this->$method();
            $this->tearDown();
            
            $this->testResults[$method] = [
                'status' => 'PASS',
                'message' => 'Test passed',
                'time' => microtime(true) - $this->startTime
            ];
            $this->output("PASS\n");
            
        } catch (TestFailedException $e) {
            $this->testResults[$method] = [
                'status' => 'FAIL',
                'message' => $e->getMessage(),
                'time' => microtime(true) - $this->startTime
            ];
            $this->output("FAIL - " . $e->getMessage() . "\n");
            
        } catch (Exception $e) {
            $this->testResults[$method] = [
                'status' => 'ERROR',
                'message' => $e->getMessage(),
                'time' => microtime(true) - $this->startTime
            ];
            $this->output("ERROR - " . $e->getMessage() . "\n");
        }
    }
    
    /**
     * 测试前的设置
     */
    protected function setUp() {
        // 子类可以重写这个方法
    }
    
    /**
     * 测试后的清理
     */
    protected function tearDown() {
        // 子类可以重写这个方法
    }
    
    /**
     * 生成测试报告
     */
    protected function generateReport() {
        $total = count($this->testResults);
        $passed = count(array_filter($this->testResults, function($result) {
            return $result['status'] === 'PASS';
        }));
        $failed = $total - $passed;
        
        $this->output("\n" . str_repeat("=", 50) . "\n");
        $this->output("Test Results:\n");
        $this->output("Total: {$total}, Passed: {$passed}, Failed: {$failed}\n");
        $this->output("Success Rate: " . round(($passed / $total) * 100, 2) . "%\n");
        
        if ($failed > 0) {
            $this->output("\nFailed Tests:\n");
            foreach ($this->testResults as $method => $result) {
                if ($result['status'] !== 'PASS') {
                    $this->output("- {$method}: {$result['message']}\n");
                }
            }
        }
    }
    
    // ==================== 断言方法 ====================
    
    /**
     * 断言相等
     */
    protected function assertEquals($expected, $actual, $message = '') {
        if ($expected != $actual) {
            $this->fail($message ?: "Expected {$expected}, got {$actual}");
        }
    }
    
    /**
     * 断言严格相等
     */
    protected function assertSame($expected, $actual, $message = '') {
        if ($expected !== $actual) {
            $this->fail($message ?: "Expected " . var_export($expected, true) . ", got " . var_export($actual, true));
        }
    }
    
    /**
     * 断言为真
     */
    protected function assertTrue($condition, $message = '') {
        if (!$condition) {
            $this->fail($message ?: "Condition is not true");
        }
    }
    
    /**
     * 断言为假
     */
    protected function assertFalse($condition, $message = '') {
        if ($condition) {
            $this->fail($message ?: "Condition is not false");
        }
    }
    
    /**
     * 断言为null
     */
    protected function assertNull($value, $message = '') {
        if ($value !== null) {
            $this->fail($message ?: "Value is not null");
        }
    }
    
    /**
     * 断言不为null
     */
    protected function assertNotNull($value, $message = '') {
        if ($value === null) {
            $this->fail($message ?: "Value is null");
        }
    }
    
    /**
     * 断言包含
     */
    protected function assertContains($needle, $haystack, $message = '') {
        if (is_string($haystack)) {
            if (strpos($haystack, $needle) === false) {
                $this->fail($message ?: "String '{$haystack}' does not contain '{$needle}'");
            }
        } elseif (is_array($haystack)) {
            if (!in_array($needle, $haystack)) {
                $this->fail($message ?: "Array does not contain " . var_export($needle, true));
            }
        } else {
            $this->fail("Unsupported haystack type for assertContains");
        }
    }
    
    /**
     * 断言数组有键
     */
    protected function assertArrayHasKey($key, $array, $message = '') {
        if (!array_key_exists($key, $array)) {
            $this->fail($message ?: "Array does not have key '{$key}'");
        }
    }
    
    /**
     * 断言实例类型
     */
    protected function assertInstanceOf($expected, $actual, $message = '') {
        if (!($actual instanceof $expected)) {
            $this->fail($message ?: "Object is not an instance of {$expected}");
        }
    }
    
    /**
     * 断言数量
     */
    protected function assertCount($expected, $actual, $message = '') {
        $count = is_countable($actual) ? count($actual) : 0;
        if ($count !== $expected) {
            $this->fail($message ?: "Expected count {$expected}, got {$count}");
        }
    }
    
    /**
     * 断言抛出异常
     */
    protected function expectException($exceptionClass, callable $callback) {
        try {
            $callback();
            $this->fail("Expected exception {$exceptionClass} was not thrown");
        } catch (Exception $e) {
            if (!($e instanceof $exceptionClass)) {
                $this->fail("Expected exception {$exceptionClass}, got " . get_class($e));
            }
        }
    }
    
    /**
     * 失败测试
     */
    protected function fail($message) {
        throw new TestFailedException($message);
    }
    
    /**
     * 输出信息
     */
    protected function output($message) {
        echo $message;
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 创建模拟对象
     */
    protected function createMock($className) {
        return new MockObject($className);
    }
    
    /**
     * 创建测试数据
     */
    protected function createTestData($table, $data) {
        return $this->db->insert($table, $data);
    }
    
    /**
     * 清理测试数据
     */
    protected function cleanTestData($table, $conditions = []) {
        if (empty($conditions)) {
            $this->db->query("TRUNCATE TABLE {$table}");
        } else {
            $this->db->delete($table, $conditions);
        }
    }
    
    /**
     * 获取测试固件
     */
    protected function getFixture($name) {
        $fixturePath = dirname(__DIR__) . "/tests/fixtures/{$name}.php";
        if (file_exists($fixturePath)) {
            return require $fixturePath;
        }
        return [];
    }
}

/**
 * 测试失败异常类
 */
class TestFailedException extends Exception {
    
}

/**
 * 简单的模拟对象类
 */
class MockObject {
    private $className;
    private $methods = [];
    
    public function __construct($className) {
        $this->className = $className;
    }
    
    public function method($name) {
        if (!isset($this->methods[$name])) {
            $this->methods[$name] = new MockMethod();
        }
        return $this->methods[$name];
    }
    
    public function __call($name, $args) {
        if (isset($this->methods[$name])) {
            return $this->methods[$name]->invoke($args);
        }
        return null;
    }
}

/**
 * 模拟方法类
 */
class MockMethod {
    private $returnValue = null;
    private $callback = null;
    private $callCount = 0;
    
    public function willReturn($value) {
        $this->returnValue = $value;
        return $this;
    }
    
    public function will($callback) {
        $this->callback = $callback;
        return $this;
    }
    
    public function invoke($args) {
        $this->callCount++;
        
        if ($this->callback) {
            return call_user_func_array($this->callback, $args);
        }
        
        return $this->returnValue;
    }
    
    public function getCallCount() {
        return $this->callCount;
    }
}