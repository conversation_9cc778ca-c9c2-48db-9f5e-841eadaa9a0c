<?php
/**
 * SOPAN 测试环境配置
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

return [
    // 应用配置
    'app' => [
        'name' => 'SOPAN Test',
        'environment' => 'test',
        'debug' => true,
        'url' => 'http://localhost:8080',
        'timezone' => 'Asia/Shanghai'
    ],
    
    // 数据库配置
    'database' => [
        'host' => getenv('TEST_DB_HOST') ?: 'localhost',
        'port' => (int)(getenv('TEST_DB_PORT') ?: 3306),
        'username' => getenv('TEST_DB_USERNAME') ?: 'root',
        'password' => getenv('TEST_DB_PASSWORD') ?: '',
        'database' => getenv('TEST_DB_DATABASE') ?: 'sopan_test',
        'charset' => 'utf8mb4',
        'prefix' => 'test_'
    ],
    
    // 日志配置
    'logging' => [
        'level' => 'debug',
        'file_logging' => true,
        'database_logging' => true,
        'log_path' => sys_get_temp_dir() . '/sopan_test_logs',
        'max_file_size' => 1048576, // 1MB
        'max_files' => 3
    ],
    
    // 缓存配置
    'cache' => [
        'driver' => 'memory',
        'path' => sys_get_temp_dir() . '/sopan_test_cache'
    ],
    
    // API配置
    'api' => [
        'rate_limit' => 1000,
        'allowed_origins' => ['*'],
        'access_token' => 'test_token_' . uniqid()
    ],
    
    // 搜索配置
    'search' => [
        'timeout' => 30,
        'max_results' => 100,
        'mock_mode' => true, // 测试模式使用模拟数据
        'panseeker_api_url' => 'http://localhost/mock-panseeker-api',
        'api_key' => 'test_api_key'
    ],
    
    // 转存配置
    'transfer' => [
        'timeout' => 60,
        'max_retries' => 2,
        'mock_mode' => true, // 测试模式使用模拟转存
        'platforms' => [
            'quark' => [
                'username' => 'test_quark_user',
                'password' => 'test_quark_pass'
            ],
            'baidu' => [
                'username' => 'test_baidu_user', 
                'password' => 'test_baidu_pass'
            ]
        ]
    ],
    
    // QR码配置
    'qrcode' => [
        'size' => 6,
        'level' => 'M',
        'margin' => 2,
        'cache_ttl' => 3600
    ],
    
    // 错误处理配置
    'error_handling' => [
        'display_errors' => true,
        'log_errors' => true,
        'error_reporting' => E_ALL,
        'max_error_reports' => 1000
    ]
];