<?php
/**
 * Database类的单元测试
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

require_once dirname(__DIR__) . '/BaseTestCase.php';

class DatabaseTest extends BaseTestCase {
    
    protected function setUp() {
        // 创建测试表
        $this->createTestTables();
    }
    
    protected function tearDown() {
        // 清理测试表
        $this->dropTestTables();
    }
    
    /**
     * 测试数据库连接
     */
    public function testConnection() {
        $this->assertInstanceOf('Database', $this->db);
        $this->assertTrue($this->db->isConnected(), 'Database should be connected');
    }
    
    /**
     * 测试插入操作
     */
    public function testInsert() {
        $data = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $id = $this->db->insert('test_users', $data);
        
        $this->assertNotNull($id, 'Insert should return an ID');
        $this->assertTrue($id > 0, 'Insert ID should be positive');
        
        // 验证数据是否正确插入
        $user = $this->db->find('test_users', ['id' => $id]);
        $this->assertEquals('Test User', $user['name']);
        $this->assertEquals('<EMAIL>', $user['email']);
    }
    
    /**
     * 测试查找操作
     */
    public function testFind() {
        // 先插入测试数据
        $this->createTestUser('John Doe', '<EMAIL>');
        
        // 测试按条件查找
        $user = $this->db->find('test_users', ['email' => '<EMAIL>']);
        $this->assertNotNull($user, 'Should find user by email');
        $this->assertEquals('John Doe', $user['name']);
        
        // 测试查找不存在的记录
        $nonExistent = $this->db->find('test_users', ['email' => '<EMAIL>']);
        $this->assertNull($nonExistent, 'Should return null for non-existent record');
    }
    
    /**
     * 测试查找所有记录
     */
    public function testFindAll() {
        // 插入多条测试数据
        $this->createTestUser('User 1', '<EMAIL>');
        $this->createTestUser('User 2', '<EMAIL>');
        $this->createTestUser('User 3', '<EMAIL>');
        
        // 查找所有用户
        $users = $this->db->findAll('test_users');
        $this->assertCount(3, $users, 'Should find all 3 users');
        
        // 测试条件查找
        $users = $this->db->findAll('test_users', ['name' => 'User 1']);
        $this->assertCount(1, $users, 'Should find only one user');
        
        // 测试排序和限制
        $users = $this->db->findAll('test_users', [], [
            'order' => 'name DESC',
            'limit' => 2
        ]);
        $this->assertCount(2, $users, 'Should limit to 2 users');
        $this->assertEquals('User 3', $users[0]['name'], 'Should be ordered by name DESC');
    }
    
    /**
     * 测试更新操作
     */
    public function testUpdate() {
        // 创建测试用户
        $userId = $this->createTestUser('Old Name', '<EMAIL>');
        
        // 更新用户信息
        $affected = $this->db->update('test_users', [
            'name' => 'New Name',
            'email' => '<EMAIL>'
        ], ['id' => $userId]);
        
        $this->assertEquals(1, $affected, 'Should update exactly one record');
        
        // 验证更新结果
        $user = $this->db->find('test_users', ['id' => $userId]);
        $this->assertEquals('New Name', $user['name']);
        $this->assertEquals('<EMAIL>', $user['email']);
    }
    
    /**
     * 测试删除操作
     */
    public function testDelete() {
        // 创建测试用户
        $userId = $this->createTestUser('Delete Me', '<EMAIL>');
        
        // 删除用户
        $affected = $this->db->delete('test_users', ['id' => $userId]);
        $this->assertEquals(1, $affected, 'Should delete exactly one record');
        
        // 验证删除结果
        $user = $this->db->find('test_users', ['id' => $userId]);
        $this->assertNull($user, 'User should be deleted');
    }
    
    /**
     * 测试计数操作
     */
    public function testCount() {
        // 插入测试数据
        $this->createTestUser('User 1', '<EMAIL>');
        $this->createTestUser('User 2', '<EMAIL>');
        
        // 测试总数统计
        $count = $this->db->count('test_users');
        $this->assertEquals(2, $count, 'Should count all users');
        
        // 测试条件统计
        $count = $this->db->count('test_users', ['name' => 'User 1']);
        $this->assertEquals(1, $count, 'Should count one specific user');
    }
    
    /**
     * 测试原生查询
     */
    public function testQuery() {
        // 插入测试数据
        $this->createTestUser('Query User', '<EMAIL>');
        
        // 测试SELECT查询
        $result = $this->db->query("SELECT * FROM test_users WHERE email = ?", ['<EMAIL>']);
        $this->assertCount(1, $result, 'Query should return one result');
        $this->assertEquals('Query User', $result[0]['name']);
        
        // 测试UPDATE查询
        $affected = $this->db->query("UPDATE test_users SET name = ? WHERE email = ?", [
            'Updated User', '<EMAIL>'
        ]);
        $this->assertEquals(1, $affected, 'Update query should affect one row');
    }
    
    /**
     * 测试事务处理
     */
    public function testTransaction() {
        $this->db->beginTransaction();
        
        try {
            // 在事务中插入数据
            $userId1 = $this->db->insert('test_users', [
                'name' => 'Transaction User 1',
                'email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            $userId2 = $this->db->insert('test_users', [
                'name' => 'Transaction User 2',
                'email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            $this->db->commit();
            
            // 验证事务提交后数据存在
            $user1 = $this->db->find('test_users', ['id' => $userId1]);
            $user2 = $this->db->find('test_users', ['id' => $userId2]);
            
            $this->assertNotNull($user1, 'Transaction user 1 should exist');
            $this->assertNotNull($user2, 'Transaction user 2 should exist');
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->fail('Transaction should not fail: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试事务回滚
     */
    public function testTransactionRollback() {
        $this->db->beginTransaction();
        
        // 插入数据
        $userId = $this->db->insert('test_users', [
            'name' => 'Rollback User',
            'email' => '<EMAIL>',
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        // 回滚事务
        $this->db->rollback();
        
        // 验证数据不存在
        $user = $this->db->find('test_users', ['id' => $userId]);
        $this->assertNull($user, 'Rollback user should not exist after rollback');
    }
    
    /**
     * 测试SQL注入防护
     */
    public function testSQLInjectionProtection() {
        // 插入正常用户
        $this->createTestUser('Normal User', '<EMAIL>');
        
        // 尝试SQL注入攻击
        $maliciousEmail = "'; DROP TABLE test_users; --";
        
        $user = $this->db->find('test_users', ['email' => $maliciousEmail]);
        $this->assertNull($user, 'SQL injection should not find any user');
        
        // 验证表仍然存在
        $count = $this->db->count('test_users');
        $this->assertEquals(1, $count, 'Table should still exist with original data');
    }
    
    /**
     * 测试错误处理
     */
    public function testErrorHandling() {
        // 测试查询不存在的表
        $this->expectException('Exception', function() {
            $this->db->query("SELECT * FROM non_existent_table");
        });
        
        // 测试插入无效数据
        $this->expectException('Exception', function() {
            $this->db->insert('test_users', [
                'invalid_column' => 'value'
            ]);
        });
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 创建测试表
     */
    private function createTestTables() {
        $this->db->query("
            CREATE TABLE IF NOT EXISTS test_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
    }
    
    /**
     * 删除测试表
     */
    private function dropTestTables() {
        $this->db->query("DROP TABLE IF EXISTS test_users");
    }
    
    /**
     * 创建测试用户
     */
    private function createTestUser($name, $email) {
        return $this->db->insert('test_users', [
            'name' => $name,
            'email' => $email,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}