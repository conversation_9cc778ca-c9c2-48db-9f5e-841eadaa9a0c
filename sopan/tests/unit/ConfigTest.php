<?php
/**
 * Config类的单元测试
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

require_once dirname(__DIR__) . '/BaseTestCase.php';

class ConfigTest extends BaseTestCase {
    
    protected function setUp() {
        // 重置配置实例
        $reflection = new ReflectionClass('Config');
        $instance = $reflection->getProperty('instance');
        $instance->setAccessible(true);
        $instance->setValue(null, null);
        
        // 创建测试配置文件
        $this->createTestConfigFile();
    }
    
    protected function tearDown() {
        // 清理测试配置文件
        $this->cleanupTestConfigFile();
    }
    
    /**
     * 测试单例模式
     */
    public function testSingleton() {
        $config1 = Config::getInstance();
        $config2 = Config::getInstance();
        
        $this->assertSame($config1, $config2, 'Config should be singleton');
    }
    
    /**
     * 测试配置文件加载
     */
    public function testConfigFileLoading() {
        $config = Config::getInstance();
        
        // 测试基本配置加载
        $this->assertEquals('SOPAN Test', $config->get('app.name'), 'Should load app name from config');
        $this->assertEquals('localhost', $config->get('database.host'), 'Should load database host');
    }
    
    /**
     * 测试get方法
     */
    public function testGet() {
        $config = Config::getInstance();
        
        // 测试简单键值获取
        $this->assertEquals('test', $config->get('app.environment'));
        
        // 测试嵌套键值获取
        $this->assertEquals('utf8mb4', $config->get('database.charset'));
        
        // 测试默认值
        $this->assertEquals('default_value', $config->get('non.existent.key', 'default_value'));
        $this->assertNull($config->get('non.existent.key'), 'Should return null for non-existent key without default');
    }
    
    /**
     * 测试set方法
     */
    public function testSet() {
        $config = Config::getInstance();
        
        // 设置新的配置值
        $config->set('app.debug', true);
        $this->assertTrue($config->get('app.debug'), 'Should set and get boolean value');
        
        // 设置嵌套配置值
        $config->set('new.nested.value', 'test_value');
        $this->assertEquals('test_value', $config->get('new.nested.value'), 'Should set nested configuration');
        
        // 覆盖现有配置
        $config->set('app.name', 'New Name');
        $this->assertEquals('New Name', $config->get('app.name'), 'Should override existing config');
    }
    
    /**
     * 测试has方法
     */
    public function testHas() {
        $config = Config::getInstance();
        
        // 测试存在的配置
        $this->assertTrue($config->has('app.name'), 'Should return true for existing config');
        $this->assertTrue($config->has('database.host'), 'Should return true for nested config');
        
        // 测试不存在的配置
        $this->assertFalse($config->has('non.existent.key'), 'Should return false for non-existent config');
        
        // 设置后测试
        $config->set('new.key', 'value');
        $this->assertTrue($config->has('new.key'), 'Should return true for newly set config');
    }
    
    /**
     * 测试remove方法
     */
    public function testRemove() {
        $config = Config::getInstance();
        
        // 设置一个配置
        $config->set('temp.key', 'temp_value');
        $this->assertTrue($config->has('temp.key'), 'Config should exist before removal');
        
        // 移除配置
        $config->remove('temp.key');
        $this->assertFalse($config->has('temp.key'), 'Config should not exist after removal');
        $this->assertNull($config->get('temp.key'), 'Removed config should return null');
    }
    
    /**
     * 测试getAll方法
     */
    public function testGetAll() {
        $config = Config::getInstance();
        
        $allConfig = $config->getAll();
        
        $this->assertIsArray($allConfig, 'getAll should return an array');
        $this->assertArrayHasKey('app', $allConfig, 'Should contain app configuration');
        $this->assertArrayHasKey('database', $allConfig, 'Should contain database configuration');
    }
    
    /**
     * 测试数组式访问
     */
    public function testArrayAccess() {
        $config = Config::getInstance();
        
        // 测试读取
        $this->assertEquals('SOPAN Test', $config['app.name'], 'Should support array-style read access');
        
        // 测试设置
        $config['test.array.access'] = 'test_value';
        $this->assertEquals('test_value', $config['test.array.access'], 'Should support array-style write access');
        
        // 测试isset
        $this->assertTrue(isset($config['app.name']), 'Should support isset check');
        $this->assertFalse(isset($config['non.existent']), 'Should return false for non-existent keys');
        
        // 测试unset
        $config['temp.unset'] = 'value';
        unset($config['temp.unset']);
        $this->assertFalse(isset($config['temp.unset']), 'Should support unset operation');
    }
    
    /**
     * 测试配置合并
     */
    public function testMerge() {
        $config = Config::getInstance();
        
        $additionalConfig = [
            'app' => [
                'version' => '2.0.0',
                'debug' => true
            ],
            'cache' => [
                'driver' => 'file',
                'path' => '/tmp/cache'
            ]
        ];
        
        $config->merge($additionalConfig);
        
        // 测试现有配置保持不变
        $this->assertEquals('SOPAN Test', $config->get('app.name'), 'Existing config should remain');
        
        // 测试新配置被添加
        $this->assertEquals('2.0.0', $config->get('app.version'), 'New config should be added');
        $this->assertTrue($config->get('app.debug'), 'New nested config should be added');
        $this->assertEquals('file', $config->get('cache.driver'), 'New section should be added');
    }
    
    /**
     * 测试环境配置
     */
    public function testEnvironmentConfig() {
        // 设置环境变量
        $_ENV['SOPAN_APP_NAME'] = 'ENV Test App';
        $_ENV['SOPAN_DATABASE_HOST'] = 'env.localhost';
        
        // 重新创建配置实例以加载环境变量
        $reflection = new ReflectionClass('Config');
        $instance = $reflection->getProperty('instance');
        $instance->setAccessible(true);
        $instance->setValue(null, null);
        
        $config = Config::getInstance();
        
        // 环境变量应该覆盖配置文件
        $this->assertEquals('ENV Test App', $config->get('app.name'), 'Environment variable should override config file');
        $this->assertEquals('env.localhost', $config->get('database.host'), 'Environment variable should set database host');
        
        // 清理环境变量
        unset($_ENV['SOPAN_APP_NAME']);
        unset($_ENV['SOPAN_DATABASE_HOST']);
    }
    
    /**
     * 测试配置验证
     */
    public function testConfigValidation() {
        $config = Config::getInstance();
        
        // 测试必需配置检查
        $requiredConfigs = ['app.name', 'database.host', 'database.username'];
        
        foreach ($requiredConfigs as $key) {
            $this->assertTrue($config->has($key), "Required config '{$key}' should exist");
            $this->assertNotEmpty($config->get($key), "Required config '{$key}' should not be empty");
        }
    }
    
    /**
     * 测试配置缓存
     */
    public function testConfigCaching() {
        $config = Config::getInstance();
        
        // 第一次获取（应该从文件读取）
        $startTime = microtime(true);
        $value1 = $config->get('app.name');
        $time1 = microtime(true) - $startTime;
        
        // 第二次获取（应该从缓存读取）
        $startTime = microtime(true);
        $value2 = $config->get('app.name');
        $time2 = microtime(true) - $startTime;
        
        $this->assertEquals($value1, $value2, 'Values should be the same');
        // 注：在实际应用中，缓存读取应该比文件读取快，但在测试环境中差异可能不明显
    }
    
    /**
     * 测试错误处理
     */
    public function testErrorHandling() {
        $config = Config::getInstance();
        
        // 测试无效的键格式
        $this->expectException('InvalidArgumentException', function() use ($config) {
            $config->get('');
        });
        
        // 测试设置null键
        $this->expectException('InvalidArgumentException', function() use ($config) {
            $config->set(null, 'value');
        });
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 创建测试配置文件
     */
    private function createTestConfigFile() {
        $configDir = dirname(__DIR__, 2) . '/config';
        if (!is_dir($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        $testConfig = [
            'app' => [
                'name' => 'SOPAN Test',
                'environment' => 'test',
                'url' => 'http://localhost'
            ],
            'database' => [
                'host' => 'localhost',
                'port' => 3306,
                'username' => 'test_user',
                'password' => 'test_pass',
                'database' => 'sopan_test',
                'charset' => 'utf8mb4'
            ],
            'test' => [
                'setting1' => 'value1',
                'setting2' => 'value2'
            ]
        ];
        
        file_put_contents($configDir . '/app.php', "<?php\nreturn " . var_export($testConfig, true) . ";\n");
    }
    
    /**
     * 清理测试配置文件
     */
    private function cleanupTestConfigFile() {
        $configFile = dirname(__DIR__, 2) . '/config/app.php';
        if (file_exists($configFile)) {
            unlink($configFile);
        }
    }
}