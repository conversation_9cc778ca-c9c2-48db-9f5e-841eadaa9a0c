<?php
/**
 * BaseController类的单元测试
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

require_once dirname(__DIR__) . '/BaseTestCase.php';

class BaseControllerTest extends BaseTestCase {
    private $controller;
    
    protected function setUp() {
        // 创建测试控制器
        $this->controller = new TestController();
        
        // 模拟HTTP请求环境
        $this->setupHttpEnvironment();
    }
    
    protected function tearDown() {
        // 清理HTTP环境
        $this->cleanupHttpEnvironment();
    }
    
    /**
     * 测试控制器初始化
     */
    public function testControllerInitialization() {
        $this->assertInstanceOf('BaseController', $this->controller);
        
        // 测试依赖注入
        $reflection = new ReflectionClass($this->controller);
        
        $configProperty = $reflection->getProperty('config');
        $configProperty->setAccessible(true);
        $config = $configProperty->getValue($this->controller);
        $this->assertInstanceOf('Config', $config);
        
        $dbProperty = $reflection->getProperty('db');
        $dbProperty->setAccessible(true);
        $db = $dbProperty->getValue($this->controller);
        $this->assertInstanceOf('Database', $db);
    }
    
    /**
     * 测试CORS设置
     */
    public function testCorsSetup() {
        // 模拟OPTIONS请求
        $_SERVER['REQUEST_METHOD'] = 'OPTIONS';
        $_SERVER['HTTP_ORIGIN'] = 'http://localhost:8080';
        
        ob_start();
        $controller = new TestController();
        $output = ob_get_clean();
        
        // 验证CORS头部
        $headers = headers_list();
        $this->assertContains('Access-Control-Allow-Origin: http://localhost:8080', $headers);
        $this->assertContains('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS', $headers);
    }
    
    /**
     * 测试请求参数解析
     */
    public function testRequestParsing() {
        // 测试GET参数
        $_GET['test_param'] = 'get_value';
        $value = $this->controller->testGetParam('test_param');
        $this->assertEquals('get_value', $value);
        
        // 测试POST参数
        $_POST['post_param'] = 'post_value';
        $value = $this->controller->testGetParam('post_param');
        $this->assertEquals('post_value', $value);
        
        // 测试默认值
        $value = $this->controller->testGetParam('nonexistent', 'default');
        $this->assertEquals('default', $value);
    }
    
    /**
     * 测试JSON请求体解析
     */
    public function testJsonRequestParsing() {
        // 模拟JSON请求
        $_SERVER['CONTENT_TYPE'] = 'application/json';
        
        $jsonData = json_encode(['key' => 'value', 'number' => 123]);
        
        // 由于无法直接模拟php://input，这里测试JSON解析逻辑
        $parsed = json_decode($jsonData, true);
        $this->assertEquals('value', $parsed['key']);
        $this->assertEquals(123, $parsed['number']);
    }
    
    /**
     * 测试成功响应
     */
    public function testSuccessResponse() {
        ob_start();
        $this->controller->testSuccess(['test' => 'data'], 'Success message');
        $output = ob_get_clean();
        
        $response = json_decode($output, true);
        
        $this->assertTrue($response['success']);
        $this->assertEquals(200, $response['code']);
        $this->assertEquals('Success message', $response['message']);
        $this->assertEquals('data', $response['data']['test']);
        $this->assertArrayHasKey('timestamp', $response);
        $this->assertArrayHasKey('request_id', $response);
    }
    
    /**
     * 测试错误响应
     */
    public function testErrorResponse() {
        ob_start();
        $this->controller->testError('Error message', 400);
        $output = ob_get_clean();
        
        $response = json_decode($output, true);
        
        $this->assertFalse($response['success']);
        $this->assertEquals(400, $response['error']['code']);
        $this->assertEquals('Error message', $response['error']['message']);
        $this->assertEquals('bad_request', $response['error']['type']);
        $this->assertArrayHasKey('timestamp', $response);
    }
    
    /**
     * 测试分页响应
     */
    public function testPaginateResponse() {
        $data = [
            ['id' => 1, 'name' => 'Item 1'],
            ['id' => 2, 'name' => 'Item 2'],
        ];
        
        ob_start();
        $this->controller->testPaginate($data, 50, 1, 10);
        $output = ob_get_clean();
        
        $response = json_decode($output, true);
        
        $this->assertTrue($response['success']);
        $this->assertCount(2, $response['data']['list']);
        $this->assertEquals(50, $response['data']['pagination']['total']);
        $this->assertEquals(1, $response['data']['pagination']['page']);
        $this->assertEquals(10, $response['data']['pagination']['size']);
        $this->assertEquals(5, $response['data']['pagination']['pages']);
    }
    
    /**
     * 测试参数验证
     */
    public function testParameterValidation() {
        // 测试必需参数验证 - 成功情况
        $_POST['required1'] = 'value1';
        $_POST['required2'] = 'value2';
        
        $result = $this->controller->testValidateRequired(['required1', 'required2']);
        $this->assertTrue($result);
        
        // 测试必需参数验证 - 失败情况
        unset($_POST['required2']);
        
        ob_start();
        $result = $this->controller->testValidateRequired(['required1', 'required2']);
        $output = ob_get_clean();
        
        $this->assertFalse($result);
        
        $response = json_decode($output, true);
        $this->assertFalse($response['success']);
        $this->assertEquals(422, $response['error']['code']);
    }
    
    /**
     * 测试HTTP方法验证
     */
    public function testMethodValidation() {
        // 测试允许的方法
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $result = $this->controller->testValidateMethod(['POST', 'PUT']);
        $this->assertTrue($result);
        
        // 测试不允许的方法
        $_SERVER['REQUEST_METHOD'] = 'DELETE';
        ob_start();
        $result = $this->controller->testValidateMethod(['GET', 'POST']);
        $output = ob_get_clean();
        
        $this->assertFalse($result);
        
        $response = json_decode($output, true);
        $this->assertEquals(405, $response['error']['code']);
    }
    
    /**
     * 测试速率限制
     */
    public function testRateLimit() {
        session_start();
        
        // 第一次请求应该成功
        $result = $this->controller->testRateLimit('test_key', 2, 60);
        $this->assertTrue($result);
        
        // 第二次请求应该成功
        $result = $this->controller->testRateLimit('test_key', 2, 60);
        $this->assertTrue($result);
        
        // 第三次请求应该被限制
        ob_start();
        $result = $this->controller->testRateLimit('test_key', 2, 60);
        $output = ob_get_clean();
        
        $this->assertFalse($result);
        
        $response = json_decode($output, true);
        $this->assertEquals(429, $response['error']['code']);
        $this->assertArrayHasKey('retry_after', $response['data']);
    }
    
    /**
     * 测试客户端IP获取
     */
    public function testClientIpDetection() {
        // 测试直接IP
        $_SERVER['REMOTE_ADDR'] = '*************';
        
        $reflection = new ReflectionClass($this->controller);
        $requestProperty = $reflection->getProperty('request');
        $requestProperty->setAccessible(true);
        $request = $requestProperty->getValue($this->controller);
        
        $this->assertEquals('*************', $request['ip']);
        
        // 测试代理IP
        $_SERVER['HTTP_X_FORWARDED_FOR'] = '***********, *************';
        $controller = new TestController();
        
        $requestProperty = $reflection->getProperty('request');
        $requestProperty->setAccessible(true);
        $request = $requestProperty->getValue($controller);
        
        $this->assertEquals('***********', $request['ip']);
    }
    
    /**
     * 测试请求头解析
     */
    public function testHeaderParsing() {
        $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer test-token';
        $_SERVER['HTTP_CONTENT_TYPE'] = 'application/json';
        $_SERVER['HTTP_USER_AGENT'] = 'Test User Agent';
        
        $controller = new TestController();
        
        $reflection = new ReflectionClass($controller);
        $requestProperty = $reflection->getProperty('request');
        $requestProperty->setAccessible(true);
        $request = $requestProperty->getValue($controller);
        
        $this->assertEquals('Bearer test-token', $request['headers']['Authorization']);
        $this->assertEquals('application/json', $request['headers']['Content-Type']);
        $this->assertEquals('Test User Agent', $request['headers']['User-Agent']);
    }
    
    /**
     * 测试错误类型映射
     */
    public function testErrorTypeMapping() {
        $controller = new TestController();
        
        // 测试不同错误码的类型映射
        $testCases = [
            400 => 'bad_request',
            401 => 'unauthorized',
            403 => 'forbidden',
            404 => 'not_found',
            422 => 'validation_error',
            429 => 'rate_limit',
            500 => 'server_error'
        ];
        
        foreach ($testCases as $code => $expectedType) {
            ob_start();
            $controller->testError('Test error', $code);
            $output = ob_get_clean();
            
            $response = json_decode($output, true);
            $this->assertEquals($expectedType, $response['error']['type'], "Error code {$code} should map to {$expectedType}");
        }
    }
    
    /**
     * 测试数据清理
     */
    public function testDataSanitization() {
        // 这里测试敏感数据清理逻辑
        $sensitiveData = [
            'username' => 'testuser',
            'password' => 'secret123',
            'token' => 'abc123',
            'normal_field' => 'normal_value'
        ];
        
        // 模拟数据清理（实际实现在BaseController中）
        $sanitized = $sensitiveData;
        unset($sanitized['password']);
        unset($sanitized['token']);
        
        $this->assertArrayNotHasKey('password', $sanitized);
        $this->assertArrayNotHasKey('token', $sanitized);
        $this->assertArrayHasKey('username', $sanitized);
        $this->assertArrayHasKey('normal_field', $sanitized);
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 设置HTTP环境
     */
    private function setupHttpEnvironment() {
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_URI'] = '/test';
        $_SERVER['HTTP_HOST'] = 'localhost';
        $_SERVER['SCRIPT_NAME'] = '/index.php';
        $_SERVER['REMOTE_ADDR'] = '127.0.0.1';
        $_SERVER['HTTP_USER_AGENT'] = 'PHPUnit Test';
        
        $_GET = [];
        $_POST = [];
    }
    
    /**
     * 清理HTTP环境
     */
    private function cleanupHttpEnvironment() {
        unset($_SERVER['REQUEST_METHOD']);
        unset($_SERVER['REQUEST_URI']);
        unset($_SERVER['HTTP_HOST']);
        unset($_SERVER['SCRIPT_NAME']);
        unset($_SERVER['REMOTE_ADDR']);
        unset($_SERVER['HTTP_USER_AGENT']);
        
        $_GET = [];
        $_POST = [];
    }
}

/**
 * 测试用的控制器类
 */
class TestController extends BaseController {
    
    public function testGetParam($key, $default = null) {
        return $this->getParam($key, $default);
    }
    
    public function testSuccess($data, $message = 'success') {
        $this->success($data, $message);
    }
    
    public function testError($message, $code = 400) {
        $this->error($message, $code);
    }
    
    public function testPaginate($data, $total, $page, $size) {
        $this->paginate($data, $total, $page, $size);
    }
    
    public function testValidateRequired($required) {
        return $this->validateRequired($required);
    }
    
    public function testValidateMethod($allowed) {
        return $this->validateMethod($allowed);
    }
    
    public function testRateLimit($key, $limit, $window) {
        return $this->rateLimitCheck($key, $limit, $window);
    }
}