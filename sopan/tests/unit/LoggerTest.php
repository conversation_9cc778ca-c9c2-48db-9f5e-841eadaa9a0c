<?php
/**
 * Logger类的单元测试
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

require_once dirname(__DIR__) . '/BaseTestCase.php';

class LoggerTest extends BaseTestCase {
    private $testLogDir;
    
    protected function setUp() {
        // 创建测试日志目录
        $this->testLogDir = sys_get_temp_dir() . '/sopan_test_logs_' . uniqid();
        mkdir($this->testLogDir, 0755, true);
        
        // 重置Logger实例
        $reflection = new ReflectionClass('Logger');
        $instance = $reflection->getProperty('instance');
        $instance->setAccessible(true);
        $instance->setValue(null, null);
        
        // 创建测试日志表
        $this->createLogTables();
    }
    
    protected function tearDown() {
        // 清理测试日志目录
        $this->cleanupLogDirectory();
        
        // 清理测试日志表
        $this->dropLogTables();
    }
    
    /**
     * 测试Logger单例模式
     */
    public function testSingleton() {
        $logger1 = Logger::getInstance();
        $logger2 = Logger::getInstance();
        
        $this->assertSame($logger1, $logger2, 'Logger should be singleton');
        $this->assertInstanceOf('Logger', $logger1);
    }
    
    /**
     * 测试基本日志记录
     */
    public function testBasicLogging() {
        $logger = Logger::getInstance();
        
        // 测试不同级别的日志记录
        $logger->info('Test info message');
        $logger->warning('Test warning message');
        $logger->error('Test error message');
        $logger->debug('Test debug message');
        
        // 验证文件日志
        $logFiles = glob($this->testLogDir . '/*.log');
        $this->assertGreaterThan(0, count($logFiles), 'Should create log files');
        
        // 验证日志内容
        $logContent = file_get_contents($logFiles[0]);
        $this->assertContains('Test info message', $logContent, 'Should contain info message');
        $this->assertContains('INFO', $logContent, 'Should contain log level');
    }
    
    /**
     * 测试搜索日志记录
     */
    public function testSearchLogging() {
        $logger = Logger::getInstance();
        
        $logger->logSearch(
            'test keyword',
            'all',
            5,
            1.25,
            '***********',
            true
        );
        
        // 验证数据库中的搜索日志
        $searchLogs = $this->db->findAll('search_logs', ['keyword' => 'test keyword']);
        $this->assertCount(1, $searchLogs, 'Should create search log record');
        
        $log = $searchLogs[0];
        $this->assertEquals('test keyword', $log['keyword']);
        $this->assertEquals('all', $log['search_type']);
        $this->assertEquals(5, $log['result_count']);
        $this->assertEquals(1.25, $log['response_time']);
        $this->assertEquals('***********', $log['user_ip']);
        $this->assertEquals(1, $log['is_success']);
    }
    
    /**
     * 测试转存日志记录
     */
    public function testTransferLogging() {
        $logger = Logger::getInstance();
        
        $logger->logTransfer(
            'https://example.com/share/123',
            'baidu',
            '***********',
            'success',
            'Transfer completed successfully'
        );
        
        // 验证文件日志包含转存信息
        $logFiles = glob($this->testLogDir . '/transfer_*.log');
        if (!empty($logFiles)) {
            $logContent = file_get_contents($logFiles[0]);
            $this->assertContains('Transfer', $logContent, 'Should contain transfer log');
            $this->assertContains('baidu', $logContent, 'Should contain platform info');
        }
    }
    
    /**
     * 测试API访问日志记录
     */
    public function testApiAccessLogging() {
        $logger = Logger::getInstance();
        
        $logger->logApiAccess(
            '/api/search/start',
            'POST',
            200,
            150.5,
            '***********'
        );
        
        // 验证API日志
        $logFiles = glob($this->testLogDir . '/api_*.log');
        if (!empty($logFiles)) {
            $logContent = file_get_contents($logFiles[0]);
            $this->assertContains('/api/search/start', $logContent);
            $this->assertContains('POST', $logContent);
        }
    }
    
    /**
     * 测试上下文信息记录
     */
    public function testContextLogging() {
        $logger = Logger::getInstance();
        
        $context = [
            'user_id' => 123,
            'session_id' => 'sess_abc123',
            'extra_data' => ['key' => 'value']
        ];
        
        $logger->error('Test error with context', $context);
        
        // 验证上下文信息被正确记录
        $systemLogs = $this->db->findAll('system_logs', ['message' => 'Test error with context']);
        $this->assertCount(1, $systemLogs, 'Should create system log with context');
        
        $log = $systemLogs[0];
        $contextData = json_decode($log['context'], true);
        $this->assertEquals(123, $contextData['user_id']);
        $this->assertEquals('sess_abc123', $contextData['session_id']);
    }
    
    /**
     * 测试日志级别过滤
     */
    public function testLogLevelFiltering() {
        $logger = Logger::getInstance();
        
        // 记录不同级别的日志
        $logger->debug('Debug message');
        $logger->info('Info message');
        $logger->warning('Warning message');
        $logger->error('Error message');
        $logger->critical('Critical message');
        
        // 验证所有级别都被记录到文件
        $logFiles = glob($this->testLogDir . '/*.log');
        $this->assertGreaterThan(0, count($logFiles));
        
        $logContent = file_get_contents($logFiles[0]);
        $this->assertContains('DEBUG', $logContent);
        $this->assertContains('INFO', $logContent);
        $this->assertContains('WARNING', $logContent);
        $this->assertContains('ERROR', $logContent);
        $this->assertContains('CRITICAL', $logContent);
    }
    
    /**
     * 测试文件轮转
     */
    public function testLogRotation() {
        $logger = Logger::getInstance();
        
        // 模拟大量日志写入来触发轮转
        // 注：这里简化测试，实际轮转需要达到文件大小限制
        for ($i = 0; $i < 100; $i++) {
            $logger->info("Log message number {$i}");
        }
        
        $logFiles = glob($this->testLogDir . '/*.log*');
        $this->assertGreaterThan(0, count($logFiles), 'Should create log files');
    }
    
    /**
     * 测试日志清理
     */
    public function testLogCleanup() {
        $logger = Logger::getInstance();
        
        // 记录一些日志
        $logger->info('Test message for cleanup');
        
        // 执行清理（模拟30天前的日志）
        $cleaned = $logger->cleanupOldLogs(0); // 清理所有日志
        
        // 验证清理结果
        $this->assertGreaterThanOrEqual(0, $cleaned, 'Should return number of cleaned files');
    }
    
    /**
     * 测试日志统计
     */
    public function testLogStats() {
        $logger = Logger::getInstance();
        
        // 记录一些日志
        $logger->info('Info message');
        $logger->warning('Warning message');
        $logger->error('Error message');
        
        $stats = $logger->getLogStats();
        
        $this->assertIsArray($stats, 'Should return stats array');
        $this->assertArrayHasKey('file_logs', $stats);
        $this->assertArrayHasKey('database_logs', $stats);
    }
    
    /**
     * 测试错误处理
     */
    public function testErrorHandling() {
        $logger = Logger::getInstance();
        
        // 测试在数据库不可用时的处理
        $this->db = null;
        
        // 应该仍然能够记录到文件
        $logger->info('Test message without database');
        
        $logFiles = glob($this->testLogDir . '/*.log');
        $this->assertGreaterThan(0, count($logFiles), 'Should still create file logs without database');
    }
    
    /**
     * 测试并发日志写入
     */
    public function testConcurrentLogging() {
        $logger = Logger::getInstance();
        
        // 模拟并发写入
        for ($i = 0; $i < 10; $i++) {
            $logger->info("Concurrent message {$i}");
        }
        
        $logFiles = glob($this->testLogDir . '/*.log');
        $this->assertGreaterThan(0, count($logFiles));
        
        $logContent = file_get_contents($logFiles[0]);
        for ($i = 0; $i < 10; $i++) {
            $this->assertContains("Concurrent message {$i}", $logContent);
        }
    }
    
    /**
     * 测试日志格式
     */
    public function testLogFormat() {
        $logger = Logger::getInstance();
        
        $logger->info('Format test message');
        
        $logFiles = glob($this->testLogDir . '/*.log');
        $logContent = file_get_contents($logFiles[0]);
        
        // 验证日志格式包含必要元素
        $this->assertRegExp('/\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]/', $logContent, 'Should contain timestamp');
        $this->assertContains('INFO.SYSTEM', $logContent, 'Should contain level and type');
        $this->assertContains('Format test message', $logContent, 'Should contain message');
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 创建测试日志表
     */
    private function createLogTables() {
        $this->db->query("
            CREATE TABLE IF NOT EXISTS system_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                level VARCHAR(20) NOT NULL,
                type VARCHAR(50) NOT NULL,
                message TEXT NOT NULL,
                context JSON,
                ip VARCHAR(45),
                request_id VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        $this->db->query("
            CREATE TABLE IF NOT EXISTS search_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                keyword VARCHAR(255) NOT NULL,
                user_ip VARCHAR(45) NOT NULL,
                user_agent VARCHAR(500),
                search_type VARCHAR(50),
                result_count INT DEFAULT 0,
                response_time DECIMAL(10,3),
                is_success TINYINT(1) DEFAULT 1,
                search_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
    }
    
    /**
     * 删除测试日志表
     */
    private function dropLogTables() {
        $this->db->query("DROP TABLE IF EXISTS system_logs");
        $this->db->query("DROP TABLE IF EXISTS search_logs");
    }
    
    /**
     * 清理测试日志目录
     */
    private function cleanupLogDirectory() {
        if (is_dir($this->testLogDir)) {
            $files = glob($this->testLogDir . '/*');
            foreach ($files as $file) {
                unlink($file);
            }
            rmdir($this->testLogDir);
        }
    }
}