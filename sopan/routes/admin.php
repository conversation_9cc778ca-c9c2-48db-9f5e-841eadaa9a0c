<?php
/**
 * SOPAN 后台管理路由配置
 * 支持动态后台路径的管理界面路由
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

// 获取动态后台路径
$adminPath = Config::get('admin_path', 'admin');

// 获取当前路径并去掉后台路径前缀
$currentPath = $router->getCurrentPath();
$adminSubPath = preg_replace('#^/' . preg_quote($adminPath, '#') . '#', '', $currentPath);
if ($adminSubPath === '') {
    $adminSubPath = '/';
}

// ==================== 后台管理路由 ====================

// 后台首页（重定向到仪表板）
$router->get('/', function() use ($adminPath) {
    header('Location: /' . $adminPath . '/dashboard');
    exit;
});

// 后台登录页面
$router->get('/login', function() {
    $controller = new AdminController();
    $controller->login();
});

// 后台登录处理
$router->post('/login', function() {
    $controller = new AdminController();
    $controller->login();
});

// 后台仪表板
$router->get('/dashboard', function() {
    $controller = new AdminController();
    $controller->dashboard();
});

// 仪表板数据API
$router->get('/dashboard/data', function() {
    $controller = new AdminController();
    $controller->dashboardData();
});

// 系统设置页面
$router->get('/settings', function() {
    $controller = new AdminController();
    $controller->settings();
});

// 系统设置保存
$router->post('/settings', function() {
    $controller = new AdminController();
    $controller->saveSettings();
});

// 网盘配置管理
$router->get('/config', function() {
    $controller = new AdminController();
    $controller->config();
});

// 配置管理API
$router->get('/config/platforms', function() {
    $controller = new AdminController();
    $controller->getPlatformsStatus();
});

$router->get('/config/platform/{platform}', function($platform) {
    $controller = new AdminController();
    $controller->getPlatformConfig($platform);
});

$router->post('/config/platform/{platform}', function($platform) {
    $controller = new AdminController();
    $controller->savePlatformConfig($platform);
});

$router->post('/config/test/{platform}', function($platform) {
    $controller = new AdminController();
    $controller->testPlatformConfig($platform);
});

$router->get('/config/system', function() {
    $controller = new AdminController();
    $controller->getSystemConfig();
});

$router->post('/config/system', function() {
    $controller = new AdminController();
    $controller->saveSystemConfig();
});

// 搜索日志管理
$router->get('/logs/search', function() {
    $controller = new AdminController();
    $controller->searchLogs();
});

// 系统日志
$router->get('/logs/system', function() {
    $controller = new AdminController();
    $controller->systemLogs();
});

// 错误日志
$router->get('/logs/error', function() {
    $controller = new AdminController();
    $controller->errorLogs();
});

// 转存记录管理
$router->get('/transfers', function() {
    $controller = new AdminController();
    $controller->transfers();
});

// 用户管理
$router->get('/users', function() {
    $controller = new AdminController();
    $controller->users();
});

// 管理员管理
$router->get('/admins', function() {
    $controller = new AdminController();
    $controller->admins();
});

// 新增管理员
$router->get('/admins/create', function() {
    $controller = new AdminController();
    $controller->createAdmin();
});

$router->post('/admins/create', function() {
    $controller = new AdminController();
    $controller->storeAdmin();
});

// 编辑管理员
$router->get('/admins/{id}/edit', function($id) {
    $controller = new AdminController();
    $controller->editAdmin($id);
});

$router->post('/admins/{id}/edit', function($id) {
    $controller = new AdminController();
    $controller->updateAdmin($id);
});

// 删除管理员
$router->post('/admins/{id}/delete', function($id) {
    $controller = new AdminController();
    $controller->deleteAdmin($id);
});

// 个人资料
$router->get('/profile', function() {
    $controller = new AdminController();
    $controller->profile();
});

$router->post('/profile', function() {
    $controller = new AdminController();
    $controller->updateProfile();
});

// 修改密码
$router->get('/password', function() {
    $controller = new AdminController();
    $controller->changePassword();
});

$router->post('/password', function() {
    $controller = new AdminController();
    $controller->updatePassword();
});

// 退出登录
$router->get('/logout', function() {
    $controller = new AdminController();
    $controller->logout();
});

// 清理缓存
$router->post('/cache/clear', function() {
    $controller = new AdminController();
    $controller->clearCache();
});

// 系统信息
$router->get('/info', function() {
    $controller = new AdminController();
    $controller->systemInfo();
});

// ==================== 后台路由分发 ====================

// 使用修改后的路径进行路由分发
try {
    $router->dispatch('GET', $adminSubPath);
} catch (Exception $e) {
    error_log('Admin Router Exception: ' . $e->getMessage());
    
    $controller = new AdminController();
    $controller->showError('后台路由处理错误', 500);
}