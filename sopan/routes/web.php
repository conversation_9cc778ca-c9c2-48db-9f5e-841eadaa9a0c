<?php
/**
 * SOPAN 前台路由配置
 * 支持动态后台路径和前台首页
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

// 加载路由器和控制器
require_once dirname(__DIR__) . '/core/Router.php';
require_once dirname(__DIR__) . '/app/controllers/IndexController.php';
require_once dirname(__DIR__) . '/app/controllers/AdminController.php';
require_once dirname(__DIR__) . '/core/Config.php';

// 创建路由器实例
$router = new Router();

// 获取当前请求路径
$currentPath = $router->getCurrentPath();

// 获取后台路径配置（默认为 admin）
$adminPath = Config::get('admin_path', 'admin');

// 检查是否为后台访问
if (preg_match('#^/' . preg_quote($adminPath, '#') . '(/.*)?$#', $currentPath)) {
    // 后台路由处理
    require_once dirname(__DIR__) . '/routes/admin.php';
    return;
}

// ==================== 前台页面路由 ====================

// 首页
$router->get('/', function() {
    $controller = new IndexController();
    $controller->index();
});

// 搜索结果页
$router->get('/search', function() {
    $controller = new IndexController();
    $controller->search();
});

// 关于页面
$router->get('/about', function() {
    $controller = new IndexController();
    $controller->about();
});

// 帮助页面
$router->get('/help', function() {
    $controller = new IndexController();
    $controller->help();
});

// 联系我们
$router->get('/contact', function() {
    $controller = new IndexController();
    $controller->contact();
});

// 用户协议
$router->get('/terms', function() {
    $controller = new IndexController();
    $controller->terms();
});

// 隐私政策
$router->get('/privacy', function() {
    $controller = new IndexController();
    $controller->privacy();
});

// 站点地图
$router->get('/sitemap', function() {
    $controller = new IndexController();
    $controller->sitemap();
});

// ==================== 404处理 ====================

// 设置404处理器
$router->setNotFoundHandler(function() {
    $controller = new IndexController();
    $controller->error404();
});

// ==================== 错误处理 ====================

// 设置错误处理器
$router->setErrorHandler(function($error) {
    error_log('Frontend Error: ' . $error);
    
    $controller = new IndexController();
    $controller->showError('系统错误：' . $error, 500);
});

// ==================== 路由分发 ====================

// 执行路由分发
try {
    $router->dispatch();
} catch (Exception $e) {
    error_log('Router Exception: ' . $e->getMessage());
    
    $controller = new IndexController();
    $controller->showError('路由处理错误', 500);
}