<?php
/**
 * SOPAN API路由配置
 * 定义所有API接口的路由规则
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

// 加载路由器和控制器
require_once dirname(__DIR__) . '/core/Router.php';
require_once dirname(__DIR__) . '/app/controllers/SearchController.php';
require_once dirname(__DIR__) . '/app/controllers/TransferController.php';
require_once dirname(__DIR__) . '/app/controllers/QRCodeController.php';
require_once dirname(__DIR__) . '/app/controllers/ErrorController.php';

// 创建路由器实例
$router = new Router();

// ==================== 搜索相关路由 ====================

// 开始异步搜索
$router->post('/api/search/start', function() {
    $controller = new SearchController();
    $controller->startSearch();
});

// SSE流式搜索
$router->get('/api/search/stream/{sessionId}', function($sessionId) {
    $controller = new SearchController();
    $controller->streamSearch($sessionId);
});

// 获取搜索状态
$router->get('/api/search/status/{sessionId}', function($sessionId) {
    $controller = new SearchController();
    $controller->getSearchStatus($sessionId);
});

// 获取搜索结果
$router->get('/api/search/results/{sessionId}', function($sessionId) {
    $controller = new SearchController();
    $controller->getSearchResults($sessionId);
});

// 取消搜索
$router->post('/api/search/cancel/{sessionId}', function($sessionId) {
    $controller = new SearchController();
    $controller->cancelSearch($sessionId);
});

// 获取热门搜索关键词
$router->get('/api/search/hot-keywords', function() {
    $controller = new SearchController();
    $controller->getHotKeywords();
});

// 获取搜索提示
$router->get('/api/search/suggestions', function() {
    $controller = new SearchController();
    $controller->getSearchSuggestions();
});

// ==================== 转存相关路由 ====================

// 开始转存（SearchController中的方法）
$router->post('/api/transfer/start', function() {
    $controller = new SearchController();
    $controller->startTransfer();
});

// 获取转存记录（SearchController中的方法）
$router->get('/api/transfer/records', function() {
    $controller = new SearchController();
    $controller->getTransferRecords();
});

// ==================== QR码相关路由 ====================

// 生成QR码
$router->post('/api/qrcode/generate', function() {
    $controller = new QRCodeController();
    $controller->generateQRCode();
});

// 生成网盘链接QR码
$router->post('/api/qrcode/netdisk', function() {
    $controller = new QRCodeController();
    $controller->generateNetdiskQRCode();
});

// 直接输出QR码图片
$router->get('/api/qrcode/image', function() {
    $controller = new QRCodeController();
    $controller->generateQRCodeImage();
});

// 验证链接
$router->post('/api/transfer/validate', function() {
    $controller = new TransferController();
    $controller->validateLink();
});

// 智能转存
$router->post('/api/transfer/smart', function() {
    $controller = new TransferController();
    $controller->smartTransfer();
});

// 批量转存
$router->post('/api/transfer/batch', function() {
    $controller = new TransferController();
    $controller->batchTransfer();
});

// 获取平台状态
$router->get('/api/transfer/platforms/status', function() {
    $controller = new TransferController();
    $controller->getPlatformStatus();
});

// 获取平台文件列表
$router->get('/api/transfer/platforms/{platform}/files', function($platform) {
    $controller = new TransferController();
    $controller->getPlatformFiles($platform);
});

// 获取转存记录
$router->get('/api/transfer/logs', function() {
    $controller = new TransferController();
    $controller->getTransferLogs();
});

// 获取转存统计
$router->get('/api/transfer/stats', function() {
    $controller = new TransferController();
    $controller->getTransferStats();
});

// 删除转存记录
$router->delete('/api/transfer/logs/{id}', function($id) {
    $controller = new TransferController();
    $controller->deleteTransferLog($id);
});

// 清理过期记录
$router->post('/api/transfer/cleanup', function() {
    $controller = new TransferController();
    $controller->cleanupExpiredLogs();
});

// 重试失败的转存
$router->post('/api/transfer/retry/{id}', function($id) {
    $controller = new TransferController();
    $controller->retryTransfer($id);
});

// ==================== 系统相关路由 ====================

// 错误报告
$router->post('/api/error/report', function() {
    $controller = new ErrorController();
    $controller->report();
});

// 系统健康检查
$router->get('/api/health', function() {
    $controller = new ErrorController();
    $controller->health();
});

// 错误统计
$router->get('/api/error/stats', function() {
    $controller = new ErrorController();
    $controller->stats();
});

// API信息
$router->get('/api/info', function() {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => true,
        'data' => [
            'name' => 'SOPAN API',
            'version' => '1.0.0',
            'description' => '专业的网盘资源搜索与转存API服务',
            'author' => 'SOPAN Project',
            'endpoints' => [
                'search' => [
                    'start' => 'POST /api/search/start',
                    'stream' => 'GET /api/search/stream/{sessionId}',
                    'status' => 'GET /api/search/status/{sessionId}',
                    'results' => 'GET /api/search/results/{sessionId}',
                    'cancel' => 'POST /api/search/cancel/{sessionId}',
                    'hot_keywords' => 'GET /api/search/hot-keywords',
                    'suggestions' => 'GET /api/search/suggestions'
                ],
                'transfer' => [
                    'validate' => 'POST /api/transfer/validate',
                    'smart' => 'POST /api/transfer/smart',
                    'batch' => 'POST /api/transfer/batch',
                    'platform_status' => 'GET /api/transfer/platforms/status',
                    'platform_files' => 'GET /api/transfer/platforms/{platform}/files',
                    'logs' => 'GET /api/transfer/logs',
                    'stats' => 'GET /api/transfer/stats',
                    'delete_log' => 'DELETE /api/transfer/logs/{id}',
                    'cleanup' => 'POST /api/transfer/cleanup',
                    'retry' => 'POST /api/transfer/retry/{id}'
                ],
                'system' => [
                    'health' => 'GET /api/health',
                    'info' => 'GET /api/info'
                ]
            ]
        ],
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
});

// ==================== 404处理 ====================

// 设置404处理器
$router->setNotFoundHandler(function() {
    http_response_code(404);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'code' => 404,
        'message' => 'API接口不存在',
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
});

// ==================== 错误处理 ====================

// 设置错误处理器
$router->setErrorHandler(function($error) {
    http_response_code(500);
    header('Content-Type: application/json; charset=utf-8');
    
    // 记录错误日志
    error_log('API Error: ' . $error);
    
    echo json_encode([
        'success' => false,
        'code' => 500,
        'message' => '服务器内部错误',
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
});

// ==================== 路由分发 ====================

// 执行路由分发
try {
    $router->dispatch();
} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: application/json; charset=utf-8');
    
    // 记录错误日志
    error_log('Router Exception: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'code' => 500,
        'message' => '路由处理错误',
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
}