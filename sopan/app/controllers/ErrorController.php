<?php
/**
 * SOPAN 错误报告控制器
 * 接收和处理前端错误报告
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

require_once __DIR__ . '/BaseController.php';

class ErrorController extends BaseController {
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * 接收错误报告
     * POST /api/error/report
     */
    public function report() {
        if (!$this->validateMethod(['POST'])) return;
        
        // 验证必需参数
        if (!$this->validateRequired(['type', 'message'])) return;
        
        $errorData = [
            'type' => $this->getParam('type'),
            'message' => $this->getParam('message'),
            'stack' => $this->getParam('stack', ''),
            'timestamp' => $this->getParam('timestamp'),
            'url' => $this->getParam('url', ''),
            'user_agent' => $this->getParam('userAgent', ''),
            'user_ip' => $this->request['ip'],
            'additional_info' => $this->getParam('context', [])
        ];
        
        try {
            // 记录错误到日志系统
            $this->logger->error('Frontend Error Report', [
                'error_type' => $errorData['type'],
                'error_message' => $errorData['message'],
                'stack_trace' => $errorData['stack'],
                'page_url' => $errorData['url'],
                'user_agent' => $errorData['user_agent'],
                'client_timestamp' => $errorData['timestamp'],
                'additional_info' => $errorData['additional_info']
            ]);
            
            // 记录到数据库
            $this->recordErrorReport($errorData);
            
            $this->success(null, '错误报告已接收');
            
        } catch (Exception $e) {
            $this->logger->error('Error report processing failed', [
                'exception_message' => $e->getMessage(),
                'original_error' => $errorData
            ]);
            
            $this->serverError('错误报告处理失败', $e);
        }
    }
    
    /**
     * 获取系统健康状态
     * GET /api/health
     */
    public function health() {
        if (!$this->validateMethod(['GET'])) return;
        
        $status = [
            'status' => 'ok',
            'timestamp' => time(),
            'version' => '1.0.0',
            'uptime' => $this->getUptime(),
            'checks' => []
        ];
        
        // 数据库连接检查
        try {
            $this->db->query('SELECT 1');
            $status['checks']['database'] = 'ok';
        } catch (Exception $e) {
            $status['checks']['database'] = 'error';
            $status['status'] = 'degraded';
        }
        
        // 磁盘空间检查
        $diskSpace = disk_free_space(dirname(__DIR__, 2));
        $diskTotal = disk_total_space(dirname(__DIR__, 2));
        $diskUsage = ($diskTotal - $diskSpace) / $diskTotal * 100;
        
        $status['checks']['disk_space'] = [
            'status' => $diskUsage < 90 ? 'ok' : 'warning',
            'usage_percent' => round($diskUsage, 2),
            'free_bytes' => $diskSpace,
            'total_bytes' => $diskTotal
        ];
        
        // 日志目录检查
        $logDir = dirname(__DIR__, 2) . '/logs';
        $status['checks']['logs'] = [
            'status' => is_writable($logDir) ? 'ok' : 'error',
            'writable' => is_writable($logDir)
        ];
        
        $this->success($status, 'Health check completed');
    }
    
    /**
     * 获取系统状态统计
     * GET /api/error/stats
     */
    public function stats() {
        if (!$this->validateMethod(['GET'])) return;
        
        try {
            // 获取最近24小时的错误统计
            $stats = [
                'error_reports' => $this->getErrorStats(),
                'api_requests' => $this->getApiStats(),
                'search_stats' => $this->getSearchStats(),
                'system_performance' => $this->getPerformanceStats()
            ];
            
            $this->success($stats, '统计数据获取成功');
            
        } catch (Exception $e) {
            $this->serverError('获取统计数据失败', $e);
        }
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 记录错误报告到数据库
     */
    private function recordErrorReport($errorData) {
        $this->db->insert('error_reports', [
            'error_type' => $errorData['type'],
            'error_message' => $errorData['message'],
            'stack_trace' => $errorData['stack'],
            'page_url' => $errorData['url'],
            'user_agent' => $errorData['user_agent'],
            'user_ip' => $errorData['user_ip'],
            'client_timestamp' => $errorData['timestamp'],
            'additional_info' => json_encode($errorData['additional_info'], JSON_UNESCAPED_UNICODE),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 获取系统运行时间
     */
    private function getUptime() {
        $uptimeFile = '/proc/uptime';
        if (file_exists($uptimeFile)) {
            $uptime = floatval(file_get_contents($uptimeFile));
            return round($uptime);
        }
        
        // Windows或其他系统的备选方案
        return time() - filectime(__FILE__);
    }
    
    /**
     * 获取错误统计
     */
    private function getErrorStats() {
        $yesterday = date('Y-m-d H:i:s', time() - 86400);
        
        return [
            'total_errors' => $this->db->count('error_reports', [
                'created_at >=' => $yesterday
            ]),
            'by_type' => $this->db->query(
                "SELECT error_type, COUNT(*) as count 
                 FROM error_reports 
                 WHERE created_at >= ? 
                 GROUP BY error_type 
                 ORDER BY count DESC",
                [$yesterday]
            ),
            'by_hour' => $this->db->query(
                "SELECT DATE_FORMAT(created_at, '%H') as hour, COUNT(*) as count 
                 FROM error_reports 
                 WHERE created_at >= ? 
                 GROUP BY hour 
                 ORDER BY hour",
                [$yesterday]
            )
        ];
    }
    
    /**
     * 获取API统计
     */
    private function getApiStats() {
        $yesterday = date('Y-m-d H:i:s', time() - 86400);
        
        // 如果有API访问日志表
        if ($this->tableExists('api_access_logs')) {
            return [
                'total_requests' => $this->db->count('api_access_logs', [
                    'created_time >=' => $yesterday
                ]),
                'by_method' => $this->db->query(
                    "SELECT method, COUNT(*) as count 
                     FROM api_access_logs 
                     WHERE created_time >= ? 
                     GROUP BY method",
                    [$yesterday]
                )
            ];
        }
        
        return ['message' => 'API access logs not available'];
    }
    
    /**
     * 获取搜索统计
     */
    private function getSearchStats() {
        $yesterday = date('Y-m-d H:i:s', time() - 86400);
        
        return [
            'total_searches' => $this->db->count('search_logs', [
                'search_time >=' => $yesterday
            ]),
            'success_rate' => $this->getSearchSuccessRate($yesterday),
            'popular_keywords' => $this->db->query(
                "SELECT keyword, COUNT(*) as count 
                 FROM search_logs 
                 WHERE search_time >= ? 
                 GROUP BY keyword 
                 ORDER BY count DESC 
                 LIMIT 10",
                [$yesterday]
            )
        ];
    }
    
    /**
     * 获取性能统计
     */
    private function getPerformanceStats() {
        return [
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit')
            ],
            'php_version' => PHP_VERSION,
            'load_average' => $this->getLoadAverage()
        ];
    }
    
    /**
     * 获取搜索成功率
     */
    private function getSearchSuccessRate($since) {
        $total = $this->db->count('search_logs', ['search_time >=' => $since]);
        $successful = $this->db->count('search_logs', [
            'search_time >=' => $since,
            'is_success' => 1
        ]);
        
        return $total > 0 ? round($successful / $total * 100, 2) : 0;
    }
    
    /**
     * 获取系统负载
     */
    private function getLoadAverage() {
        if (function_exists('sys_getloadavg')) {
            return sys_getloadavg();
        }
        
        return null;
    }
    
    /**
     * 检查表是否存在
     */
    private function tableExists($tableName) {
        try {
            $this->db->query("DESCRIBE {$tableName}");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}