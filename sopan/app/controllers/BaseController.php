<?php
/**
 * SOPAN API控制器基类
 * 提供通用的API响应方法和错误处理
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

abstract class BaseController {
    protected $config;
    protected $db;
    protected $request;
    protected $response;
    protected $logger;
    protected $startTime;
    
    public function __construct() {
        $this->startTime = microtime(true);
        $this->config = Config::getInstance();
        $this->db = Database::getInstance();
        $this->initializeLogger();
        $this->initializeRequest();
        $this->setupCors();
    }
    
    /**
     * 初始化日志记录器
     */
    private function initializeLogger() {
        require_once dirname(__DIR__, 2) . '/core/Logger.php';
        $this->logger = Logger::getInstance();
    }
    private function initializeRequest() {
        $this->request = [
            'method' => $_SERVER['REQUEST_METHOD'],
            'uri' => $_SERVER['REQUEST_URI'],
            'query' => $_GET,
            'body' => $this->getRequestBody(),
            'headers' => $this->getRequestHeaders(),
            'ip' => $this->getClientIp()
        ];
    }
    
    /**
     * 设置CORS头
     */
    private function setupCors() {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '*';
        $allowedOrigins = $this->config->get('api.allowed_origins', ['*']);
        
        if (in_array('*', $allowedOrigins) || in_array($origin, $allowedOrigins)) {
            header("Access-Control-Allow-Origin: {$origin}");
        }
        
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400');
        
        // 处理OPTIONS预检请求
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }
    
    /**
     * 获取请求体数据
     * 
     * @return array
     */
    private function getRequestBody() {
        $body = file_get_contents('php://input');
        
        if (empty($body)) {
            return $_POST;
        }
        
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        if (strpos($contentType, 'application/json') !== false) {
            $decoded = json_decode($body, true);
            return $decoded ?: [];
        }
        
        if (strpos($contentType, 'application/x-www-form-urlencoded') !== false) {
            parse_str($body, $parsed);
            return $parsed;
        }
        
        return [];
    }
    
    /**
     * 获取请求头
     * 
     * @return array
     */
    private function getRequestHeaders() {
        $headers = [];
        
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
        } else {
            foreach ($_SERVER as $key => $value) {
                if (strpos($key, 'HTTP_') === 0) {
                    $header = str_replace('_', '-', substr($key, 5));
                    $headers[$header] = $value;
                }
            }
        }
        
        return $headers;
    }
    
    /**
     * 获取客户端IP
     * 
     * @return string
     */
    private function getClientIp() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                return trim($ips[0]);
            }
        }
        
        return '127.0.0.1';
    }
    
    /**
     * 成功响应
     * 
     * @param mixed $data 数据
     * @param string $message 消息
     * @param int $code 状态码
     */
    protected function success($data = null, $message = 'success', $code = 200) {
        // 记录API访问日志
        $this->logApiAccess($code, microtime(true) - $this->startTime);
        
        $this->json([
            'success' => true,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'request_id' => $this->generateRequestId()
        ], $code);
    }
    
    /**
     * 错误响应
     * 
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 额外数据
     * @param array $context 错误上下文
     */
    protected function error($message = 'error', $code = 400, $data = null, $context = []) {
        // 记录错误日志
        $logContext = array_merge([
            'method' => $this->request['method'],
            'uri' => $this->request['uri'],
            'ip' => $this->request['ip'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_data' => $this->sanitizeLogData($this->request['body'])
        ], $context);
        
        $this->logger->error($message, $logContext);
        
        // 记录API访问日志
        $this->logApiAccess($code, microtime(true) - $this->startTime);
        
        $responseData = [
            'success' => false,
            'error' => [
                'code' => $code,
                'message' => $message,
                'type' => $this->getErrorType($code)
            ],
            'timestamp' => time(),
            'request_id' => $this->generateRequestId()
        ];
        
        if ($data !== null) {
            $responseData['data'] = $data;
        }
        
        $this->json($responseData, $this->getHttpStatusCode($code));
    }
    
    /**
     * 验证错误响应
     * 
     * @param array $errors 验证错误列表
     * @param string $message 主要错误消息
     */
    protected function validationError($errors, $message = '输入数据验证失败') {
        $this->error($message, 422, ['validation_errors' => $errors], [
            'error_type' => 'validation',
            'errors' => $errors
        ]);
    } 
     * @param array $data 数据列表
     * @param int $total 总数
     * @param int $page 当前页
     * @param int $size 每页数量
     * @param string $message 消息
     */
    protected function paginate($data, $total, $page, $size, $message = 'success') {
        $this->success([
            'list' => $data,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'size' => $size,
                'pages' => ceil($total / $size),
                'has_more' => ($page * $size) < $total
            ]
        ], $message);
    }
    
    /**
     * 输出JSON响应
     * 
     * @param array $data 数据
     * @param int $httpCode HTTP状态码
     */
    private function json($data, $httpCode = 200) {
        http_response_code($httpCode);
        header('Content-Type: application/json; charset=utf-8');
        
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }
    
    /**
     * 生成请求ID
     * 
     * @return string
     */
    private function generateRequestId() {
        return uniqid('req_', true);
    }
    
    /**
     * 验证必需参数
     * 
     * @param array $required 必需参数列表
     * @param array $data 数据源
     * @return bool
     */
    protected function validateRequired($required, $data = null) {
        $data = $data ?: array_merge($this->request['query'], $this->request['body']);
        $missing = [];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            $this->validationError([
                'missing_fields' => $missing
            ], '缺少必需参数：' . implode(', ', $missing));
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取参数值
     * 
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @param array $data 数据源
     * @return mixed
     */
    protected function getParam($key, $default = null, $data = null) {
        $data = $data ?: array_merge($this->request['query'], $this->request['body']);
        return $data[$key] ?? $default;
    }
    
    /**
     * 记录API访问日志
     * 
     * @param string $action 操作名称
     * @param array $extra 额外信息
     */
    protected function logAccess($action, $extra = []) {
        try {
            $logData = [
                'action' => $action,
                'method' => $this->request['method'],
                'uri' => $this->request['uri'],
                'ip' => $this->request['ip'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'referer' => $_SERVER['HTTP_REFERER'] ?? '',
                'created_time' => date('Y-m-d H:i:s')
            ];
            
            if (!empty($extra)) {
                $logData['extra_data'] = json_encode($extra, JSON_UNESCAPED_UNICODE);
            }
            
            $this->db->insert('api_access_logs', $logData);
        } catch (Exception $e) {
            error_log('API access log failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 速率限制检查
     * 
     * @param string $key 限制键
     * @param int $limit 限制次数
     * @param int $window 时间窗口（秒）
     * @return bool
     */
    protected function rateLimitCheck($key, $limit = 60, $window = 60) {
        $redisKey = "rate_limit:{$key}";
        
        // 简单的内存限制实现（生产环境建议使用Redis）
        session_start();
        $current = $_SESSION[$redisKey] ?? 0;
        $timestamp = $_SESSION[$redisKey . '_time'] ?? 0;
        
        if (time() - $timestamp > $window) {
            $_SESSION[$redisKey] = 1;
            $_SESSION[$redisKey . '_time'] = time;
            return true;
        }
        
        if ($current >= $limit) {
            $this->error('请求过于频繁，请稍后再试', 429, [
                'retry_after' => $window - (time() - $timestamp)
            ], [
                'error_type' => 'rate_limit',
                'limit' => $limit,
                'window' => $window
            ]);
            return false;
        }
        
        $_SESSION[$redisKey] = $current + 1;
        return true;
    }
    
    /**
     * 身份验证（简单实现）
     * 
     * @param bool $required 是否必需
     * @return bool
     */
    protected function authenticate($required = false) {
        $token = $this->request['headers']['Authorization'] ?? 
                 $this->getParam('token') ?? 
                 $this->getParam('access_token');
        
        if (empty($token)) {
            if ($required) {
                $this->error('未提供访问令牌', 401);
                return false;
            }
            return true;
        }
        
        // 简单的token验证（生产环境需要更严格的验证）
        $apiToken = $this->config->get('api.access_token', '');
        if (!empty($apiToken) && $token !== $apiToken) {
            $this->error('无效的访问令牌', 401);
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证请求方法
     * 
     * @param array $allowedMethods 允许的方法
     * @return bool
     */
    protected function validateMethod($allowedMethods) {
        if (!in_array($this->request['method'], $allowedMethods)) {
            $this->error('不支持的请求方法', 405, [
                'allowed_methods' => $allowedMethods,
                'current_method' => $this->request['method']
            ], [
                'error_type' => 'method_not_allowed',
                'allowed_methods' => $allowedMethods
            ]);
            return false;
        }
        return true;
    }
    
    // ========== 新增的工具方法 ==========
    
    /**
     * 获取错误类型
     * 
     * @param int $code 错误码
     * @return string
     */
    private function getErrorType($code) {
        $types = [
            400 => 'bad_request',
            401 => 'unauthorized', 
            403 => 'forbidden',
            404 => 'not_found',
            405 => 'method_not_allowed',
            409 => 'conflict',
            422 => 'validation_error',
            429 => 'rate_limit',
            500 => 'server_error',
            502 => 'bad_gateway',
            503 => 'service_unavailable',
            504 => 'gateway_timeout'
        ];
        
        return $types[$code] ?? 'unknown_error';
    }
    
    /**
     * 获取HTTP状态码
     * 
     * @param int $errorCode 错误码
     * @return int
     */
    private function getHttpStatusCode($errorCode) {
        // 将业务错误码映射为HTTP状态码
        if ($errorCode >= 400 && $errorCode < 600) {
            return $errorCode;
        }
        
        // 默认映射
        if ($errorCode >= 1000) {
            return 400; // 业务错误
        }
        
        return 500; // 服务器错误
    }
    
    /**
     * 清理日志数据（隐藏敏感信息）
     * 
     * @param array $data 原始数据
     * @return array
     */
    private function sanitizeLogData($data) {
        $sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***';
            }
        }
        
        return $data;
    }
    
    /**
     * 记录API访问日志
     * 
     * @param int $statusCode 状态码
     * @param float $responseTime 响应时间
     */
    private function logApiAccess($statusCode, $responseTime) {
        try {
            $this->logger->logApiAccess(
                $this->request['uri'],
                $this->request['method'],
                $statusCode,
                round($responseTime * 1000, 2), // 转换为毫秒
                $this->request['ip']
            );
        } catch (Exception $e) {
            error_log('API access log failed: ' . $e->getMessage());
        }
    }
}