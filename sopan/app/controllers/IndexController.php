<?php
/**
 * SOPAN 前台控制器
 * 处理搜索页面和结果页面的渲染
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class IndexController {
    private $config;
    private $db;
    private $view;
    
    public function __construct() {
        $this->config = Config::getInstance();
        $this->db = Database::getInstance();
        $this->view = new View();
        
        // 设置全局视图变量
        $this->setupGlobalViewVars();
    }
    
    /**
     * 设置全局视图变量
     */
    private function setupGlobalViewVars() {
        $this->view->assign('site_name', $this->config->get('site_name', 'SOPAN搜索'));
        $this->view->assign('site_description', $this->config->get('site_description', '专业的网盘资源搜索引擎'));
        $this->view->assign('site_keywords', $this->config->get('site_keywords', '网盘,搜索,资源'));
        $this->view->assign('site_logo', $this->config->get('site_logo', ''));
        $this->view->assign('icp_number', $this->config->get('icp_number', ''));
        $this->view->assign('current_year', date('Y'));
        $this->view->assign('base_url', $this->getBaseUrl());
        $this->view->assign('api_url', $this->getBaseUrl() . '/api');
    }
    
    /**
     * 首页
     */
    public function index() {
        try {
            // 获取热门搜索关键词
            $hotKeywords = $this->getHotKeywords(8);
            
            // 获取搜索提示词
            $suggestions = $this->getSearchSuggestions(6);
            
            // 获取网站统计
            $stats = $this->getSiteStats();
            
            // 获取广告内容
            $ads = $this->getAds(['header', 'sidebar']);
            
            $this->view->assignArray([
                'page_title' => $this->config->get('site_name', 'SOPAN搜索'),
                'meta_description' => $this->config->get('site_description', ''),
                'meta_keywords' => $this->config->get('site_keywords', ''),
                'hot_keywords' => $hotKeywords,
                'suggestions' => $suggestions,
                'stats' => $stats,
                'ads' => $ads,
                'show_stats' => true
            ]);
            
            echo $this->view->render('index');
            
        } catch (Exception $e) {
            $this->showError('页面加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 搜索结果页
     */
    public function search() {
        try {
            $keyword = trim($_GET['q'] ?? '');
            $type = $_GET['type'] ?? 'all';
            $page = max(1, (int)($_GET['page'] ?? 1));
            
            if (empty($keyword)) {
                header('Location: /');
                exit;
            }
            
            // 验证关键词长度
            if (mb_strlen($keyword, 'UTF-8') < 2) {
                $this->showError('搜索关键词至少需要2个字符');
                return;
            }
            
            if (mb_strlen($keyword, 'UTF-8') > 100) {
                $this->showError('搜索关键词不能超过100个字符');
                return;
            }
            
            // 记录搜索日志
            $this->logSearch($keyword, $type);
            
            // 获取热门搜索关键词
            $hotKeywords = $this->getHotKeywords(10);
            
            // 获取广告内容
            $ads = $this->getAds(['search_top', 'search_bottom']);
            
            // 获取平台状态
            $platformStatus = $this->getPlatformStatus();
            
            $this->view->assignArray([
                'page_title' => $keyword . ' - 搜索结果 - ' . $this->config->get('site_name', 'SOPAN搜索'),
                'meta_description' => '搜索"' . $keyword . '"的网盘资源结果',
                'meta_keywords' => $keyword . ',网盘搜索,资源搜索',
                'keyword' => $keyword,
                'search_type' => $type,
                'current_page' => $page,
                'hot_keywords' => $hotKeywords,
                'ads' => $ads,
                'platform_status' => $platformStatus,
                'is_search_page' => true
            ]);
            
            echo $this->view->render('search');
            
        } catch (Exception $e) {
            $this->showError('搜索页面加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 关于页面
     */
    public function about() {
        try {
            $this->view->assignArray([
                'page_title' => '关于我们 - ' . $this->config->get('site_name', 'SOPAN搜索'),
                'meta_description' => '了解SOPAN网盘搜索引擎的功能特色和使用方法',
                'meta_keywords' => '关于我们,网盘搜索,SOPAN'
            ]);
            
            echo $this->view->render('about');
            
        } catch (Exception $e) {
            $this->showError('页面加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 帮助页面
     */
    public function help() {
        try {
            $this->view->assignArray([
                'page_title' => '使用帮助 - ' . $this->config->get('site_name', 'SOPAN搜索'),
                'meta_description' => 'SOPAN网盘搜索引擎的使用说明和常见问题解答',
                'meta_keywords' => '使用帮助,网盘搜索,使用说明'
            ]);
            
            echo $this->view->render('help');
            
        } catch (Exception $e) {
            $this->showError('页面加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 联系我们页面
     */
    public function contact() {
        try {
            $this->view->assignArray([
                'page_title' => '联系我们 - ' . $this->config->get('site_name', 'SOPAN搜索'),
                'meta_description' => '联系SOPAN网盘搜索引擎团队',
                'meta_keywords' => '联系我们,意见反馈,客服'
            ]);
            
            echo $this->view->render('contact');
            
        } catch (Exception $e) {
            $this->showError('页面加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 错误页面
     */
    public function error404() {
        http_response_code(404);
        
        try {
            $this->view->assignArray([
                'page_title' => '页面未找到 - ' . $this->config->get('site_name', 'SOPAN搜索'),
                'meta_description' => '抱歉，您访问的页面不存在',
                'error_code' => 404,
                'error_message' => '抱歉，您访问的页面不存在'
            ]);
            
            echo $this->view->render('error');
            
        } catch (Exception $e) {
            echo '页面不存在';
        }
    }
    
    /**
     * 获取热门搜索关键词
     * 
     * @param int $limit 数量限制
     * @return array
     */
    private function getHotKeywords($limit = 10) {
        try {
            return $this->db->findAll('hot_keywords', 
                ['is_trending' => 1], 
                [
                    'order' => 'trend_score DESC, search_count DESC',
                    'limit' => $limit,
                    'fields' => 'keyword, search_count'
                ]
            );
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取搜索提示词
     * 
     * @param int $limit 数量限制
     * @return array
     */
    private function getSearchSuggestions($limit = 6) {
        try {
            return $this->db->findAll('search_suggestions', 
                ['status' => 1], 
                [
                    'order' => 'sort_order ASC, click_count DESC',
                    'limit' => $limit,
                    'fields' => 'keyword, description'
                ]
            );
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取网站统计信息
     * 
     * @return array
     */
    private function getSiteStats() {
        try {
            // 今日搜索次数
            $todaySearches = $this->db->count('search_logs', [
                'search_time >=' => date('Y-m-d 00:00:00'),
                'search_time <=' => date('Y-m-d 23:59:59')
            ]);
            
            // 总搜索次数
            $totalSearches = $this->db->count('search_logs');
            
            // 今日转存次数
            $todayTransfers = $this->db->count('transfer_logs', [
                'start_time >=' => date('Y-m-d 00:00:00'),
                'start_time <=' => date('Y-m-d 23:59:59'),
                'transfer_status' => 'success'
            ]);
            
            // 总转存次数
            $totalTransfers = $this->db->count('transfer_logs', [
                'transfer_status' => 'success'
            ]);
            
            return [
                'today_searches' => $todaySearches,
                'total_searches' => $totalSearches,
                'today_transfers' => $todayTransfers,
                'total_transfers' => $totalTransfers
            ];
        } catch (Exception $e) {
            return [
                'today_searches' => 0,
                'total_searches' => 0,
                'today_transfers' => 0,
                'total_transfers' => 0
            ];
        }
    }
    
    /**
     * 获取广告内容
     * 
     * @param array $positions 广告位置
     * @return array
     */
    private function getAds($positions) {
        try {
            $ads = [];
            foreach ($positions as $position) {
                $adList = $this->db->findAll('advertisements', [
                    'position' => $position,
                    'status' => 1,
                    'start_time <=' => date('Y-m-d H:i:s'),
                    'end_time >=' => date('Y-m-d H:i:s')
                ], [
                    'order' => 'sort_order ASC',
                    'limit' => 3
                ]);
                
                $ads[$position] = $adList;
            }
            return $ads;
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取平台状态
     * 
     * @return array
     */
    private function getPlatformStatus() {
        try {
            $platforms = [
                'quark' => $this->config->get('quark_status', '0'),
                'baidu' => $this->config->get('baidu_status', '0'),
                'aliyun' => $this->config->get('aliyun_status', '0'),
                'uc' => $this->config->get('uc_status', '0')
            ];
            
            $status = [];
            foreach ($platforms as $platform => $enabled) {
                $status[$platform] = [
                    'enabled' => $enabled === '1',
                    'name' => $this->getPlatformName($platform)
                ];
            }
            
            return $status;
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取平台名称
     * 
     * @param string $platform 平台标识
     * @return string
     */
    private function getPlatformName($platform) {
        $names = [
            'quark' => '夸克网盘',
            'baidu' => '百度网盘',
            'aliyun' => '阿里云盘',
            'uc' => 'UC网盘'
        ];
        
        return $names[$platform] ?? $platform;
    }
    
    /**
     * 记录搜索日志
     * 
     * @param string $keyword 关键词
     * @param string $type 搜索类型
     */
    private function logSearch($keyword, $type) {
        try {
            $this->db->insert('search_logs', [
                'keyword' => $keyword,
                'user_ip' => $this->getClientIp(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'referer' => $_SERVER['HTTP_REFERER'] ?? '',
                'search_type' => $type,
                'search_time' => date('Y-m-d H:i:s')
            ]);
            
            // 更新热门关键词
            $this->updateHotKeyword($keyword);
        } catch (Exception $e) {
            error_log('Search log failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新热门关键词
     * 
     * @param string $keyword 关键词
     */
    private function updateHotKeyword($keyword) {
        try {
            $existing = $this->db->find('hot_keywords', ['keyword' => $keyword]);
            
            if ($existing) {
                $this->db->update('hot_keywords', [
                    'search_count' => $existing['search_count'] + 1,
                    'last_search_time' => date('Y-m-d H:i:s'),
                    'trend_score' => $existing['trend_score'] + 0.1
                ], ['keyword' => $keyword]);
            } else {
                $this->db->insert('hot_keywords', [
                    'keyword' => $keyword,
                    'search_count' => 1,
                    'last_search_time' => date('Y-m-d H:i:s'),
                    'trend_score' => 1.0,
                    'created_date' => date('Y-m-d')
                ]);
            }
        } catch (Exception $e) {
            error_log('Update hot keyword failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取客户端IP
     * 
     * @return string
     */
    private function getClientIp() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                return trim($ips[0]);
            }
        }
        
        return '127.0.0.1';
    }
    
    /**
     * 获取基础URL
     * 
     * @return string
     */
    private function getBaseUrl() {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $script = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = dirname($script);
        
        if ($basePath === '/' || $basePath === '\\') {
            $basePath = '';
        }
        
        return $protocol . '://' . $host . $basePath;
    }
    
    /**
     * 显示错误页面
     * 
     * @param string $message 错误信息
     * @param int $code 错误码
     */
    private function showError($message, $code = 500) {
        http_response_code($code);
        
        try {
            $this->view->assignArray([
                'page_title' => '错误 - ' . $this->config->get('site_name', 'SOPAN搜索'),
                'meta_description' => '页面出现错误',
                'error_code' => $code,
                'error_message' => $message
            ]);
            
            echo $this->view->render('error');
        } catch (Exception $e) {
            echo '<h1>错误</h1><p>' . htmlspecialchars($message) . '</p>';
        }
    }
}