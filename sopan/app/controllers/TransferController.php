<?php
/**
 * SOPAN 转存API控制器
 * 提供网盘转存功能接口
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

require_once __DIR__ . '/BaseController.php';

class TransferController extends BaseController {
    private $transferService;
    
    public function __construct() {
        parent::__construct();
        $this->initializeTransferService();
    }
    
    /**
     * 初始化转存服务
     */
    private function initializeTransferService() {
        require_once dirname(__DIR__) . '/services/TransferService.php';
        $this->transferService = new TransferService();
    }
    
    /**
     * 验证链接
     * POST /api/transfer/validate
     */
    public function validateLink() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->rateLimitCheck($this->request['ip'], 20, 60)) return;
        
        // 验证参数
        if (!$this->validateRequired(['url'])) return;
        
        $url = $this->getParam('url');
        $code = $this->getParam('code', '');
        
        try {
            $result = $this->transferService->validateLink($url, $code);
            
            if ($result['success']) {
                $this->success($result['data'], $result['message']);
            } else {
                $this->error($result['message'], $result['error_code'] ?? 400);
            }
        } catch (Exception $e) {
            $this->error('链接验证失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 智能转存
     * POST /api/transfer/smart
     */
    public function smartTransfer() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->rateLimitCheck($this->request['ip'], 5, 60)) return;
        
        // 验证参数
        if (!$this->validateRequired(['url'])) return;
        
        $url = $this->getParam('url');
        $code = $this->getParam('code', '');
        $title = $this->getParam('title', '');
        $expiredType = (int)$this->getParam('expired_type', 1);
        $targetDir = $this->getParam('target_dir', '');
        $useNewVersion = $this->getParam('use_v2', false);
        
        // 记录转存请求
        $this->logAccess('transfer_request', [
            'url' => $url,
            'has_code' => !empty($code),
            'expired_type' => $expiredType,
            'use_v2' => $useNewVersion
        ]);
        
        try {
            // 选择转存方法
            if ($useNewVersion) {
                $result = $this->transferService->smartTransferV2($url, $code, $title, $expiredType, $targetDir);
            } else {
                $result = $this->transferService->smartTransfer($url, $code, $title, $expiredType, $targetDir);
            }
            
            if ($result['success']) {
                $this->success($result['data'], $result['message']);
            } else {
                $this->error($result['message'], $result['error_code'] ?? 400);
            }
        } catch (Exception $e) {
            $this->error('转存失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 批量转存
     * POST /api/transfer/batch
     */
    public function batchTransfer() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->rateLimitCheck($this->request['ip'], 2, 60)) return;
        
        // 验证参数
        if (!$this->validateRequired(['url', 'platforms'])) return;
        
        $url = $this->getParam('url');
        $code = $this->getParam('code', '');
        $platforms = $this->getParam('platforms');
        $title = $this->getParam('title', '');
        $expiredType = (int)$this->getParam('expired_type', 1);
        $targetDir = $this->getParam('target_dir', '');
        
        // 验证平台列表
        if (!is_array($platforms) || empty($platforms)) {
            $this->error('平台列表不能为空', 400);
            return;
        }
        
        $options = [
            'title' => $title,
            'expired_type' => $expiredType,
            'target_dir' => $targetDir
        ];
        
        try {
            $result = $this->transferService->batchTransferV2($url, $code, $platforms, $options);
            
            if ($result['success']) {
                $this->success($result, $result['message']);
            } else {
                $this->error($result['message'], $result['error_code'] ?? 400);
            }
        } catch (Exception $e) {
            $this->error('批量转存失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取平台状态
     * GET /api/transfer/platforms/status
     */
    public function getPlatformStatus() {
        if (!$this->validateMethod(['GET'])) return;
        
        try {
            $status = $this->transferService->getAllPlatformStatus();
            $this->success($status, '获取平台状态成功');
        } catch (Exception $e) {
            $this->error('获取平台状态失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取平台文件列表
     * GET /api/transfer/platforms/{platform}/files
     */
    public function getPlatformFiles($platform) {
        if (!$this->validateMethod(['GET'])) return;
        
        $parentId = $this->getParam('parent_id', '0');
        $page = (int)$this->getParam('page', 1);
        $size = (int)$this->getParam('size', 50);
        
        // 验证分页参数
        if ($page < 1) $page = 1;
        if ($size < 1 || $size > 100) $size = 50;
        
        try {
            $result = $this->transferService->getFiles($platform, $parentId);
            
            if ($result['success']) {
                // 简单分页处理
                $files = $result['data']['list'] ?? [];
                $total = count($files);
                $offset = ($page - 1) * $size;
                $pagedFiles = array_slice($files, $offset, $size);
                
                $this->paginate($pagedFiles, $total, $page, $size, '获取文件列表成功');
            } else {
                $this->error($result['message'], $result['error_code'] ?? 400);
            }
        } catch (Exception $e) {
            $this->error('获取文件列表失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取转存记录
     * GET /api/transfer/logs
     */
    public function getTransferLogs() {
        if (!$this->validateMethod(['GET'])) return;
        
        $page = (int)$this->getParam('page', 1);
        $size = (int)$this->getParam('size', 20);
        $status = $this->getParam('status', '');
        $platform = $this->getParam('platform', '');
        $startDate = $this->getParam('start_date', '');
        $endDate = $this->getParam('end_date', '');
        
        // 验证分页参数
        if ($page < 1) $page = 1;
        if ($size < 1 || $size > 100) $size = 20;
        
        try {
            $conditions = [];
            
            if (!empty($status)) {
                $conditions['transfer_status'] = $status;
            }
            
            if (!empty($platform)) {
                $conditions['target_platform'] = $platform;
            }
            
            // 日期范围查询需要特殊处理
            if (!empty($startDate)) {
                $conditions['start_time >='] = $startDate . ' 00:00:00';
            }
            
            if (!empty($endDate)) {
                $conditions['start_time <='] = $endDate . ' 23:59:59';
            }
            
            $total = $this->db->count('transfer_logs', $conditions);
            
            $logs = $this->db->findAll('transfer_logs', $conditions, [
                'order' => 'start_time DESC',
                'limit' => $size,
                'offset' => ($page - 1) * $size
            ]);
            
            $this->paginate($logs, $total, $page, $size, '获取转存记录成功');
            
        } catch (Exception $e) {
            $this->error('获取转存记录失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取转存统计
     * GET /api/transfer/stats
     */
    public function getTransferStats() {
        if (!$this->validateMethod(['GET'])) return;
        
        $timeRange = $this->getParam('time_range', '24h');
        
        try {
            $stats = $this->transferService->getTransferStats($timeRange);
            $this->success($stats, '获取转存统计成功');
        } catch (Exception $e) {
            $this->error('获取转存统计失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 删除转存记录
     * DELETE /api/transfer/logs/{id}
     */
    public function deleteTransferLog($id) {
        if (!$this->validateMethod(['DELETE'])) return;
        if (!$this->authenticate(true)) return; // 需要认证
        
        try {
            $deleted = $this->db->delete('transfer_logs', ['id' => $id]);
            
            if ($deleted) {
                $this->success(['id' => $id], '删除成功');
            } else {
                $this->error('记录不存在或删除失败', 404);
            }
        } catch (Exception $e) {
            $this->error('删除失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 清理过期记录
     * POST /api/transfer/cleanup
     */
    public function cleanupExpiredLogs() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->authenticate(true)) return; // 需要认证
        
        $days = (int)$this->getParam('days', 30);
        
        if ($days < 1 || $days > 365) {
            $this->error('保留天数必须在1-365天之间', 400);
            return;
        }
        
        try {
            $expireDate = date('Y-m-d H:i:s', time() - ($days * 24 * 3600));
            
            $deleted = $this->db->delete('transfer_logs', [
                'start_time <' => $expireDate
            ]);
            
            $this->success([
                'deleted_count' => $deleted,
                'expire_date' => $expireDate
            ], "清理了 {$deleted} 条过期记录");
            
        } catch (Exception $e) {
            $this->error('清理失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 重试失败的转存
     * POST /api/transfer/retry/{id}
     */
    public function retryTransfer($id) {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->rateLimitCheck($this->request['ip'], 3, 60)) return;
        
        try {
            $log = $this->db->find('transfer_logs', ['id' => $id]);
            
            if (!$log) {
                $this->error('转存记录不存在', 404);
                return;
            }
            
            if ($log['transfer_status'] !== 'failed') {
                $this->error('只能重试失败的转存记录', 400);
                return;
            }
            
            // 重新执行转存
            $result = $this->transferService->smartTransferV2(
                $log['original_url'],
                $log['original_code'],
                $log['resource_title'] ?? '',
                1, // 默认永久
                ''
            );
            
            if ($result['success']) {
                $this->success($result['data'], '重试转存成功');
            } else {
                $this->error($result['message'], $result['error_code'] ?? 400);
            }
            
        } catch (Exception $e) {
            $this->error('重试转存失败：' . $e->getMessage(), 500);
        }
    }
}