<?php
/**
 * SOPAN 后台管理控制器
 * 处理管理员登录和后台页面渲染
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class AdminController {
    private $config;
    private $db;
    private $view;
    private $configService;
    
    public function __construct() {
        $this->config = Config::getInstance();
        $this->db = Database::getInstance();
        $this->view = new View();
        
        // 加载配置服务
        require_once dirname(__DIR__) . '/services/ConfigService.php';
        $this->configService = new ConfigService();
        
        // 设置后台模板路径
        $this->view = new View(dirname(__DIR__) . '/views/admin/');
        
        // 设置全局变量
        $this->setupGlobalVars();
    }
    
    /**
     * 设置全局变量
     */
    private function setupGlobalVars() {
        $this->view->assign('site_name', $this->config->get('site_name', 'SOPAN搜索'));
        $this->view->assign('base_url', $this->getBaseUrl());
        $this->view->assign('admin_url', $this->getBaseUrl() . '/admin');
        $this->view->assign('api_url', $this->getBaseUrl() . '/api');
    }
    
    /**
     * 登录页面
     */
    public function login() {
        // 如果已登录，跳转到仪表板
        if ($this->isLoggedIn()) {
            header('Location: ' . $this->getBaseUrl() . '/admin/dashboard');
            exit;
        }
        
        $error = '';
        
        // 处理登录提交
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = trim($_POST['username'] ?? '');
            $password = $_POST['password'] ?? '';
            
            if (empty($username) || empty($password)) {
                $error = '用户名和密码不能为空';
            } else {
                $loginResult = $this->attemptLogin($username, $password);
                if ($loginResult['success']) {
                    header('Location: ' . $this->getBaseUrl() . '/admin/dashboard');
                    exit;
                } else {
                    $error = $loginResult['message'];
                }
            }
        }
        
        $this->view->assignArray([
            'page_title' => '管理员登录',
            'error' => $error
        ]);
        
        echo $this->view->render('login');
    }
    
    /**
     * 仪表板页面
     */
    public function dashboard() {
        $this->requireLogin();
        
        try {
            // 获取统计数据
            $stats = $this->getDashboardStats();
            
            // 获取最近搜索记录
            $recentSearches = $this->getRecentSearches(10);
            
            // 获取搜索趋势数据
            $searchTrend = $this->getSearchTrend(7);
            
            // 获取平台统计
            $platformStats = $this->getPlatformStats();
            
            // 获取系统信息
            $systemInfo = $this->getSystemInfo();
            
            $this->view->assignArray([
                'page_title' => '仪表板',
                'stats' => $stats,
                'recent_searches' => $recentSearches,
                'search_trend' => $searchTrend,
                'platform_stats' => $platformStats,
                'system_info' => $systemInfo,
                'current_admin' => $this->getCurrentAdmin()
            ]);
            
            echo $this->view->render('dashboard');
            
        } catch (Exception $e) {
            $this->showError('仪表板加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 仪表板数据API
     */
    public function dashboardData() {
        $this->requireLogin();
        
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            $data = [
                'stats' => $this->getDashboardStats(),
                'recent_searches' => $this->getRecentSearches(10),
                'search_trend' => $this->getSearchTrend(7),
                'platform_stats' => $this->getPlatformStats(),
                'system_info' => $this->getSystemInfo()
            ];
            
            echo json_encode([
                'success' => true,
                'data' => $data
            ], JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '获取数据失败：' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 系统设置页面
     */
    public function settings() {
        $this->requireLogin();
        
        try {
            // 获取当前设置
            $settings = [
                'site_name' => Config::get('site_name', 'SOPAN搜索'),
                'site_description' => Config::get('site_description', '专业的网盘资源搜索引擎'),
                'site_keywords' => Config::get('site_keywords', '网盘搜索,资源搜索,文件搜索,SOPAN'),
                'site_url' => Config::get('site_url', ''),
                'admin_path' => Config::getAdminPath(),
                'panseeker_api_url' => Config::get('panseeker_api_url', ''),
                'panseeker_api_key' => Config::get('panseeker_api_key', ''),
                'search_timeout' => Config::get('search_timeout', '30'),
                'max_results_per_page' => Config::get('max_results_per_page', '20'),
                'enable_search_log' => Config::get('enable_search_log', '1'),
                'enable_error_report' => Config::get('enable_error_report', '1'),
                'cache_expire_time' => Config::get('cache_expire_time', '3600'),
                'qr_code_expire_time' => Config::get('qr_code_expire_time', '86400')
            ];
            
            $this->view->assignArray([
                'page_title' => '系统设置',
                'settings' => $settings,
                'current_admin' => $this->getCurrentAdmin()
            ]);
            
            echo $this->view->render('settings');
            
        } catch (Exception $e) {
            $this->showError('设置页面加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 保存系统设置
     */
    public function saveSettings() {
        $this->requireLogin();
        
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            // 验证输入
            $settings = [
                'site_name' => trim($_POST['site_name'] ?? ''),
                'site_description' => trim($_POST['site_description'] ?? ''),
                'site_keywords' => trim($_POST['site_keywords'] ?? ''),
                'site_url' => trim($_POST['site_url'] ?? ''),
                'admin_path' => trim($_POST['admin_path'] ?? ''),
                'panseeker_api_url' => trim($_POST['panseeker_api_url'] ?? ''),
                'panseeker_api_key' => trim($_POST['panseeker_api_key'] ?? ''),
                'search_timeout' => (int)($_POST['search_timeout'] ?? 30),
                'max_results_per_page' => (int)($_POST['max_results_per_page'] ?? 20),
                'enable_search_log' => ($_POST['enable_search_log'] ?? '0') === '1' ? '1' : '0',
                'enable_error_report' => ($_POST['enable_error_report'] ?? '0') === '1' ? '1' : '0',
                'cache_expire_time' => (int)($_POST['cache_expire_time'] ?? 3600),
                'qr_code_expire_time' => (int)($_POST['qr_code_expire_time'] ?? 86400)
            ];
            
            // 验证必填项
            if (empty($settings['site_name'])) {
                throw new Exception('网站名称不能为空');
            }
            
            // 验证后台路径
            if (empty($settings['admin_path'])) {
                throw new Exception('后台路径不能为空');
            }
            
            if (!Config::isValidAdminPath($settings['admin_path'])) {
                throw new Exception('后台路径格式不正确或与系统保留路径冲突');
            }
            
            // 保存设置
            foreach ($settings as $key => $value) {
                if ($key === 'admin_path') {
                    Config::setAdminPath($value);
                } else {
                    Config::set($key, $value, true);
                }
            }
            
            // 记录操作日志
            $this->logAdminAction('update_settings', '更新系统设置', [
                'settings' => array_keys($settings)
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => '设置保存成功',
                'redirect_url' => $this->getBaseUrl() . '/' . $settings['admin_path'] . '/settings'
            ], JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '保存设置失败：' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 配置管理页面
     */
    public function config() {
        
        try {
            // 获取网盘平台配置
            $platforms = $this->getPlatformConfigs();
            
            $this->view->assignArray([
                'page_title' => '网盘配置',
                'platforms' => $platforms,
                'current_admin' => $this->getCurrentAdmin()
            ]);
            
            echo $this->view->render('config');
            
        } catch (Exception $e) {
            $this->showError('配置页面加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取所有平台状态API
     */
    public function getPlatformsStatus() {
        $this->requireLogin();
        
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            $platforms = $this->getPlatformConfigs();
            
            echo json_encode([
                'success' => true,
                'data' => $platforms
            ], JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '获取平台状态失败: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 获取单个平台配置API
     */
    public function getPlatformConfig($platform) {
        $this->requireLogin();
        
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            $config = $this->configService->getPlatformConfig($platform);
            
            echo json_encode([
                'success' => true,
                'data' => $config
            ], JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '获取平台配置失败: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 保存平台配置API
     */
    public function savePlatformConfig($platform) {
        $this->requireLogin();
        
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                echo json_encode([
                    'success' => false,
                    'message' => '无效的请求数据'
                ], JSON_UNESCAPED_UNICODE);
                return;
            }
            
            $result = $this->configService->setPlatformConfig($platform, $input);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => '配置保存成功'
                ], JSON_UNESCAPED_UNICODE);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '配置保存失败'
                ], JSON_UNESCAPED_UNICODE);
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '保存配置失败: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 测试平台配置API
     */
    public function testPlatformConfig($platform) {
        $this->requireLogin();
        
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            $result = $this->configService->testPlatformConfig($platform);
            
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '测试配置失败: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 获取系统配置API
     */
    public function getSystemConfig() {
        $this->requireLogin();
        
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            $allConfigs = $this->configService->getAllGroups();
            
            echo json_encode([
                'success' => true,
                'data' => $allConfigs
            ], JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '获取系统配置失败: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 保存系统配置API
     */
    public function saveSystemConfig() {
        $this->requireLogin();
        
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['configs'])) {
                echo json_encode([
                    'success' => false,
                    'message' => '无效的请求数据'
                ], JSON_UNESCAPED_UNICODE);
                return;
            }
            
            $configs = [];
            foreach ($input['configs'] as $key => $data) {
                $configs[$key] = $data;
            }
            
            $result = $this->configService->setMultiple($configs);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => '系统配置保存成功'
                ], JSON_UNESCAPED_UNICODE);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '系统配置保存失败'
                ], JSON_UNESCAPED_UNICODE);
            }
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '保存系统配置失败: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 系统日志管理页面
     */
    public function logs() {
        $this->requireLogin();
        
        try {
            $page = max(1, (int)($_GET['page'] ?? 1));
            $type = $_GET['type'] ?? 'all';
            $level = $_GET['level'] ?? '';
            $date = $_GET['date'] ?? '';
            $search = $_GET['search'] ?? '';
            
            $logsData = $this->getSystemLogs($page, 50, $type, $level, $date, $search);
            
            $this->view->assignArray([
                'page_title' => '系统日志',
                'logs' => $logsData['list'],
                'log_types' => $this->getLogTypes(),
                'pagination' => $this->buildPagination($page, 50, $logsData['total']),
                'current_admin' => $this->getCurrentAdmin()
            ]);
            
            echo $this->view->render('logs');
            
        } catch (Exception $e) {
            $this->showError('系统日志加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 转存记录管理页面
     */
    public function transfers() {
        $this->requireLogin();
        
        try {
            $page = max(1, (int)($_GET['page'] ?? 1));
            $status = $_GET['status'] ?? '';
            $platform = $_GET['platform'] ?? '';
            $date = $_GET['date'] ?? '';
            $search = $_GET['search'] ?? '';
            
            $transfersData = $this->getTransferRecords($page, 20, $status, $platform, $date, $search);
            $transferStats = $this->getTransferStats();
            
            $this->view->assignArray([
                'page_title' => '转存记录',
                'transfers' => $transfersData['list'],
                'stats' => $transferStats,
                'pagination' => $this->buildPagination($page, 20, $transfersData['total']),
                'current_admin' => $this->getCurrentAdmin()
            ]);
            
            echo $this->view->render('transfers');
            
        } catch (Exception $e) {
            $this->showError('转存记录加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 系统监控页面
     */
    public function monitor() {
        $this->requireLogin();
        
        try {
            // 获取系统监控数据
            $monitor = $this->getSystemMonitor();
            
            $this->view->assignArray([
                'page_title' => '系统监控',
                'monitor' => $monitor,
                'current_admin' => $this->getCurrentAdmin()
            ]);
            
            echo $this->view->render('monitor');
            
        } catch (Exception $e) {
            $this->showError('系统监控加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 错误报告管理页面
     */
    public function errorReports() {
        $this->requireLogin();
        
        try {
            $page = max(1, (int)($_GET['page'] ?? 1));
            $type = $_GET['type'] ?? '';
            $resolved = $_GET['resolved'] ?? '';
            $date = $_GET['date'] ?? '';
            $search = $_GET['search'] ?? '';
            
            $reportsData = $this->getErrorReports($page, 20, $type, $resolved, $date, $search);
            $reportStats = $this->getErrorReportStats();
            
            $this->view->assignArray([
                'page_title' => '错误报告',
                'reports' => $reportsData['list'],
                'stats' => $reportStats,
                'pagination' => $this->buildPagination($page, 20, $reportsData['total']),
                'current_admin' => $this->getCurrentAdmin()
            ]);
            
            echo $this->view->render('error_reports');
            
        } catch (Exception $e) {
            $this->showError('错误报告加载失败：' . $e->getMessage());
        }
    }
    
    /**
     * 标记错误报告为已解决
     */
    public function resolveErrorReport($reportId) {
        $this->requireLogin();
        
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            $note = $_POST['resolution_note'] ?? '';
            
            $this->db->update('error_reports', [
                'is_resolved' => 1,
                'resolution_note' => $note,
                'updated_at' => date('Y-m-d H:i:s')
            ], ['id' => $reportId]);
            
            echo json_encode([
                'success' => true,
                'message' => '错误报告已标记为已解决'
            ], JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '操作失败：' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 清理日志API
     */
    public function clearLogs() {
        $this->requireLogin();
        
        header('Content-Type: application/json; charset=utf-8');
        
        try {
            $type = $_POST['type'] ?? 'all';
            $date = $_POST['date'] ?? '';
            $olderThan = (int)($_POST['older_than'] ?? 30); // 默认清理30天以前的日志
            
            $conditions = [];
            if ($type !== 'all') {
                $conditions['type'] = $type;
            }
            
            if ($date) {
                $conditions['DATE(created_at)'] = $date;
            } else {
                $conditions['created_at <'] = date('Y-m-d H:i:s', time() - $olderThan * 86400);
            }
            
            $deletedCount = $this->db->delete('system_logs', $conditions);
            
            // 记录清理操作
            $this->logAdminAction('clear_logs', [
                'type' => $type,
                'date' => $date,
                'older_than' => $olderThan,
                'deleted_count' => $deletedCount
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => "已清理 {$deletedCount} 条日志记录"
            ], JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => '清理失败：' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 退出登录
     */
    public function logout() {
        session_start();
        session_destroy();
        
        header('Location: ' . $this->getBaseUrl() . '/admin/login');
        exit;
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 检查是否已登录
     */
    private function isLoggedIn() {
        session_start();
        return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
    }
    
    /**
     * 要求登录
     */
    private function requireLogin() {
        if (!$this->isLoggedIn()) {
            header('Location: ' . $this->getBaseUrl() . '/admin/login');
            exit;
        }
    }
    
    /**
     * 尝试登录
     */
    private function attemptLogin($username, $password) {
        try {
            $admin = $this->db->find('admins', [
                'username' => $username,
                'status' => 1
            ]);
            
            if (!$admin) {
                return ['success' => false, 'message' => '用户名不存在或已被禁用'];
            }
            
            // 检查是否被锁定
            if ($admin['locked_until'] && strtotime($admin['locked_until']) > time()) {
                return ['success' => false, 'message' => '账户已被锁定，请稍后再试'];
            }
            
            // 验证密码
            if (!password_verify($password, $admin['password'])) {
                // 增加失败次数
                $this->incrementFailedAttempts($admin['id']);
                return ['success' => false, 'message' => '密码错误'];
            }
            
            // 登录成功，创建会话
            $this->createSession($admin);
            
            // 更新登录信息
            $this->updateLoginInfo($admin['id']);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            error_log('Login error: ' . $e->getMessage());
            return ['success' => false, 'message' => '登录系统错误'];
        }
    }
    
    /**
     * 创建会话
     */
    private function createSession($admin) {
        session_start();
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['admin_role'] = $admin['role'];
        $_SESSION['login_time'] = time();
    }
    
    /**
     * 获取当前管理员
     */
    private function getCurrentAdmin() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        session_start();
        return [
            'id' => $_SESSION['admin_id'],
            'username' => $_SESSION['admin_username'],
            'role' => $_SESSION['admin_role'],
            'login_time' => $_SESSION['login_time']
        ];
    }
    
    /**
     * 获取仪表板统计数据
     */
    private function getDashboardStats() {
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        
        // 基础统计
        $totalSearches = $this->db->count('search_logs');
        $todaySearches = $this->db->count('search_logs', ['DATE(created_at)' => $today]);
        $yesterdaySearches = $this->db->count('search_logs', ['DATE(created_at)' => $yesterday]);
        
        $totalTransfers = $this->db->count('transfer_logs');
        $successTransfers = $this->db->count('transfer_logs', ['transfer_status' => 'success']);
        $todayTransfers = $this->db->count('transfer_logs', ['DATE(start_time)' => $today]);
        
        $totalVisits = $this->db->count('access_logs');
        $todayVisits = $this->db->count('access_logs', ['DATE(created_at)' => $today]);
        
        return [
            'total_searches' => $totalSearches,
            'today_searches' => $todaySearches,
            'yesterday_searches' => $yesterdaySearches,
            'total_transfers' => $totalTransfers,
            'success_transfers' => $successTransfers,
            'today_transfers' => $todayTransfers,
            'success_rate' => $totalTransfers > 0 ? $successTransfers / $totalTransfers : 0,
            'total_visits' => $totalVisits,
            'today_visits' => $todayVisits,
            'online_users' => $this->getOnlineUsers()
        ];
    }
    
    /**
     * 获取搜索趋势数据
     */
    private function getSearchTrend($days = 7) {
        $trend = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $count = $this->db->count('search_logs', ['DATE(created_at)' => $date]);
            $trend[] = [
                'date' => date('m-d', strtotime($date)),
                'count' => $count
            ];
        }
        return $trend;
    }
    
    /**
     * 获取平台统计数据
     */
    private function getPlatformStats() {
        $platforms = [
            ['name' => '百度网盘', 'key' => 'baidu', 'color' => 'bg-blue-500'],
            ['name' => '夸克网盘', 'key' => 'quark', 'color' => 'bg-green-500'],
            ['name' => '阿里云盘', 'key' => 'aliyun', 'color' => 'bg-orange-500'],
            ['name' => 'UC网盘', 'key' => 'uc', 'color' => 'bg-purple-500']
        ];
        
        $total = $this->db->count('transfer_logs');
        $stats = [];
        
        foreach ($platforms as $platform) {
            $count = $this->db->count('transfer_logs', ['target_platform' => $platform['key']]);
            $stats[] = [
                'name' => $platform['name'],
                'count' => $count,
                'percentage' => $total > 0 ? round(($count / $total) * 100, 1) : 0,
                'color' => $platform['color']
            ];
        }
        
        return $stats;
    }
    
    /**
     * 获取系统信息
     */
    private function getSystemInfo() {
        $diskUsage = $this->getDiskSpace();
        $memoryUsage = $this->getMemoryUsage();
        $load = $this->getServerLoad();
        
        return [
            'php_version' => PHP_VERSION,
            'load_average' => $load ? $load['1min'] : 'N/A',
            'cpu_usage' => $load ? min(100, round($load['1min'] * 100, 1)) : 0,
            'memory_usage' => $memoryUsage['usage_percent'],
            'disk_usage' => $diskUsage['usage_percent'],
            'db_size' => $this->getDatabaseSize(),
            'uptime' => $this->getSystemUptime(),
            'status_text' => '正常运行',
            'platform_status' => $this->getPlatformStatusList()
        ];
    }
    
    /**
     * 获取网盘平台配置
     */
    private function getPlatformConfigs() {
        return [
            [
                'key' => 'quark',
                'name' => '夸克网盘',
                'description' => '支持高速转存，智能去广告',
                'status' => $this->config->get('quark_status', '0')
            ],
            [
                'key' => 'baidu',
                'name' => '百度网盘',
                'description' => '最大的网盘平台，用户基数最多',
                'status' => $this->config->get('baidu_status', '0')
            ],
            [
                'key' => 'aliyun',
                'name' => '阿里云盘',
                'description' => '阿里巴巴旗下云存储服务',
                'status' => $this->config->get('aliyun_status', '0')
            ]
        ];
    }
    
    /**
     * 获取在线用户数
     */
    private function getOnlineUsers() {
        // 统计最近5分钟内的活跃用户
        $fiveMinutesAgo = date('Y-m-d H:i:s', time() - 300);
        try {
            return $this->db->query(
                "SELECT COUNT(DISTINCT user_ip) as count FROM access_logs WHERE created_at >= ?",
                [$fiveMinutesAgo]
            )[0]['count'] ?? 0;
        } catch (Exception $e) {
            return 0;
        }
    }
    
    // ==================== 辅助方法 ====================
    
    private function incrementFailedAttempts($adminId) {
        $this->db->update('admins', [
            'failed_attempts' => 'failed_attempts + 1',
            'locked_until' => date('Y-m-d H:i:s', time() + 900) // 锁定15分钟
        ], ['id' => $adminId]);
    }
    
    private function updateLoginInfo($adminId) {
        $this->db->update('admins', [
            'last_login_ip' => $this->getClientIp(),
            'last_login_time' => date('Y-m-d H:i:s'),
            'login_count' => 'login_count + 1',
            'failed_attempts' => 0,
            'locked_until' => null
        ], ['id' => $adminId]);
    }
    
    private function getPlatformStatusList() {
        return [
            ['name' => '夸克', 'online' => $this->config->get('quark_status', '0') === '1'],
            ['name' => '百度', 'online' => $this->config->get('baidu_status', '0') === '1'],
            ['name' => '阿里', 'online' => $this->config->get('aliyun_status', '0') === '1']
        ];
    }
    
    private function getDatabaseSize() {
        try {
            $result = $this->db->query(
                "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS size_mb
                 FROM information_schema.tables
                 WHERE table_schema = DATABASE()"
            );
            return ($result[0]['size_mb'] ?? 0) . ' MB';
        } catch (Exception $e) {
            return 'N/A';
        }
    }
    
    private function getSystemUptime() {
        if (function_exists('sys_getloadavg') && file_exists('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptime = explode(' ', $uptime);
            $seconds = (int)$uptime[0];
            
            $days = floor($seconds / 86400);
            $hours = floor(($seconds % 86400) / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            
            return "{$days}天{$hours}小时{$minutes}分钟";
        }
        return 'N/A';
    }
    
    private function getDiskSpace() {
        $total = disk_total_space('.');
        $free = disk_free_space('.');
        $used = $total - $free;
        
        return [
            'total' => $this->formatBytes($total),
            'used' => $this->formatBytes($used),
            'free' => $this->formatBytes($free),
            'usage_percent' => round(($used / $total) * 100, 2)
        ];
    }
    
    private function getMemoryUsage() {
        $usage = memory_get_usage(true);
        $peak = memory_get_peak_usage(true);
        $limit = $this->parseSize(ini_get('memory_limit'));
        
        return [
            'current' => $this->formatBytes($usage),
            'peak' => $this->formatBytes($peak),
            'limit' => $this->formatBytes($limit),
            'usage_percent' => $limit ? round(($usage / $limit) * 100, 2) : 0
        ];
    }
    
    private function getServerLoad() {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => $load[0],
                '5min' => $load[1],
                '15min' => $load[2]
            ];
        }
        return null;
    }
    
    private function getDatabaseConnections() {
        try {
            $result = $this->db->query('SHOW STATUS WHERE Variable_name = "Threads_connected"');
            return $result[0]['Value'] ?? 0;
        } catch (Exception $e) {
            return 0;
        }
    }
    
    private function getTableCount() {
        try {
            $result = $this->db->query('SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE()');
            return $result[0]['count'] ?? 0;
        } catch (Exception $e) {
            return 0;
        }
    }
    
    private function formatBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }
    
    private function parseSize($size) {
        $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
        $size = preg_replace('/[^0-9\.]/', '', $size);
        if ($unit) {
            return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
        }
        return round($size);
    }
    
    private function getClientIp() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                return trim($ips[0]);
            }
        }
        
        return '127.0.0.1';
    }
    
    /**
     * 获取系统日志
     */
    private function getSystemLogs($page, $size, $type = 'all', $level = '', $date = '', $search = '') {
        $conditions = [];
        $offset = ($page - 1) * $size;
        
        if ($type !== 'all') {
            $conditions['type'] = $type;
        }
        
        if (!empty($level)) {
            $conditions['level'] = $level;
        }
        
        if (!empty($date)) {
            $conditions['DATE(created_at)'] = $date;
        }
        
        // 构建查询
        $whereClause = [];
        $params = [];
        
        foreach ($conditions as $field => $value) {
            $whereClause[] = "$field = ?";
            $params[] = $value;
        }
        
        if (!empty($search)) {
            $whereClause[] = "(message LIKE ? OR context LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as count FROM system_logs $whereStr";
        $totalResult = $this->db->query($countSql, $params);
        $total = $totalResult[0]['count'] ?? 0;
        
        // 获取日志列表
        $sql = "SELECT * FROM system_logs $whereStr ORDER BY created_at DESC LIMIT $offset, $size";
        $logs = $this->db->query($sql, $params);
        
        return [
            'list' => $logs,
            'total' => $total
        ];
    }
    
    /**
     * 获取日志类型
     */
    private function getLogTypes() {
        try {
            $types = $this->db->query(
                "SELECT type, COUNT(*) as count FROM system_logs GROUP BY type ORDER BY count DESC"
            );
        } catch (Exception $e) {
            $types = [];
        }
        
        $result = [['key' => 'all', 'name' => '全部', 'count' => 0]];
        $totalCount = 0;
        
        foreach ($types as $type) {
            $result[] = [
                'key' => $type['type'],
                'name' => $this->getLogTypeName($type['type']),
                'count' => (int)$type['count']
            ];
            $totalCount += (int)$type['count'];
        }
        
        $result[0]['count'] = $totalCount;
        
        return $result;
    }
    
    /**
     * 获取日志类型名称
     */
    private function getLogTypeName($type) {
        $names = [
            'search' => '搜索日志',
            'transfer' => '转存日志',
            'system' => '系统日志',
            'error' => '错误日志',
            'api' => 'API日志'
        ];
        return $names[$type] ?? $type;
    }
    
    /**
     * 获取转存记录
     */
    private function getTransferRecords($page, $size, $status = '', $platform = '', $date = '', $search = '') {
        $conditions = [];
        $offset = ($page - 1) * $size;
        
        if (!empty($status)) {
            $conditions['transfer_status'] = $status;
        }
        
        if (!empty($platform)) {
            $conditions['target_platform'] = $platform;
        }
        
        if (!empty($date)) {
            $conditions['DATE(start_time)'] = $date;
        }
        
        // 构建查询
        $whereClause = [];
        $params = [];
        
        foreach ($conditions as $field => $value) {
            $whereClause[] = "$field = ?";
            $params[] = $value;
        }
        
        if (!empty($search)) {
            $whereClause[] = "(original_url LIKE ? OR user_ip LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as count FROM transfer_logs $whereStr";
        $totalResult = $this->db->query($countSql, $params);
        $total = $totalResult[0]['count'] ?? 0;
        
        // 获取记录列表
        $sql = "SELECT * FROM transfer_logs $whereStr ORDER BY start_time DESC LIMIT $offset, $size";
        $transfers = $this->db->query($sql, $params);
        
        return [
            'list' => $transfers,
            'total' => $total
        ];
    }
    
    /**
     * 获取转存统计
     */
    private function getTransferStats() {
        $totalTransfers = $this->db->count('transfer_logs');
        $successTransfers = $this->db->count('transfer_logs', ['transfer_status' => 'success']);
        $failedTransfers = $this->db->count('transfer_logs', ['transfer_status' => 'failed']);
        
        return [
            'total_transfers' => $totalTransfers,
            'success_transfers' => $successTransfers,
            'failed_transfers' => $failedTransfers,
            'success_rate' => $totalTransfers > 0 ? $successTransfers / $totalTransfers : 0
        ];
    }
    
    /**
     * 构建分页数据
     */
    private function buildPagination($currentPage, $perPage, $total) {
        return [
            'current_page' => $currentPage,
            'per_page' => $perPage,
            'total' => $total,
            'last_page' => ceil($total / $perPage),
            'from' => (($currentPage - 1) * $perPage) + 1,
            'to' => min($currentPage * $perPage, $total)
        ];
    }
    
    private function getBaseUrl() {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $script = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = dirname($script);
        
        if ($basePath === '/' || $basePath === '\\') {
            $basePath = '';
        }
        
        return $protocol . '://' . $host . $basePath;
    }
    
    private function showError($message, $code = 500) {
        http_response_code($code);
        
        try {
            $this->view->assignArray([
                'page_title' => '错误',
                'error_message' => $message,
                'error_code' => $code
            ]);
            
            echo $this->view->render('error');
        } catch (Exception $e) {
            echo '<h1>系统错误</h1><p>' . htmlspecialchars($message) . '</p>';
        }
    }
    
    // ==================== 错误报告和监控相关方法 ====================
    
    /**
     * 获取错误报告
     */
    private function getErrorReports($page, $size, $type = '', $resolved = '', $date = '', $search = '') {
        $conditions = [];
        $offset = ($page - 1) * $size;
        
        if (!empty($type)) {
            $conditions['error_type'] = $type;
        }
        
        if ($resolved !== '') {
            $conditions['is_resolved'] = (int)$resolved;
        }
        
        if (!empty($date)) {
            $conditions['DATE(created_at)'] = $date;
        }
        
        // 构建查询
        $whereClause = [];
        $params = [];
        
        foreach ($conditions as $field => $value) {
            $whereClause[] = "$field = ?";
            $params[] = $value;
        }
        
        if (!empty($search)) {
            $whereClause[] = "(error_message LIKE ? OR page_url LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $whereStr = !empty($whereClause) ? 'WHERE ' . implode(' AND ', $whereClause) : '';
        
        try {
            // 获取总数
            $countSql = "SELECT COUNT(*) as count FROM error_reports $whereStr";
            $totalResult = $this->db->query($countSql, $params);
            $total = $totalResult[0]['count'] ?? 0;
            
            // 获取报告列表
            $sql = "SELECT * FROM error_reports $whereStr ORDER BY created_at DESC LIMIT $offset, $size";
            $reports = $this->db->query($sql, $params);
            
            return [
                'list' => $reports,
                'total' => $total
            ];
        } catch (Exception $e) {
            return [
                'list' => [],
                'total' => 0
            ];
        }
    }
    
    /**
     * 获取错误报告统计
     */
    private function getErrorReportStats() {
        try {
            $totalReports = $this->db->count('error_reports');
            $unresolvedReports = $this->db->count('error_reports', ['is_resolved' => 0]);
            $todayReports = $this->db->count('error_reports', ['DATE(created_at)' => date('Y-m-d')]);
            
            // 按错误类型统计
            $typeStats = $this->db->query(
                "SELECT error_type, COUNT(*) as count FROM error_reports GROUP BY error_type ORDER BY count DESC LIMIT 10"
            );
            
            // 按时间统计(最近7天)
            $timeStats = $this->db->query(
                "SELECT DATE(created_at) as date, COUNT(*) as count 
                 FROM error_reports 
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
                 GROUP BY DATE(created_at) 
                 ORDER BY date DESC"
            );
            
            return [
                'total_reports' => $totalReports,
                'unresolved_reports' => $unresolvedReports,
                'today_reports' => $todayReports,
                'type_stats' => $typeStats,
                'time_stats' => $timeStats
            ];
        } catch (Exception $e) {
            return [
                'total_reports' => 0,
                'unresolved_reports' => 0,
                'today_reports' => 0,
                'type_stats' => [],
                'time_stats' => []
            ];
        }
    }
    
    /**
     * 获取系统监控数据
     */
    private function getSystemMonitor() {
        $monitor = [
            'system_info' => $this->getSystemInfo(),
            'performance' => $this->getPerformanceStats(),
            'database' => $this->getDatabaseStats(),
            'logs' => $this->getLogStats(),
            'errors' => $this->getErrorStats()
        ];
        
        return $monitor;
    }
    
    /**
     * 获取性能统计
     */
    private function getPerformanceStats() {
        return [
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => $this->parseSize(ini_get('memory_limit')),
                'usage_percent' => round(memory_get_usage(true) / $this->parseSize(ini_get('memory_limit')) * 100, 2)
            ],
            'cpu' => $this->getCpuUsage(),
            'disk_space' => $this->getDiskSpaceInfo(),
            'load_average' => function_exists('sys_getloadavg') ? sys_getloadavg() : null
        ];
    }
    
    /**
     * 获取数据库统计
     */
    private function getDatabaseStats() {
        try {
            // 获取数据库大小
            $sizeResult = $this->db->query(
                "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb 
                 FROM information_schema.tables 
                 WHERE table_schema = DATABASE()"
            );
            
            // 获取表统计
            $tableStats = $this->db->query(
                "SELECT table_name, table_rows, 
                        ROUND(data_length / 1024 / 1024, 2) AS data_size_mb,
                        ROUND(index_length / 1024 / 1024, 2) AS index_size_mb
                 FROM information_schema.tables 
                 WHERE table_schema = DATABASE() 
                 ORDER BY data_length DESC LIMIT 10"
            );
            
            return [
                'database_size_mb' => $sizeResult[0]['db_size_mb'] ?? 0,
                'table_stats' => $tableStats,
                'connection_status' => 'connected'
            ];
        } catch (Exception $e) {
            return [
                'database_size_mb' => 0,
                'table_stats' => [],
                'connection_status' => 'error',
                'error_message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取日志统计
     */
    private function getLogStats() {
        try {
            $logCounts = $this->db->query(
                "SELECT level, COUNT(*) as count FROM system_logs 
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) 
                 GROUP BY level"
            );
            
            $errorCount = $this->db->count('error_reports', [
                'created_at >=' => date('Y-m-d H:i:s', time() - 86400)
            ]);
            
            return [
                'log_counts' => $logCounts,
                'error_count_24h' => $errorCount,
                'log_file_sizes' => $this->getLogFileSizes()
            ];
        } catch (Exception $e) {
            return [
                'log_counts' => [],
                'error_count_24h' => 0,
                'log_file_sizes' => []
            ];
        }
    }
    
    /**
     * 获取错误统计
     */
    private function getErrorStats() {
        try {
            $criticalErrors = $this->db->count('system_logs', [
                'level' => 'error',
                'created_at >=' => date('Y-m-d H:i:s', time() - 3600) // 最近1小时
            ]);
            
            $recentErrors = $this->db->query(
                "SELECT message, created_at FROM system_logs 
                 WHERE level IN ('error', 'critical') 
                 ORDER BY created_at DESC LIMIT 5"
            );
            
            return [
                'critical_errors_1h' => $criticalErrors,
                'recent_errors' => $recentErrors
            ];
        } catch (Exception $e) {
            return [
                'critical_errors_1h' => 0,
                'recent_errors' => []
            ];
        }
    }
    
    /**
     * 获取CPU使用率
     */
    private function getCpuUsage() {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $cpuCount = 1;
            
            if (is_readable('/proc/cpuinfo')) {
                $cpuinfo = file_get_contents('/proc/cpuinfo');
                preg_match_all('/^processor/m', $cpuinfo, $matches);
                $cpuCount = count($matches[0]);
            }
            
            return [
                'load_1min' => $load[0] ?? 0,
                'load_5min' => $load[1] ?? 0,
                'load_15min' => $load[2] ?? 0,
                'cpu_count' => $cpuCount,
                'usage_percent' => round(($load[0] / $cpuCount) * 100, 2)
            ];
        }
        
        return null;
    }
    
    /**
     * 获取磁盘空间信息
     */
    private function getDiskSpaceInfo() {
        $path = dirname(__DIR__, 2);
        
        return [
            'total_space' => disk_total_space($path),
            'free_space' => disk_free_space($path),
            'used_space' => disk_total_space($path) - disk_free_space($path),
            'usage_percent' => round((1 - disk_free_space($path) / disk_total_space($path)) * 100, 2)
        ];
    }
    
    /**
     * 获取日志文件大小
     */
    private function getLogFileSizes() {
        $logDir = dirname(__DIR__, 2) . '/logs';
        $sizes = [];
        
        if (is_dir($logDir)) {
            $files = glob($logDir . '/*.log');
            foreach ($files as $file) {
                $sizes[basename($file)] = filesize($file);
            }
        }
        
        return $sizes;
    }
    
    /**
     * 记录管理员操作
     */
    private function logAdminAction($action, $details = []) {
        try {
            $admin = $this->getCurrentAdmin();
            
            require_once dirname(__DIR__, 2) . '/core/Logger.php';
            $logger = Logger::getInstance();
            
            $logger->info('Admin Action: ' . $action, array_merge([
                'admin_id' => $admin['id'] ?? null,
                'admin_username' => $admin['username'] ?? null,
                'action' => $action,
                'ip' => $this->getClientIp(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ], $details));
            
        } catch (Exception $e) {
            error_log('Failed to log admin action: ' . $e->getMessage());
        }
    }
}