<?php
/**
 * SOPAN QR码控制器
 * 简化版QR码生成，直接输出图片，不存储数据库
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

require_once __DIR__ . '/BaseController.php';

class QRCodeController extends BaseController {
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * 生成网盘链接QR码
     * POST /api/qrcode/netdisk
     */
    public function generateNetdiskQRCode() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->rateLimitCheck($this->request['ip'], 15, 60)) return;
        
        // 验证参数
        if (!$this->validateRequired(['url'])) return;
        
        $url = $this->getParam('url');
        $code = $this->getParam('code', '');
        $title = $this->getParam('title', '');
        
        try {
            // 验证URL格式
            if (!$this->isValidNetdiskUrl($url)) {
                $this->error('无效的网盘链接格式', 400);
                return;
            }
            
            // 构建分享文本
            $shareText = $this->buildShareText($title, $url, $code);
            
            // 生成QR码URL
            $qrUrl = $this->buildQRCodeUrl($shareText);
            
            $this->success([
                'qr_url' => $qrUrl,
                'share_text' => $shareText,
                'title' => $title,
                'url' => $url,
                'code' => $code
            ], '二维码生成成功');
            
        } catch (Exception $e) {
            $this->error('二维码生成失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 生成普通链接QR码
     * POST /api/qrcode/generate
     */
    public function generateQRCode() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->rateLimitCheck($this->request['ip'], 20, 60)) return;
        
        // 验证参数
        if (!$this->validateRequired(['data'])) return;
        
        $data = $this->getParam('data');
        $size = $this->getParam('size', 6);
        
        try {
            // 生成QR码URL
            $qrUrl = $this->buildQRCodeUrl($data, $size);
            
            $this->success([
                'qr_url' => $qrUrl,
                'data' => $data
            ], '二维码生成成功');
            
        } catch (Exception $e) {
            $this->error('二维码生成失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 直接输出QR码图片
     * GET /api/qrcode/image
     */
    public function generateQRCodeImage() {
        if (!$this->validateMethod(['GET'])) return;
        
        $data = $this->getParam('data', '');
        $size = (int)$this->getParam('size', 6);
        $level = $this->getParam('level', 'M');
        $margin = (int)$this->getParam('margin', 2);
        
        if (empty($data)) {
            http_response_code(400);
            echo 'Missing data parameter';
            return;
        }
        
        try {
            // 引入phpqrcode库
            require_once dirname(__DIR__, 2) . '/extend/phpqrcode/qrlib.php';
            
            // 验证参数
            $size = max(1, min(10, $size));
            $level = in_array($level, ['L', 'M', 'Q', 'H']) ? $level : 'M';
            $margin = max(0, min(10, $margin));
            
            // 设置内容类型为PNG图片
            header('Content-Type: image/png');
            header('Cache-Control: public, max-age=3600'); // 缓存1小时
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
            
            // 生成并输出二维码
            QRcode::png($data, false, $level, $size, $margin);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo 'QR Code generation failed: ' . $e->getMessage();
        }
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 验证网盘URL格式
     */
    private function isValidNetdiskUrl($url) {
        $patterns = [
            'quark' => '/quark\.cn\/s\//i',
            'baidu' => '/(pan\.baidu\.com|yun\.baidu\.com)\/s\//i',
            'aliyun' => '/aliyundrive\.com\/s\//i',
            '115' => '/115\.com\//i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 构建分享文本
     */
    private function buildShareText($title, $url, $code) {
        $shareText = '';
        
        if (!empty($title)) {
            $shareText .= "【{$title}】\n";
        }
        
        $shareText .= "网盘链接: {$url}";
        
        if (!empty($code)) {
            $shareText .= "\n提取码: {$code}";
        }
        
        return $shareText;
    }
    
    /**
     * 构建QR码URL
     */
    private function buildQRCodeUrl($data, $size = 6) {
        $baseUrl = $this->getBaseUrl();
        
        // 使用我们自己的QR码生成API
        return $baseUrl . '/qrcode.php?' . http_build_query([
            'data' => $data,
            'size' => $size,
            'level' => 'M',
            'margin' => 2
        ]);
    }
    
    /**
     * 获取基础URL
     */
    private function getBaseUrl() {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $script = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = dirname($script);
        
        if ($basePath === '/' || $basePath === '\\') {
            $basePath = '';
        }
        
        return $protocol . '://' . $host . $basePath;
    }
}