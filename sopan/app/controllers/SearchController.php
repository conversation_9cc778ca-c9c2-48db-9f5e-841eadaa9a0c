<?php
/**
 * SOPAN 搜索API控制器
 * 提供异步搜索接口和结果管理功能
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

require_once __DIR__ . '/BaseController.php';

class SearchController extends BaseController {
    private $panseekerService;
    
    public function __construct() {
        parent::__construct();
        $this->initializeServices();
    }
    
    /**
     * 初始化服务
     */
    private function initializeServices() {
        require_once dirname(__DIR__) . '/services/PanseekerService.php';
        $this->panseekerService = new PanseekerService();
    }
    
    /**
     * 开始异步搜索
     * POST /api/search/start
     */
    public function startSearch() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->rateLimitCheck($this->request['ip'], 10, 60)) return;
        
        // 验证参数
        if (!$this->validateRequired(['keyword'])) return;
        
        $keyword = $this->getParam('keyword');
        $typeFilter = $this->getParam('type', 'all');
        $userIp = $this->request['ip'];
        
        try {
            // 使用PanseekerService开始搜索
            $result = $this->panseekerService->startSearch($keyword, $typeFilter, $userIp);
            
            if ($result['success']) {
                $this->success([
                    'session_id' => $result['session_id'],
                    'keyword' => $keyword,
                    'type_filter' => $typeFilter,
                    'sse_url' => '/api/search/stream/' . $result['session_id'],
                    'status_url' => '/api/search/status/' . $result['session_id'],
                    'results_url' => '/api/search/results/' . $result['session_id']
                ], $result['message']);
                
                // 异步执行搜索（实际项目中可以使用消息队列或后台任务）
                $this->executeSearchAsync($result['session_id']);
            } else {
                $this->error($result['message'], 400);
            }
            
        } catch (Exception $e) {
            $this->error('搜索启动失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * SSE流式搜索接口
     * GET /api/search/stream/{sessionId}
     */
    public function streamSearch($sessionId) {
        if (!$this->validateMethod(['GET'])) return;
        
        // 验证会话ID
        $statusResult = $this->panseekerService->getSearchStatus($sessionId);
        if (!$statusResult['success']) {
            $this->error($statusResult['message'], 404);
            return;
        }
        
        // 设置SSE头
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no'); // Nginx不缓冲
        
        // 输出连接建立事件
        $this->sendSSEEvent('connected', $statusResult['data']);
        
        // 循环检查搜索状态和结果
        $this->streamSearchProgress($sessionId);
    }
    
    /**
     * 异步执行搜索（简化版，实际项目中应使用消息队列）
     */
    private function executeSearchAsync($sessionId) {
        // 这里使用简单的方式直接执行
        // 实际项目中应该使用Redis队列或其他异步机制
        ignore_user_abort(true);
        set_time_limit(0);
        
        // 延迟一小段时间再开始搜索，给客户端时间连接SSE
        usleep(500000); // 0.5秒
        
        $this->panseekerService->executeSearch($sessionId);
    }
    
    /**
     * 流式推送搜索进度
     */
    private function streamSearchProgress($sessionId) {
        $maxWaitTime = 300; // 最大等待时间300秒
        $startTime = time();
        $lastResultCount = 0;
        
        while (time() - $startTime < $maxWaitTime) {
            // 检查客户端是否断开连接
            if (connection_aborted()) {
                break;
            }
            
            // 获取搜索状态
            $statusResult = $this->panseekerService->getSearchStatus($sessionId);
            if ($statusResult['success']) {
                $status = $statusResult['data'];
                
                // 发送状态更新
                $this->sendSSEEvent('status', $status);
                
                // 检查是否有新结果
                if ($status['total_results'] > $lastResultCount) {
                    $newResults = $this->getNewResults($sessionId, $lastResultCount);
                    if ($newResults) {
                        $this->sendSSEEvent('results', $newResults);
                        $lastResultCount = $status['total_results'];
                    }
                }
                
                // 检查搜索是否完成
                if (in_array($status['status'], ['completed', 'error', 'cancelled'])) {
                    $this->sendSSEEvent('finished', [
                        'status' => $status['status'],
                        'total_results' => $status['total_results'],
                        'message' => $this->getStatusMessage($status['status'])
                    ]);
                    break;
                }
            }
            
            // 发送心跳包
            $this->sendSSEEvent('heartbeat', ['timestamp' => time()]);
            
            // 等待一秒
            sleep(1);
        }
        
        // 发送结束事件
        $this->sendSSEEvent('close', ['reason' => 'timeout_or_completed']);
    }
    
    /**
     * 获取搜索状态
     * GET /api/search/status/{sessionId}
     */
    public function getSearchStatus($sessionId) {
        if (!$this->validateMethod(['GET'])) return;
        
        try {
            $result = $this->panseekerService->getSearchStatus($sessionId);
            
            if ($result['success']) {
                $this->success($result['data'], '获取搜索状态成功');
            } else {
                $this->error($result['message'], 404);
            }
        } catch (Exception $e) {
            $this->error('获取搜索状态失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取搜索结果
     * GET /api/search/results/{sessionId}
     */
    public function getSearchResults($sessionId) {
        if (!$this->validateMethod(['GET'])) return;
        
        $page = (int)$this->getParam('page', 1);
        $limit = (int)$this->getParam('limit', 20);
        
        // 验证分页参数
        if ($page < 1) $page = 1;
        if ($limit < 1 || $limit > 100) $limit = 20;
        
        try {
            $result = $this->panseekerService->getSearchResults($sessionId, $page, $limit);
            
            if ($result['success']) {
                $this->success($result['data'], '获取搜索结果成功');
            } else {
                $this->error($result['message'], 404);
            }
        } catch (Exception $e) {
            $this->error('获取搜索结果失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 取消搜索
     * POST /api/search/cancel/{sessionId}
     */
    public function cancelSearch($sessionId) {
        if (!$this->validateMethod(['POST'])) return;
        
        try {
            $result = $this->panseekerService->cancelSearch($sessionId);
            
            if ($result['success']) {
                $this->success(['session_id' => $sessionId], $result['message']);
            } else {
                $this->error($result['message'], 400);
            }
        } catch (Exception $e) {
            $this->error('取消搜索失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取热门搜索关键词
     * GET /api/search/hot-keywords
     */
    public function getHotKeywords() {
        if (!$this->validateMethod(['GET'])) return;
        
        try {
            $limit = (int)$this->getParam('limit', 10);
            if ($limit < 1 || $limit > 50) $limit = 10;
            
            // 获取最近一周的热门搜索关键词
            $hotKeywords = $this->db->query(
                "SELECT keyword, COUNT(*) as search_count 
                 FROM search_logs 
                 WHERE search_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                 GROUP BY keyword 
                 ORDER BY search_count DESC 
                 LIMIT ?",
                [$limit]
            );
            
            $this->success($hotKeywords, '获取热门搜索关键词成功');
            
        } catch (Exception $e) {
            $this->error('获取热门搜索关键词失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取搜索建议
     * GET /api/search/suggestions
     */
    public function getSearchSuggestions() {
        if (!$this->validateMethod(['GET'])) return;
        
        $query = $this->getParam('q', '');
        $limit = (int)$this->getParam('limit', 8);
        
        if (strlen($query) < 2) {
            $this->success([], '查询太短');
            return;
        }
        
        try {
            // 搜索相似的历史搜索关键词
            $suggestions = $this->db->query(
                "SELECT keyword, COUNT(*) as frequency 
                 FROM search_logs 
                 WHERE keyword LIKE ? AND keyword != ?
                 GROUP BY keyword 
                 ORDER BY frequency DESC, LENGTH(keyword) ASC
                 LIMIT ?",
                ["%{$query}%", $query, $limit]
            );
            
            // 格式化结果
            $formattedSuggestions = [];
            foreach ($suggestions as $suggestion) {
                $formattedSuggestions[] = [
                    'keyword' => $suggestion['keyword'],
                    'frequency' => (int)$suggestion['frequency']
                ];
            }
            
            $this->success($formattedSuggestions, '获取搜索建议成功');
            
        } catch (Exception $e) {
            $this->error('获取搜索建议失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 开始转存资源
     * POST /api/transfer/start
     */
    public function startTransfer() {
        if (!$this->validateMethod(['POST'])) return;
        if (!$this->rateLimitCheck($this->request['ip'], 5, 60)) return; // 转存限制更严格
        
        // 验证参数
        if (!$this->validateRequired(['url', 'platform', 'title'])) return;
        
        $url = $this->getParam('url');
        $code = $this->getParam('code', '');
        $platform = $this->getParam('platform');
        $title = $this->getParam('title');
        $userIp = $this->request['ip'];
        
        try {
            // 验证URL格式
            if (!$this->isValidNetdiskUrl($url)) {
                $this->error('无效的网盘链接', 400);
                return;
            }
            
            // 检查是否支持该平台
            if (!in_array($platform, ['quark', 'baidu', 'aliyun'])) {
                $this->error('暂不支持该网盘平台', 400);
                return;
            }
            
            // 记录转存请求
            $transferId = $this->recordTransferRequest($url, $code, $platform, $title, $userIp);
            
            // 调用转存服务
            $result = $this->processTransfer($transferId, $url, $code, $platform);
            
            if ($result['success']) {
                $this->updateTransferStatus($transferId, 'success', $result['message']);
                $this->success([
                    'transfer_id' => $transferId,
                    'status' => 'success',
                    'message' => $result['message'],
                    'result' => $result['data'] ?? null
                ], '转存成功');
            } else {
                $this->updateTransferStatus($transferId, 'failed', $result['message']);
                $this->error($result['message'], 400);
            }
            
        } catch (Exception $e) {
            if (isset($transferId)) {
                $this->updateTransferStatus($transferId, 'error', $e->getMessage());
            }
            $this->error('转存失败：' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取转存记录
     * GET /api/transfer/records
     */
    public function getTransferRecords() {
        if (!$this->validateMethod(['GET'])) return;
        
        $page = max(1, (int)$this->getParam('page', 1));
        $limit = min(50, max(1, (int)$this->getParam('limit', 10)));
        $userIp = $this->request['ip'];
        
        try {
            $conditions = ['user_ip' => $userIp];
            
            $total = $this->db->count('transfer_records', $conditions);
            $records = $this->db->findAll('transfer_records', $conditions, [
                'order' => 'create_time DESC',
                'limit' => $limit,
                'offset' => ($page - 1) * $limit
            ]);
            
            $this->success([
                'records' => $records,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (Exception $e) {
            $this->error('获取转存记录失败：' . $e->getMessage(), 500);
        }
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 发送SSE事件
     */
    private function sendSSEEvent($event, $data) {
        echo "event: {$event}\n";
        echo "data: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n\n";
        
        if (ob_get_level()) {
            ob_flush();
        }
        flush();
    }
    
    /**
     * 获取新结果
     */
    private function getNewResults($sessionId, $offset) {
        try {
            $results = $this->db->findAll('search_results_cache', [
                'session_id' => $sessionId
            ], [
                'order' => 'create_time ASC',
                'limit' => 10,
                'offset' => $offset
            ]);
            
            $formattedResults = [];
            foreach ($results as $result) {
                $resultData = json_decode($result['result_data'], true);
                $formattedResults[] = [
                    'id' => $result['id'],
                    'platform_type' => $result['platform_type'],
                    'match_score' => (float)$result['match_score'],
                    'title' => $resultData['title'] ?? '',
                    'url' => $resultData['url'] ?? '',
                    'code' => $resultData['code'] ?? '',
                    'size' => $resultData['size'] ?? ''
                ];
            }
            
            return $formattedResults;
            
        } catch (Exception $e) {
            error_log('GetNewResults error: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 获取状态消息
     */
    private function getStatusMessage($status) {
        $messages = [
            'searching' => '搜索中...',
            'completed' => '搜索完成',
            'error' => '搜索出错',
            'cancelled' => '搜索已取消'
        ];
        
        return $messages[$status] ?? '未知状态';
    }
    
    /**
     * 验证网盘链接格式
     */
    private function isValidNetdiskUrl($url) {
        $patterns = [
            'quark' => '/quark\.cn\/s\//i',
            'baidu' => '/(pan\.baidu\.com|yun\.baidu\.com)\/s\//i',
            'aliyun' => '/aliyundrive\.com\/s\//i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 记录转存请求
     */
    private function recordTransferRequest($url, $code, $platform, $title, $userIp) {
        $transferId = 'tf_' . uniqid() . '_' . time();
        
        $this->db->insert('transfer_records', [
            'transfer_id' => $transferId,
            'url' => $url,
            'code' => $code,
            'platform' => $platform,
            'title' => $title,
            'user_ip' => $userIp,
            'status' => 'processing',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ]);
        
        return $transferId;
    }
    
    /**
     * 更新转存状态
     */
    private function updateTransferStatus($transferId, $status, $message = '') {
        $this->db->update('transfer_records', [
            'status' => $status,
            'message' => $message,
            'update_time' => date('Y-m-d H:i:s')
        ], ['transfer_id' => $transferId]);
    }
    
    /**
     * 处理转存逻辑
     */
    private function processTransfer($transferId, $url, $code, $platform) {
        try {
            // 加载转存服务
            require_once dirname(__DIR__, 2) . '/extend/netdisk/Transfer.php';
            
            $transfer = new \netdisk\Transfer();
            
            // 构建转存数据
            $urlData = [
                'url' => $url,
                'pwd' => $code,
                'platform' => $platform
            ];
            
            // 执行转存
            $result = $transfer->transfer($urlData);
            
            if ($result && isset($result['code']) && $result['code'] == 200) {
                return [
                    'success' => true,
                    'message' => $result['msg'] ?? '转存成功',
                    'data' => $result['data'] ?? null
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result['msg'] ?? '转存失败'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '转存服务异常：' . $e->getMessage()
            ];
        }
    }
}