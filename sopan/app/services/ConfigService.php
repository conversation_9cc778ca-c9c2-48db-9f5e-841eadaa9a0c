<?php
/**
 * SOPAN 配置管理服务
 * 管理系统配置和网盘平台配置
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class ConfigService {
    private $db;
    private $cache = [];
    private $cacheTimeout = 300; // 5分钟缓存
    
    // 配置分组
    const GROUP_BASIC = 'basic';
    const GROUP_NETDISK = 'netdisk';
    const GROUP_API = 'api';
    const GROUP_SECURITY = 'security';
    const GROUP_SYSTEM = 'system';
    
    // 配置类型
    const TYPE_TEXT = 'text';
    const TYPE_TEXTAREA = 'textarea';
    const TYPE_PASSWORD = 'password';
    const TYPE_NUMBER = 'number';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_SELECT = 'select';
    const TYPE_JSON = 'json';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 获取配置值
     * 
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed
     */
    public function get($key, $default = null) {
        $cacheKey = 'config_' . $key;
        
        // 检查缓存
        if (isset($this->cache[$cacheKey]) && 
            time() - $this->cache[$cacheKey]['time'] < $this->cacheTimeout) {
            return $this->cache[$cacheKey]['value'];
        }
        
        // 从数据库获取
        $config = $this->db->find('site_config', [
            'config_key' => $key,
            'status' => 1
        ]);
        
        if ($config) {
            $value = $this->parseConfigValue($config['config_value'], $config['config_type']);
            
            // 缓存配置
            $this->cache[$cacheKey] = [
                'value' => $value,
                'time' => time()
            ];
            
            return $value;
        }
        
        return $default;
    }
    
    /**
     * 设置配置值
     * 
     * @param string $key 配置键
     * @param mixed $value 配置值
     * @param string $desc 配置描述
     * @param string $type 配置类型
     * @param string $group 配置分组
     * @return bool
     */
    public function set($key, $value, $desc = '', $type = self::TYPE_TEXT, $group = self::GROUP_BASIC) {
        try {
            $configValue = $this->formatConfigValue($value, $type);
            
            $existing = $this->db->find('site_config', ['config_key' => $key]);
            
            if ($existing) {
                // 更新现有配置
                $result = $this->db->update('site_config', [
                    'config_value' => $configValue,
                    'config_desc' => $desc ?: $existing['config_desc'],
                    'config_type' => $type,
                    'config_group' => $group,
                    'updated_time' => date('Y-m-d H:i:s')
                ], ['config_key' => $key]);
            } else {
                // 插入新配置
                $result = $this->db->insert('site_config', [
                    'config_key' => $key,
                    'config_value' => $configValue,
                    'config_desc' => $desc,
                    'config_type' => $type,
                    'config_group' => $group,
                    'sort_order' => $this->getNextSortOrder($group),
                    'status' => 1
                ]);
            }
            
            if ($result) {
                // 清除缓存
                $this->clearCache($key);
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('ConfigService::set error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量设置配置
     * 
     * @param array $configs 配置数组
     * @return bool
     */
    public function setMultiple($configs) {
        try {
            $this->db->beginTransaction();
            
            foreach ($configs as $key => $data) {
                if (is_array($data)) {
                    $value = $data['value'] ?? '';
                    $desc = $data['desc'] ?? '';
                    $type = $data['type'] ?? self::TYPE_TEXT;
                    $group = $data['group'] ?? self::GROUP_BASIC;
                } else {
                    $value = $data;
                    $desc = '';
                    $type = self::TYPE_TEXT;
                    $group = self::GROUP_BASIC;
                }
                
                if (!$this->set($key, $value, $desc, $type, $group)) {
                    $this->db->rollback();
                    return false;
                }
            }
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log('ConfigService::setMultiple error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取分组配置
     * 
     * @param string $group 配置分组
     * @return array
     */
    public function getGroup($group) {
        $configs = $this->db->findAll('site_config', [
            'config_group' => $group,
            'status' => 1
        ], ['order' => 'sort_order ASC']);
        
        $result = [];
        foreach ($configs as $config) {
            $result[$config['config_key']] = [
                'value' => $this->parseConfigValue($config['config_value'], $config['config_type']),
                'desc' => $config['config_desc'],
                'type' => $config['config_type'],
                'is_system' => (bool)$config['is_system']
            ];
        }
        
        return $result;
    }
    
    /**
     * 获取所有配置分组
     * 
     * @return array
     */
    public function getAllGroups() {
        $groups = [
            self::GROUP_BASIC => '基础设置',
            self::GROUP_NETDISK => '网盘配置',
            self::GROUP_API => 'API设置',
            self::GROUP_SECURITY => '安全设置',
            self::GROUP_SYSTEM => '系统设置'
        ];
        
        $result = [];
        foreach ($groups as $key => $name) {
            $result[$key] = [
                'name' => $name,
                'configs' => $this->getGroup($key)
            ];
        }
        
        return $result;
    }
    
    /**
     * 删除配置
     * 
     * @param string $key 配置键
     * @return bool
     */
    public function delete($key) {
        try {
            $config = $this->db->find('site_config', ['config_key' => $key]);
            
            if (!$config) {
                return false;
            }
            
            // 系统配置不允许删除
            if ($config['is_system']) {
                return false;
            }
            
            $result = $this->db->delete('site_config', ['config_key' => $key]);
            
            if ($result) {
                $this->clearCache($key);
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('ConfigService::delete error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取网盘平台配置
     * 
     * @param string $platform 平台名称
     * @return array
     */
    public function getPlatformConfig($platform) {
        $configs = $this->getGroup(self::GROUP_NETDISK);
        $result = [];
        
        foreach ($configs as $key => $config) {
            if (strpos($key, $platform . '_') === 0) {
                $configKey = substr($key, strlen($platform . '_'));
                $result[$configKey] = $config['value'];
            }
        }
        
        return $result;
    }
    
    /**
     * 设置网盘平台配置
     * 
     * @param string $platform 平台名称
     * @param array $configs 配置数据
     * @return bool
     */
    public function setPlatformConfig($platform, $configs) {
        try {
            $configData = [];
            
            foreach ($configs as $key => $value) {
                $fullKey = $platform . '_' . $key;
                $desc = $this->getPlatformConfigDesc($platform, $key);
                $type = $this->getPlatformConfigType($key);
                
                $configData[$fullKey] = [
                    'value' => $value,
                    'desc' => $desc,
                    'type' => $type,
                    'group' => self::GROUP_NETDISK
                ];
            }
            
            return $this->setMultiple($configData);
            
        } catch (Exception $e) {
            error_log('ConfigService::setPlatformConfig error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 测试网盘平台配置
     * 
     * @param string $platform 平台名称
     * @return array
     */
    public function testPlatformConfig($platform) {
        try {
            $platformClass = ucfirst($platform) . 'Platform';
            $platformFile = dirname(__DIR__) . "/app/platforms/{$platformClass}.php";
            
            if (!file_exists($platformFile)) {
                return [
                    'success' => false,
                    'message' => "平台类文件不存在: {$platformClass}"
                ];
            }
            
            require_once $platformFile;
            
            if (!class_exists($platformClass)) {
                return [
                    'success' => false,
                    'message' => "平台类不存在: {$platformClass}"
                ];
            }
            
            $platformInstance = new $platformClass();
            
            if (!method_exists($platformInstance, 'checkConfiguration')) {
                return [
                    'success' => false,
                    'message' => '平台不支持配置检查'
                ];
            }
            
            return $platformInstance->checkConfiguration();
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '测试配置时发生错误: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 初始化默认配置
     * 
     * @return bool
     */
    public function initDefaultConfigs() {
        $defaultConfigs = [
            // 基础设置
            'site_name' => [
                'value' => 'SOPAN搜索',
                'desc' => '网站名称',
                'type' => self::TYPE_TEXT,
                'group' => self::GROUP_BASIC
            ],
            'site_description' => [
                'value' => '专业的网盘资源搜索与转存服务',
                'desc' => '网站描述',
                'type' => self::TYPE_TEXTAREA,
                'group' => self::GROUP_BASIC
            ],
            'site_keywords' => [
                'value' => '网盘搜索,资源搜索,网盘转存',
                'desc' => '网站关键词',
                'type' => self::TYPE_TEXT,
                'group' => self::GROUP_BASIC
            ],
            
            // API设置
            'panseeker_api_url' => [
                'value' => 'https://api.panseeker.com/api/search',
                'desc' => 'Panseeker API地址',
                'type' => self::TYPE_TEXT,
                'group' => self::GROUP_API
            ],
            'panseeker_api_key' => [
                'value' => '',
                'desc' => 'Panseeker API密钥',
                'type' => self::TYPE_PASSWORD,
                'group' => self::GROUP_API
            ],
            
            // 安全设置
            'max_search_per_ip' => [
                'value' => 100,
                'desc' => '每IP每日最大搜索次数',
                'type' => self::TYPE_NUMBER,
                'group' => self::GROUP_SECURITY
            ],
            'enable_cache' => [
                'value' => true,
                'desc' => '启用搜索结果缓存',
                'type' => self::TYPE_BOOLEAN,
                'group' => self::GROUP_SYSTEM
            ],
            'cache_expire' => [
                'value' => 3600,
                'desc' => '缓存过期时间（秒）',
                'type' => self::TYPE_NUMBER,
                'group' => self::GROUP_SYSTEM
            ]
        ];
        
        return $this->setMultiple($defaultConfigs);
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 解析配置值
     */
    private function parseConfigValue($value, $type) {
        switch ($type) {
            case self::TYPE_BOOLEAN:
                return in_array(strtolower($value), ['1', 'true', 'yes', 'on']);
            case self::TYPE_NUMBER:
                return is_numeric($value) ? (int)$value : 0;
            case self::TYPE_JSON:
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }
    
    /**
     * 格式化配置值
     */
    private function formatConfigValue($value, $type) {
        switch ($type) {
            case self::TYPE_BOOLEAN:
                return $value ? '1' : '0';
            case self::TYPE_JSON:
                return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
            default:
                return (string)$value;
        }
    }
    
    /**
     * 获取下一个排序序号
     */
    private function getNextSortOrder($group) {
        $result = $this->db->query(
            "SELECT MAX(sort_order) as max_order FROM site_config WHERE config_group = ?",
            [$group]
        );
        
        return ($result[0]['max_order'] ?? 0) + 10;
    }
    
    /**
     * 清除配置缓存
     */
    private function clearCache($key = null) {
        if ($key) {
            unset($this->cache['config_' . $key]);
        } else {
            $this->cache = [];
        }
    }
    
    /**
     * 获取平台配置描述
     */
    private function getPlatformConfigDesc($platform, $key) {
        $descriptions = [
            'quark' => [
                'status' => '夸克网盘启用状态',
                'cookie' => '夸克网盘Cookie',
                'file_path' => '默认转存目录',
                'file_path_temp' => '临时转存目录',
                'banned_keywords' => '禁用关键词（逗号分隔）'
            ],
            'baidu' => [
                'status' => '百度网盘启用状态',
                'cookie' => '百度网盘Cookie',
                'file_path' => '默认转存目录',
                'file_path_temp' => '临时转存目录',
                'banned_keywords' => '禁用关键词（逗号分隔）'
            ],
            'aliyun' => [
                'status' => '阿里云盘启用状态',
                'refresh_token' => '阿里云盘Refresh Token',
                'file_path' => '默认转存目录'
            ]
        ];
        
        return $descriptions[$platform][$key] ?? "{$platform}_{$key}配置";
    }
    
    /**
     * 获取平台配置类型
     */
    private function getPlatformConfigType($key) {
        $types = [
            'status' => self::TYPE_BOOLEAN,
            'cookie' => self::TYPE_TEXTAREA,
            'refresh_token' => self::TYPE_TEXTAREA,
            'file_path' => self::TYPE_TEXT,
            'file_path_temp' => self::TYPE_TEXT,
            'banned_keywords' => self::TYPE_TEXT
        ];
        
        return $types[$key] ?? self::TYPE_TEXT;
    }
}