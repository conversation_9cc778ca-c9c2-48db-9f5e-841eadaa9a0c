<?php
/**
 * SOPAN 网盘转存服务封装层
 * 集成SOPAN配置系统和错误处理机制
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class TransferService {
    private $config;
    private $db;
    
    // 网盘平台类型映射
    const PLATFORM_QUARK = 0;
    const PLATFORM_ALIYUN = 1;
    const PLATFORM_BAIDU = 2;
    const PLATFORM_UC = 3;
    const PLATFORM_XUNLEI = 4;
    
    // 网盘平台名称映射
    private $platformNames = [
        self::PLATFORM_QUARK => '夸克网盘',
        self::PLATFORM_ALIYUN => '阿里云盘',
        self::PLATFORM_BAIDU => '百度网盘',
        self::PLATFORM_UC => 'UC网盘',
        self::PLATFORM_XUNLEI => '迅雷网盘'
    ];
    
    // URL域名到平台类型的映射
    private $urlPlatformMap = [
        'pan.quark.cn' => self::PLATFORM_QUARK,
        'www.alipan.com' => self::PLATFORM_ALIYUN,
        'alipan.com' => self::PLATFORM_ALIYUN,
        'www.aliyundrive.com' => self::PLATFORM_ALIYUN,
        'aliyundrive.com' => self::PLATFORM_ALIYUN,
        'pan.baidu.com' => self::PLATFORM_BAIDU,
        'drive.uc.cn' => self::PLATFORM_UC,
        'fast.uc.cn' => self::PLATFORM_UC,
        'pan.xunlei.com' => self::PLATFORM_XUNLEI
    ];
    
    public function __construct() {
        $this->config = Config::getInstance();
        $this->db = Database::getInstance();
        
        // 确保网盘转存类库已加载
        $this->loadNetdiskLibrary();
        
        // 加载平台工厂类
        $this->loadPlatformFactory();
    }
    
    /**
     * 加载网盘转存类库
     */
    private function loadNetdiskLibrary() {
        $netdiskPath = dirname(__DIR__) . '/../extend/netdisk/';
        
        // 加载基础类
        require_once $netdiskPath . 'Transfer.php';
        require_once $netdiskPath . 'pan/BasePan.php';
        
        // 加载各个网盘平台类
        $panFiles = ['QuarkPan.php', 'AlipanPan.php', 'BaiduPan.php', 'UcPan.php'];
        foreach ($panFiles as $file) {
            $filePath = $netdiskPath . 'pan/' . $file;
            if (file_exists($filePath)) {
                require_once $filePath;
            }
        }
    }
    
    /**
     * 加载平台工厂类
     */
    private function loadPlatformFactory() {
        $factoryPath = dirname(__DIR__) . '/platforms/NetdiskPlatformFactory.php';
        if (file_exists($factoryPath)) {
            require_once $factoryPath;
        }
    }
    
    /**
     * 智能转存链接
     * 
     * @param string $url 原始链接
     * @param string $code 提取码
     * @param string $title 自定义标题
     * @param int $expiredType 过期类型 1永久 2临时
     * @param string $targetDir 目标目录
     * @return array
     */
    public function smartTransfer($url, $code = '', $title = '', $expiredType = 1, $targetDir = '') {
        try {
            // 记录转存开始日志
            $this->logTransferStart($url, $code);
            
            // 检测平台类型
            $platformType = $this->detectPlatform($url);
            if ($platformType === -1) {
                throw new Exception('不支持的网盘平台');
            }
            
            // 检查平台是否启用
            $platformKey = $this->getPlatformKey($platformType);
            $isEnabled = Config::get($platformKey . '_status', '0');
            if ($isEnabled !== '1') {
                throw new Exception($this->platformNames[$platformType] . ' 未启用');
            }
            
            // 验证链接格式
            $shareId = $this->extractShareId($url);
            if (!$shareId) {
                throw new Exception('链接格式错误');
            }
            
            // 构建转存配置
            $transferConfig = [
                'url' => $url,
                'code' => $code,
                'expired_type' => $expiredType,
                'isType' => 0, // 转存模式
                'ad_fid' => '',
                'stoken' => ''
            ];
            
            // 执行转存
            $transfer = new \netdisk\Transfer();
            $result = $transfer->transfer($transferConfig);
            
            // 处理转存结果
            if (is_array($result) && isset($result['code'])) {
                if ($result['code'] === 200) {
                    // 转存成功，记录日志
                    $this->logTransferSuccess($url, $result['data']);
                    
                    // 格式化返回结果
                    return [
                        'success' => true,
                        'data' => [
                            'share_url' => $result['data']['share_url'] ?? '',
                            'code' => $result['data']['code'] ?? '',
                            'title' => $title ?: ($result['data']['title'] ?? ''),
                            'platform' => $this->platformNames[$platformType],
                            'platform_key' => $platformKey,
                            'expired_type' => $expiredType,
                            'transfer_time' => date('Y-m-d H:i:s')
                        ],
                        'message' => '转存成功'
                    ];
                } else {
                    // 转存失败
                    $errorMsg = $result['msg'] ?? '转存失败';
                    $this->logTransferError($url, $errorMsg);
                    
                    return [
                        'success' => false,
                        'message' => $errorMsg,
                        'error_code' => $result['code']
                    ];
                }
            } else {
                throw new Exception('转存接口返回格式错误');
            }
            
        } catch (Exception $e) {
            $this->logTransferError($url, $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 500
            ];
        }
    }
    
    /**
     * 获取网盘文件列表（目录选择功能）
     * 
     * @param int $platformType 平台类型
     * @param string $parentId 父目录ID
     * @return array
     */
    public function getFiles($platformType, $parentId = '0') {
        try {
            // 检查平台是否启用
            $platformKey = $this->getPlatformKey($platformType);
            $isEnabled = Config::get($platformKey . '_status', '0');
            if ($isEnabled !== '1') {
                throw new Exception($this->platformNames[$platformType] . ' 未启用');
            }
            
            // 检查Cookie配置
            $cookie = Config::get($platformKey . '_cookie', '');
            if (empty($cookie)) {
                throw new Exception($this->platformNames[$platformType] . ' Cookie未配置');
            }
            
            $transfer = new \netdisk\Transfer();
            $result = $transfer->getFiles($platformType, $parentId);
            
            if (is_array($result) && isset($result['code']) && $result['code'] === 200) {
                return [
                    'success' => true,
                    'data' => $result['data'],
                    'message' => '获取成功'
                ];
            } else {
                $errorMsg = $result['msg'] ?? '获取文件列表失败';
                return [
                    'success' => false,
                    'message' => $errorMsg
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 批量转存
     * 
     * @param array $urls 链接数组
     * @param int $expiredType 过期类型
     * @return array
     */
    public function batchTransfer($urls, $expiredType = 1) {
        $results = [];
        $successCount = 0;
        $failCount = 0;
        
        foreach ($urls as $urlData) {
            $url = $urlData['url'] ?? '';
            $code = $urlData['code'] ?? '';
            $title = $urlData['title'] ?? '';
            
            if (empty($url)) {
                $results[] = [
                    'url' => $url,
                    'success' => false,
                    'message' => '链接不能为空'
                ];
                $failCount++;
                continue;
            }
            
            $result = $this->smartTransfer($url, $code, $title, $expiredType);
            $result['url'] = $url;
            $results[] = $result;
            
            if ($result['success']) {
                $successCount++;
            } else {
                $failCount++;
            }
            
            // 避免请求过快，短暂延迟
            usleep(500000); // 0.5秒
        }
        
        return [
            'success' => $successCount > 0,
            'total' => count($urls),
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'results' => $results,
            'message' => "批量转存完成：成功 {$successCount} 个，失败 {$failCount} 个"
        ];
    }
    
    /**
     * 验证链接有效性
     * 
     * @param string $url 链接地址
     * @param string $code 提取码
     * @return array
     */
    public function validateUrl($url, $code = '') {
        try {
            $platformType = $this->detectPlatform($url);
            if ($platformType === -1) {
                return [
                    'success' => false,
                    'message' => '不支持的网盘平台'
                ];
            }
            
            $shareId = $this->extractShareId($url);
            if (!$shareId) {
                return [
                    'success' => false,
                    'message' => '链接格式错误'
                ];
            }
            
            // 构建验证配置
            $config = [
                'url' => $url,
                'code' => $code,
                'isType' => 1 // 验证模式
            ];
            
            $transfer = new \netdisk\Transfer();
            $result = $transfer->transfer($config);
            
            if (is_array($result) && isset($result['code']) && $result['code'] === 200) {
                return [
                    'success' => true,
                    'data' => [
                        'is_valid' => true,
                        'platform' => $this->platformNames[$platformType],
                        'title' => $result['data']['title'] ?? '',
                        'file_count' => $result['data']['file_count'] ?? 0,
                        'total_size' => $result['data']['total_size'] ?? ''
                    ],
                    'message' => '链接有效'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result['msg'] ?? '链接验证失败'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 检测网盘平台类型
     * 
     * @param string $url 链接地址
     * @return int 平台类型，-1表示不支持
     */
    private function detectPlatform($url) {
        foreach ($this->urlPlatformMap as $domain => $platformType) {
            if (strpos($url, $domain) !== false) {
                return $platformType;
            }
        }
        return -1;
    }
    
    /**
     * 提取分享ID
     * 
     * @param string $url 链接地址
     * @return string|false
     */
    private function extractShareId($url) {
        $substring = strstr($url, 's/');
        if ($substring !== false) {
            $shareId = substr($substring, 2); // 去除 's/' 部分
            // 移除URL中的查询参数和锚点
            $shareId = strtok($shareId, '?');
            $shareId = strtok($shareId, '#');
            return $shareId;
        }
        return false;
    }
    
    /**
     * 获取平台配置键名
     * 
     * @param int $platformType 平台类型
     * @return string
     */
    private function getPlatformKey($platformType) {
        $keyMap = [
            self::PLATFORM_QUARK => 'quark',
            self::PLATFORM_ALIYUN => 'aliyun',
            self::PLATFORM_BAIDU => 'baidu',
            self::PLATFORM_UC => 'uc',
            self::PLATFORM_XUNLEI => 'xunlei'
        ];
        
        return $keyMap[$platformType] ?? '';
    }
    
    /**
     * 记录转存开始日志
     * 
     * @param string $url 原始链接
     * @param string $code 提取码
     */
    private function logTransferStart($url, $code) {
        try {
            $this->db->insert('transfer_logs', [
                'original_url' => $url,
                'original_code' => $code,
                'original_platform' => $this->platformNames[$this->detectPlatform($url)] ?? '',
                'user_ip' => $_SERVER['REMOTE_ADDR'] ?? '',
                'transfer_status' => 'pending',
                'start_time' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log('Transfer log failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 记录转存成功日志
     * 
     * @param string $url 原始链接
     * @param array $result 转存结果
     */
    private function logTransferSuccess($url, $result) {
        try {
            $this->db->update('transfer_logs', [
                'target_url' => $result['share_url'] ?? '',
                'target_code' => $result['code'] ?? '',
                'target_platform' => $result['platform'] ?? '',
                'resource_title' => $result['title'] ?? '',
                'resource_size' => $result['size'] ?? '',
                'transfer_status' => 'success',
                'end_time' => date('Y-m-d H:i:s')
            ], [
                'original_url' => $url,
                'transfer_status' => 'pending'
            ]);
        } catch (Exception $e) {
            error_log('Transfer success log failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 记录转存失败日志
     * 
     * @param string $url 原始链接
     * @param string $errorMsg 错误信息
     */
    private function logTransferError($url, $errorMsg) {
        try {
            $this->db->update('transfer_logs', [
                'transfer_status' => 'failed',
                'error_message' => $errorMsg,
                'end_time' => date('Y-m-d H:i:s')
            ], [
                'original_url' => $url,
                'transfer_status' => 'pending'
            ]);
        } catch (Exception $e) {
            error_log('Transfer error log failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取转存统计信息
     * 
     * @param string $timeRange 时间范围
     * @return array
     */
    public function getTransferStats($timeRange = '24h') {
        try {
            $timeCondition = $this->getTimeCondition($timeRange);
            
            // 总转存次数
            $totalTransfers = $this->db->count('transfer_logs', $timeCondition);
            
            // 成功转存次数
            $successCondition = array_merge($timeCondition, ['transfer_status' => 'success']);
            $successTransfers = $this->db->count('transfer_logs', $successCondition);
            
            // 失败转存次数
            $failedCondition = array_merge($timeCondition, ['transfer_status' => 'failed']);
            $failedTransfers = $this->db->count('transfer_logs', $failedCondition);
            
            // 成功率
            $successRate = $totalTransfers > 0 ? round(($successTransfers / $totalTransfers) * 100, 2) : 0;
            
            return [
                'total_transfers' => $totalTransfers,
                'success_transfers' => $successTransfers,
                'failed_transfers' => $failedTransfers,
                'success_rate' => $successRate . '%',
                'time_range' => $timeRange
            ];
            
        } catch (Exception $e) {
            return [
                'total_transfers' => 0,
                'success_transfers' => 0,
                'failed_transfers' => 0,
                'success_rate' => '0%',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取时间条件
     * 
     * @param string $timeRange 时间范围
     * @return array
     */
    private function getTimeCondition($timeRange) {
        $now = time();
        switch ($timeRange) {
            case '1h':
                $startTime = $now - 3600;
                break;
            case '24h':
                $startTime = $now - 86400;
                break;
            case '7d':
                $startTime = $now - 604800;
                break;
            case '30d':
                $startTime = $now - 2592000;
                break;
            default:
                $startTime = $now - 86400; // 默认24小时
        }
        
        return []; // 简化实现，实际应该返回时间条件
    }
    
    /**
     * 智能转存链接（新版，优先使用新的平台类）
     * 
     * @param string $url 原始链接
     * @param string $code 提取码
     * @param string $title 自定义标题
     * @param int $expiredType 过期类型 1永久 2临时
     * @param string $targetDir 目标目录
     * @return array
     */
    public function smartTransferV2($url, $code = '', $title = '', $expiredType = 1, $targetDir = '') {
        try {
            // 检测平台类型
            $platform = NetdiskPlatformFactory::detectPlatform($url);
            if (!$platform) {
                return [
                    'success' => false,
                    'message' => '不支持的网盘平台',
                    'error_code' => 1001
                ];
            }
            
            // 获取平台实例
            $platformInstance = NetdiskPlatformFactory::getInstance($platform);
            
            // 使用新的平台类进行转存
            $result = $platformInstance->transferLink($url, $code, $title, $expiredType, $targetDir);
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '转存失败：' . $e->getMessage(),
                'error_code' => 500
            ];
        }
    }
    
    /**
     * 批量转存到多个平台（新版）
     * 
     * @param string $url 原始链接
     * @param string $code 提取码
     * @param array $targetPlatforms 目标平台列表
     * @param array $options 转存选项
     * @return array
     */
    public function batchTransferV2($url, $code, $targetPlatforms, $options = []) {
        try {
            return NetdiskPlatformFactory::batchTransfer($url, $code, $targetPlatforms, $options);
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '批量转存失败：' . $e->getMessage(),
                'error_code' => 500
            ];
        }
    }
    
    /**
     * 验证链接格式和可用性
     * 
     * @param string $url 链接
     * @param string $code 提取码
     * @return array
     */
    public function validateLink($url, $code = '') {
        try {
            $result = NetdiskPlatformFactory::validateUrl($url);
            
            if ($result['success'] && $result['platform']) {
                // 进一步验证链接有效性
                $platformInstance = NetdiskPlatformFactory::getInstance($result['platform']);
                if (method_exists($platformInstance, 'validateLink')) {
                    $detailResult = $platformInstance->validateLink($url, $code);
                    return array_merge($result, $detailResult);
                }
            }
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '链接验证失败：' . $e->getMessage(),
                'error_code' => 500
            ];
        }
    }
    
    /**
     * 获取所有平台状态
     * 
     * @return array
     */
    public function getAllPlatformStatus() {
        try {
            return NetdiskPlatformFactory::getAllPlatformStatus();
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '获取平台状态失败：' . $e->getMessage(),
                'platforms' => []
            ];
        }
    }
}