<?php
/**
 * SOPAN Panseeker API 搜索服务
 * 集成Panseeker API，提供网盘资源搜索功能
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class PanseekerService {
    private $config;
    private $db;
    private $apiUrl;
    private $apiKey;
    private $timeout = 30;
    private $maxRetries = 3;
    
    // 搜索状态常量
    const STATUS_SEARCHING = 'searching';
    const STATUS_COMPLETED = 'completed';
    const STATUS_ERROR = 'error';
    const STATUS_CANCELLED = 'cancelled';
    
    // 支持的平台类型
    const PLATFORMS = [
        'all' => '全部',
        'quark' => '夸克网盘',
        'baidu' => '百度网盘', 
        'aliyun' => '阿里云盘',
        'uc' => 'UC网盘',
        'lanzou' => '蓝奏云',
        'thunder' => '迅雷网盘'
    ];
    
    public function __construct() {
        $this->config = Config::getInstance();
        $this->db = Database::getInstance();
        
        $this->apiUrl = $this->config->get('panseeker_api_url', 'https://api.panseeker.com/api/search');
        $this->apiKey = $this->config->get('panseeker_api_key', '');
    }
    
    /**
     * 开始异步搜索
     * 
     * @param string $keyword 搜索关键词
     * @param string $type 搜索类型
     * @param string $userIp 用户IP
     * @return array
     */
    public function startSearch($keyword, $type = 'all', $userIp = '') {
        try {
            // 参数验证
            $validation = $this->validateSearchParams($keyword, $type, $userIp);
            if (!$validation['success']) {
                return $validation;
            }
            
            // 生成搜索会话ID
            $sessionId = $this->generateSessionId();
            
            // 创建搜索会话
            $this->createSearchSession($sessionId, $keyword, $type, $userIp);
            
            // 记录搜索日志
            $this->logSearch($keyword, $type, $userIp);
            
            return [
                'success' => true,
                'session_id' => $sessionId,
                'message' => '搜索任务已启动'
            ];
            
        } catch (Exception $e) {
            error_log('PanseekerService::startSearch error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '启动搜索失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 执行搜索（用于后台处理）
     * 
     * @param string $sessionId 会话ID
     * @return array
     */
    public function executeSearch($sessionId) {
        try {
            // 获取搜索会话
            $session = $this->getSearchSession($sessionId);
            if (!$session) {
                return [
                    'success' => false,
                    'message' => '搜索会话不存在'
                ];
            }
            
            // 更新状态为处理中
            $this->updateSearchSession($sessionId, [
                'status' => self::STATUS_SEARCHING,
                'progress' => 10
            ]);
            
            // 调用Panseeker API
            $results = $this->callPanseekerAPI($session['keyword'], $session['type_filter']);
            
            if ($results['success']) {
                // 保存搜索结果
                $savedCount = $this->saveSearchResults($sessionId, $results['data']);
                
                // 更新会话状态为完成
                $this->updateSearchSession($sessionId, [
                    'status' => self::STATUS_COMPLETED,
                    'progress' => 100,
                    'total_results' => $savedCount,
                    'end_time' => date('Y-m-d H:i:s')
                ]);
                
                return [
                    'success' => true,
                    'total_results' => $savedCount,
                    'message' => '搜索完成'
                ];
            } else {
                // 更新会话状态为错误
                $this->updateSearchSession($sessionId, [
                    'status' => self::STATUS_ERROR,
                    'progress' => 0,
                    'end_time' => date('Y-m-d H:i:s')
                ]);
                
                return $results;
            }
            
        } catch (Exception $e) {
            // 更新会话状态为错误
            $this->updateSearchSession($sessionId, [
                'status' => self::STATUS_ERROR,
                'progress' => 0,
                'end_time' => date('Y-m-d H:i:s')
            ]);
            
            error_log('PanseekerService::executeSearch error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '搜索执行失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取搜索状态
     * 
     * @param string $sessionId 会话ID
     * @return array
     */
    public function getSearchStatus($sessionId) {
        try {
            $session = $this->getSearchSession($sessionId);
            if (!$session) {
                return [
                    'success' => false,
                    'message' => '搜索会话不存在'
                ];
            }
            
            return [
                'success' => true,
                'data' => [
                    'session_id' => $sessionId,
                    'keyword' => $session['keyword'],
                    'status' => $session['status'],
                    'progress' => (int)$session['progress'],
                    'total_results' => (int)$session['total_results'],
                    'start_time' => $session['start_time'],
                    'end_time' => $session['end_time']
                ]
            ];
            
        } catch (Exception $e) {
            error_log('PanseekerService::getSearchStatus error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '获取搜索状态失败'
            ];
        }
    }
    
    /**
     * 获取搜索结果
     * 
     * @param string $sessionId 会话ID
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getSearchResults($sessionId, $page = 1, $limit = 20) {
        try {
            $offset = ($page - 1) * $limit;
            
            $results = $this->db->findAll('search_results_cache', [
                'session_id' => $sessionId
            ], [
                'order' => 'match_score DESC, create_time ASC',
                'limit' => $limit,
                'offset' => $offset
            ]);
            
            // 解析结果数据
            $formattedResults = [];
            foreach ($results as $result) {
                $resultData = json_decode($result['result_data'], true);
                
                $formattedResults[] = [
                    'id' => $result['id'],
                    'source' => $result['source'],
                    'platform_type' => $result['platform_type'],
                    'match_score' => (float)$result['match_score'],
                    'title' => $resultData['title'] ?? '',
                    'url' => $resultData['url'] ?? '',
                    'code' => $resultData['code'] ?? '',
                    'size' => $resultData['size'] ?? '',
                    'file_count' => $resultData['file_count'] ?? 0,
                    'update_time' => $resultData['update_time'] ?? '',
                    'description' => $resultData['description'] ?? ''
                ];
            }
            
            // 获取总数
            $total = $this->db->count('search_results_cache', [
                'session_id' => $sessionId
            ]);
            
            return [
                'success' => true,
                'data' => [
                    'results' => $formattedResults,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => $total,
                        'pages' => ceil($total / $limit)
                    ]
                ]
            ];
            
        } catch (Exception $e) {
            error_log('PanseekerService::getSearchResults error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '获取搜索结果失败'
            ];
        }
    }
    
    /**
     * 取消搜索
     * 
     * @param string $sessionId 会话ID
     * @return array
     */
    public function cancelSearch($sessionId) {
        try {
            $session = $this->getSearchSession($sessionId);
            if (!$session) {
                return [
                    'success' => false,
                    'message' => '搜索会话不存在'
                ];
            }
            
            if ($session['status'] === self::STATUS_COMPLETED) {
                return [
                    'success' => false,
                    'message' => '搜索已完成，无法取消'
                ];
            }
            
            // 更新状态为取消
            $this->updateSearchSession($sessionId, [
                'status' => self::STATUS_CANCELLED,
                'end_time' => date('Y-m-d H:i:s')
            ]);
            
            return [
                'success' => true,
                'message' => '搜索已取消'
            ];
            
        } catch (Exception $e) {
            error_log('PanseekerService::cancelSearch error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '取消搜索失败'
            ];
        }
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 验证搜索参数
     */
    private function validateSearchParams($keyword, $type, $userIp) {
        if (empty($keyword) || strlen($keyword) < 2) {
            return [
                'success' => false,
                'message' => '搜索关键词至少需要2个字符'
            ];
        }
        
        if (strlen($keyword) > 100) {
            return [
                'success' => false,
                'message' => '搜索关键词不能超过100个字符'
            ];
        }
        
        if (!array_key_exists($type, self::PLATFORMS)) {
            return [
                'success' => false,
                'message' => '不支持的搜索类型'
            ];
        }
        
        // 检查IP限制
        if ($this->config->get('max_search_per_ip', 0) > 0) {
            $todaySearches = $this->db->count('search_logs', [
                'user_ip' => $userIp,
                'DATE(search_time)' => date('Y-m-d')
            ]);
            
            if ($todaySearches >= $this->config->get('max_search_per_ip', 100)) {
                return [
                    'success' => false,
                    'message' => '今日搜索次数已达上限'
                ];
            }
        }
        
        return ['success' => true];
    }
    
    /**
     * 生成搜索会话ID
     */
    private function generateSessionId() {
        return uniqid('search_', true) . '_' . time();
    }
    
    /**
     * 创建搜索会话
     */
    private function createSearchSession($sessionId, $keyword, $type, $userIp) {
        return $this->db->insert('search_sessions', [
            'session_id' => $sessionId,
            'keyword' => $keyword,
            'type_filter' => $type,
            'status' => self::STATUS_SEARCHING,
            'start_time' => date('Y-m-d H:i:s'),
            'progress' => 0,
            'total_results' => 0,
            'user_ip' => $userIp
        ]);
    }
    
    /**
     * 获取搜索会话
     */
    private function getSearchSession($sessionId) {
        return $this->db->find('search_sessions', ['session_id' => $sessionId]);
    }
    
    /**
     * 更新搜索会话
     */
    private function updateSearchSession($sessionId, $data) {
        return $this->db->update('search_sessions', $data, ['session_id' => $sessionId]);
    }
    
    /**
     * 记录搜索日志
     */
    private function logSearch($keyword, $type, $userIp) {
        return $this->db->insert('search_logs', [
            'keyword' => $keyword,
            'search_type' => $type,
            'user_ip' => $userIp,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'search_time' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 调用Panseeker API
     */
    private function callPanseekerAPI($keyword, $type) {
        try {
            $url = $this->apiUrl;
            $params = [
                'keyword' => $keyword,
                'type' => $type === 'all' ? '' : $type,
                'limit' => 100
            ];
            
            // 添加API密钥（如果配置了）
            if (!empty($this->apiKey)) {
                $params['api_key'] = $this->apiKey;
            }
            
            $postData = json_encode($params);
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $postData,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $this->timeout,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'User-Agent: SOPAN/1.0.0',
                    'Accept: application/json'
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            
            if ($curlError) {
                throw new Exception("CURL错误: {$curlError}");
            }
            
            if ($httpCode !== 200) {
                throw new Exception("HTTP错误: {$httpCode}");
            }
            
            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("JSON解析错误: " . json_last_error_msg());
            }
            
            // 处理API响应
            if (isset($result['success']) && $result['success']) {
                return [
                    'success' => true,
                    'data' => $result['data'] ?? []
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result['message'] ?? 'API调用失败'
                ];
            }
            
        } catch (Exception $e) {
            error_log('PanseekerService::callPanseekerAPI error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'API调用失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 保存搜索结果
     */
    private function saveSearchResults($sessionId, $results) {
        $savedCount = 0;
        
        foreach ($results as $result) {
            try {
                // 计算匹配分数（这里使用简单的规则，可以根据需要优化）
                $matchScore = $this->calculateMatchScore($result);
                
                // 确定平台类型
                $platformType = $this->detectPlatformType($result);
                
                $this->db->insert('search_results_cache', [
                    'session_id' => $sessionId,
                    'source' => $result['source'] ?? 'panseeker',
                    'result_data' => json_encode($result, JSON_UNESCAPED_UNICODE),
                    'platform_type' => $platformType,
                    'match_score' => $matchScore,
                    'create_time' => date('Y-m-d H:i:s'),
                    'expire_time' => date('Y-m-d H:i:s', time() + 86400) // 24小时后过期
                ]);
                
                $savedCount++;
            } catch (Exception $e) {
                error_log('Save search result error: ' . $e->getMessage());
                // 继续处理其他结果
            }
        }
        
        return $savedCount;
    }
    
    /**
     * 计算匹配分数
     */
    private function calculateMatchScore($result) {
        $score = 50; // 基础分数
        
        // 根据标题相关性调整分数
        if (!empty($result['title'])) {
            $titleLength = mb_strlen($result['title']);
            if ($titleLength > 10) $score += 10;
            if ($titleLength > 20) $score += 10;
        }
        
        // 根据文件数量调整分数
        if (!empty($result['file_count']) && $result['file_count'] > 1) {
            $score += min($result['file_count'], 20);
        }
        
        // 根据更新时间调整分数
        if (!empty($result['update_time'])) {
            $updateTime = strtotime($result['update_time']);
            $daysDiff = (time() - $updateTime) / 86400;
            if ($daysDiff < 30) {
                $score += 15;
            } else if ($daysDiff < 90) {
                $score += 10;
            }
        }
        
        return min(100, max(0, $score));
    }
    
    /**
     * 检测平台类型
     */
    private function detectPlatformType($result) {
        $url = $result['url'] ?? '';
        
        if (strpos($url, 'pan.quark.cn') !== false) {
            return 'quark';
        } elseif (strpos($url, 'pan.baidu.com') !== false) {
            return 'baidu';
        } elseif (strpos($url, 'aliyundrive.com') !== false || strpos($url, 'alipan.com') !== false) {
            return 'aliyun';
        } elseif (strpos($url, 'drive.uc.cn') !== false) {
            return 'uc';
        } elseif (strpos($url, 'lanzou') !== false) {
            return 'lanzou';
        } elseif (strpos($url, 'xunlei') !== false) {
            return 'thunder';
        }
        
        return 'unknown';
    }
}