<?php
/**
 * SOPAN 基础模型类
 * 提供通用的数据库操作方法
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

abstract class BaseModel {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    protected $timestamps = true;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 查找单条记录
     * 
     * @param mixed $id 主键值或查询条件
     * @param string $fields 查询字段
     * @return array|null
     */
    public function find($id, $fields = '*') {
        if (is_array($id)) {
            $conditions = $id;
        } else {
            $conditions = [$this->primaryKey => $id];
        }
        
        $result = $this->db->find($this->table, $conditions, $fields);
        
        if ($result && !empty($this->hidden)) {
            $result = $this->hideFields($result);
        }
        
        return $result;
    }
    
    /**
     * 查找多条记录
     * 
     * @param array $conditions 查询条件
     * @param string $fields 查询字段
     * @param string $orderBy 排序
     * @param int $limit 限制条数
     * @param int $offset 偏移量
     * @return array
     */
    public function findAll($conditions = [], $fields = '*', $orderBy = '', $limit = 0, $offset = 0) {
        $results = $this->db->findAll($this->table, $conditions, $fields, $orderBy, $limit, $offset);
        
        if (!empty($this->hidden)) {
            foreach ($results as &$result) {
                $result = $this->hideFields($result);
            }
        }
        
        return $results;
    }
    
    /**
     * 创建记录
     * 
     * @param array $data 数据
     * @return int 插入的ID
     */
    public function create($data) {
        // 过滤可填充字段
        if (!empty($this->fillable)) {
            $data = array_intersect_key($data, array_flip($this->fillable));
        }
        
        // 添加时间戳
        if ($this->timestamps) {
            $data['created_time'] = date('Y-m-d H:i:s');
            $data['updated_time'] = date('Y-m-d H:i:s');
        }
        
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * 更新记录
     * 
     * @param mixed $id 主键值或查询条件
     * @param array $data 数据
     * @return int 影响行数
     */
    public function update($id, $data) {
        // 过滤可填充字段
        if (!empty($this->fillable)) {
            $data = array_intersect_key($data, array_flip($this->fillable));
        }
        
        // 添加更新时间戳
        if ($this->timestamps) {
            $data['updated_time'] = date('Y-m-d H:i:s');
        }
        
        if (is_array($id)) {
            $conditions = $id;
        } else {
            $conditions = [$this->primaryKey => $id];
        }
        
        return $this->db->update($this->table, $data, $conditions);
    }
    
    /**
     * 删除记录
     * 
     * @param mixed $id 主键值或查询条件
     * @return int 影响行数
     */
    public function delete($id) {
        if (is_array($id)) {
            $conditions = $id;
        } else {
            $conditions = [$this->primaryKey => $id];
        }
        
        return $this->db->delete($this->table, $conditions);
    }
    
    /**
     * 统计记录数
     * 
     * @param array $conditions 查询条件
     * @return int
     */
    public function count($conditions = []) {
        return $this->db->count($this->table, $conditions);
    }
    
    /**
     * 分页查询
     * 
     * @param int $page 页码
     * @param int $size 每页大小
     * @param array $conditions 查询条件
     * @param string $orderBy 排序
     * @return array
     */
    public function paginate($page = 1, $size = 20, $conditions = [], $orderBy = '') {
        $page = max(1, intval($page));
        $size = max(1, intval($size));
        $offset = ($page - 1) * $size;
        
        $total = $this->count($conditions);
        $items = $this->findAll($conditions, '*', $orderBy, $size, $offset);
        
        return [
            'items' => $items,
            'pagination' => [
                'page' => $page,
                'size' => $size,
                'total' => $total,
                'pages' => ceil($total / $size),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($total / $size)
            ]
        ];
    }
    
    /**
     * 执行原生SQL查询
     * 
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return array
     */
    public function query($sql, $params = []) {
        $stmt = $this->db->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 执行原生SQL查询（单条记录）
     * 
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return array|null
     */
    public function queryOne($sql, $params = []) {
        $stmt = $this->db->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->db->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        return $this->db->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        return $this->db->rollback();
    }
    
    /**
     * 执行事务
     * 
     * @param callable $callback 回调函数
     * @return mixed
     */
    public function transaction($callback) {
        return $this->db->transaction($callback);
    }
    
    /**
     * 隐藏指定字段
     * 
     * @param array $data 数据
     * @return array
     */
    private function hideFields($data) {
        foreach ($this->hidden as $field) {
            unset($data[$field]);
        }
        return $data;
    }
    
    /**
     * 设置表名
     * 
     * @param string $table 表名
     * @return BaseModel
     */
    public function setTable($table) {
        $this->table = $table;
        return $this;
    }
    
    /**
     * 获取表名
     * 
     * @return string
     */
    public function getTable() {
        return $this->table;
    }
}