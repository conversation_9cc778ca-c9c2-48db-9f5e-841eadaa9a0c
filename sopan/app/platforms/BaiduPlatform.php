<?php
/**
 * SOPAN 百度网盘平台类
 * 优化的百度网盘转存功能实现
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class BaiduPlatform {
    private $config;
    private $db;
    private $baiduWork;
    
    // 转存状态
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    
    // 错误码定义
    const ERROR_INVALID_URL = 2001;
    const ERROR_INVALID_CODE = 2002;
    const ERROR_NETWORK_ERROR = 2003;
    const ERROR_AUTH_FAILED = 2004;
    const ERROR_CAPACITY_FULL = 2005;
    const ERROR_SHARE_FAILED = 2006;
    const ERROR_BANNED_CONTENT = 2007;
    const ERROR_CREATE_DIR_FAILED = 2008;
    
    public function __construct() {
        $this->config = Config::getInstance();
        $this->db = Database::getInstance();
        
        $this->initializeBaiduWork();
    }
    
    /**
     * 初始化百度网盘工作类
     */
    private function initializeBaiduWork() {
        $cookie = $this->config->get('baidu_cookie', '');
        
        // 加载BaiduWork类
        $baiduWorkPath = dirname(__DIR__) . '/../extend/netdisk/pan/BaiduWork.php';
        if (file_exists($baiduWorkPath)) {
            require_once $baiduWorkPath;
            $this->baiduWork = new \netdisk\pan\BaiduWork($cookie);
        }
    }
    
    /**
     * 检查百度网盘是否已配置
     * 
     * @return array
     */
    public function checkConfiguration() {
        $cookie = $this->config->get('baidu_cookie', '');
        $status = $this->config->get('baidu_status', '0');
        
        if (empty($cookie)) {
            return [
                'success' => false,
                'message' => '百度网盘Cookie未配置',
                'error_code' => self::ERROR_AUTH_FAILED
            ];
        }
        
        if ($status !== '1') {
            return [
                'success' => false,
                'message' => '百度网盘未启用',
                'error_code' => self::ERROR_AUTH_FAILED
            ];
        }
        
        if (!$this->baiduWork) {
            return [
                'success' => false,
                'message' => '百度网盘工作类初始化失败',
                'error_code' => self::ERROR_NETWORK_ERROR
            ];
        }
        
        return ['success' => true];
    }
    
    /**
     * 验证百度网盘链接
     * 
     * @param string $url 分享链接
     * @param string $code 提取码
     * @return array
     */
    public function validateLink($url, $code = '') {
        try {
            // 检查配置
            $configCheck = $this->checkConfiguration();
            if (!$configCheck['success']) {
                return $configCheck;
            }
            
            // 验证链接格式
            if (!$this->isValidBaiduUrl($url)) {
                return [
                    'success' => false,
                    'message' => '无效的百度网盘链接格式',
                    'error_code' => self::ERROR_INVALID_URL
                ];
            }
            
            // 获取bdstoken
            $bdstoken = $this->baiduWork->getBdstoken();
            $this->baiduWork->setBdstoken($bdstoken);
            
            // 解析链接信息
            $urlParts = parse_url($url);
            $linkUrl = $urlParts['scheme'] . '://' . $urlParts['host'] . $urlParts['path'];
            
            // 验证提取码（如果有）
            if (!empty($code)) {
                $randsk = $this->baiduWork->verifyPassCode($linkUrl, $code);
                if (is_numeric($randsk)) {
                    return [
                        'success' => false,
                        'message' => $this->baiduWork->getErrorMessage($randsk),
                        'error_code' => self::ERROR_INVALID_CODE
                    ];
                }
                // 更新cookie
                $this->baiduWork->updateBdclnd($randsk);
            }
            
            // 获取分享信息
            $transferParams = $this->baiduWork->getTransferParams($linkUrl);
            if (is_numeric($transferParams)) {
                return [
                    'success' => false,
                    'message' => $this->baiduWork->getErrorMessage($transferParams),
                    'error_code' => self::ERROR_NETWORK_ERROR
                ];
            }
            
            list($shareId, $userId, $fsIds, $fileNames, $isDirs) = $transferParams;
            
            return [
                'success' => true,
                'data' => [
                    'share_id' => $shareId,
                    'user_id' => $userId,
                    'title' => $fileNames[0] ?? '未知资源',
                    'file_count' => count($fileNames),
                    'file_names' => $fileNames
                ],
                'message' => '链接验证成功'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '链接验证失败：' . $e->getMessage(),
                'error_code' => self::ERROR_NETWORK_ERROR
            ];
        }
    }
    
    /**
     * 转存链接到百度网盘
     * 
     * @param string $url 分享链接
     * @param string $code 提取码
     * @param string $title 自定义标题
     * @param int $expiredType 过期类型
     * @param string $targetDir 目标目录
     * @return array
     */
    public function transferLink($url, $code = '', $title = '', $expiredType = 1, $targetDir = '') {
        try {
            // 记录转存开始
            $transferId = $this->logTransferStart($url, $code);
            
            // 验证链接
            $validation = $this->validateLink($url, $code);
            if (!$validation['success']) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $validation['message']);
                return $validation;
            }
            
            $shareId = $validation['data']['share_id'];
            $userId = $validation['data']['user_id'];
            $fileNames = $validation['data']['file_names'];
            $resourceTitle = $title ?: $validation['data']['title'];
            
            // 获取转存参数
            $urlParts = parse_url($url);
            $linkUrl = $urlParts['scheme'] . '://' . $urlParts['host'] . $urlParts['path'];
            $transferParams = $this->baiduWork->getTransferParams($linkUrl);
            
            if (is_numeric($transferParams)) {
                $errorMsg = $this->baiduWork->getErrorMessage($transferParams);
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $errorMsg);
                return [
                    'success' => false,
                    'message' => $errorMsg,
                    'error_code' => self::ERROR_NETWORK_ERROR
                ];
            }
            
            list($shareId, $userId, $fsIds, $fileNames, $isDirs) = $transferParams;
            
            // 确定目标目录
            $folderName = $targetDir ?: $this->getDefaultFolder($expiredType);
            
            // 验证目录名
            if (!$this->isValidDirectoryName($folderName)) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, '目录名包含非法字符');
                return [
                    'success' => false,
                    'message' => '转存目录名有非法字符，不能包含：< > | * ? \\ :',
                    'error_code' => self::ERROR_CREATE_DIR_FAILED
                ];
            }
            
            // 检查并创建目录
            $dirResult = $this->ensureDirectory($folderName);
            if (!$dirResult['success']) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $dirResult['message']);
                return $dirResult;
            }
            
            // 执行转存
            $transferResult = $this->baiduWork->transferFile([$shareId, $userId, $fsIds], $folderName);
            if ($transferResult !== 0) {
                $errorMsg = $this->baiduWork->getErrorMessage($transferResult);
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $errorMsg);
                return [
                    'success' => false,
                    'message' => $errorMsg,
                    'error_code' => self::ERROR_NETWORK_ERROR
                ];
            }
            
            // 处理转存后的文件
            $processResult = $this->processTransferredFiles($folderName, $fileNames);
            if (!$processResult['success']) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $processResult['message']);
                return $processResult;
            }
            
            $fsIdList = $processResult['data']['fs_id_list'];
            
            // 创建分享
            $shareResult = $this->createShare($fsIdList, $resourceTitle);
            if (!$shareResult['success']) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $shareResult['message']);
                return $shareResult;
            }
            
            // 记录转存成功
            $this->logTransferEnd($transferId, self::STATUS_SUCCESS, '转存成功');
            
            return [
                'success' => true,
                'data' => [
                    'share_url' => $shareResult['data']['share_url'],
                    'code' => $shareResult['data']['code'],
                    'title' => $resourceTitle,
                    'platform' => '百度网盘',
                    'platform_key' => 'baidu',
                    'expired_type' => $expiredType,
                    'transfer_time' => date('Y-m-d H:i:s'),
                    'transfer_id' => $transferId
                ],
                'message' => '转存成功'
            ];
            
        } catch (Exception $e) {
            if (isset($transferId)) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $e->getMessage());
            }
            
            return [
                'success' => false,
                'message' => '转存失败：' . $e->getMessage(),
                'error_code' => self::ERROR_NETWORK_ERROR
            ];
        }
    }
    
    /**
     * 获取网盘文件列表
     * 
     * @param string $parentId 父目录路径
     * @return array
     */
    public function getFileList($parentId = '/') {
        try {
            $configCheck = $this->checkConfiguration();
            if (!$configCheck['success']) {
                return $configCheck;
            }
            
            if ($parentId === '0' || empty($parentId)) {
                $parentId = '/';
            }
            
            $dirList = $this->baiduWork->getDirList($parentId);
            
            if (is_numeric($dirList)) {
                return [
                    'success' => false,
                    'message' => $this->baiduWork->getErrorMessage($dirList),
                    'error_code' => self::ERROR_NETWORK_ERROR
                ];
            }
            
            return [
                'success' => true,
                'data' => [
                    'list' => $dirList,
                    'total' => count($dirList)
                ],
                'message' => '获取成功'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '获取文件列表失败：' . $e->getMessage(),
                'error_code' => self::ERROR_NETWORK_ERROR
            ];
        }
    }
    
    /**
     * 检查是否为有效的百度网盘链接
     * 
     * @param string $url 链接
     * @return bool
     */
    private function isValidBaiduUrl($url) {
        return strpos($url, 'pan.baidu.com') !== false;
    }
    
    /**
     * 获取默认存储目录
     * 
     * @param int $expiredType 过期类型
     * @return string
     */
    private function getDefaultFolder($expiredType) {
        if ($expiredType == 2) {
            $folder = $this->config->get('baidu_file_path_temp', '/SOPAN临时');
        } else {
            $folder = $this->config->get('baidu_file_path', '/SOPAN转存');
        }
        
        // 移除开头的斜杠（百度网盘不需要）
        return ltrim($folder, '/');
    }
    
    /**
     * 验证目录名是否有效
     * 
     * @param string $dirName 目录名
     * @return bool
     */
    private function isValidDirectoryName($dirName) {
        $invalidChars = ['<', '>', '|', '*', '?', '\\', ':'];
        foreach ($invalidChars as $char) {
            if (strpos($dirName, $char) !== false) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 确保目录存在，不存在则创建
     * 
     * @param string $folderName 目录名
     * @return array
     */
    private function ensureDirectory($folderName) {
        try {
            $dirList = $this->baiduWork->getDirList($folderName);
            
            // 如果返回错误码，说明目录不存在，需要创建
            if (is_numeric($dirList)) {
                $createResult = $this->baiduWork->createDir($folderName);
                if ($createResult !== 0) {
                    return [
                        'success' => false,
                        'message' => $this->baiduWork->getErrorMessage($createResult),
                        'error_code' => self::ERROR_CREATE_DIR_FAILED
                    ];
                }
            }
            
            return ['success' => true];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '创建目录失败：' . $e->getMessage(),
                'error_code' => self::ERROR_CREATE_DIR_FAILED
            ];
        }
    }
    
    /**
     * 处理转存后的文件（清理广告文件等）
     * 
     * @param string $folderName 目录名
     * @param array $fileNames 文件名列表
     * @return array
     */
    private function processTransferredFiles($folderName, $fileNames) {
        try {
            // 获取目录中的文件列表
            $dirList = $this->baiduWork->getDirList('/' . $folderName);
            if (is_numeric($dirList)) {
                return [
                    'success' => false,
                    'message' => $this->baiduWork->getErrorMessage($dirList),
                    'error_code' => self::ERROR_NETWORK_ERROR
                ];
            }
            
            // 找到刚刚转存的文件
            $targetFiles = [];
            $fsIdList = [];
            $filePaths = [];
            $adFilePaths = [];
            $allFilesAreAds = true;
            
            foreach ($dirList as $file) {
                if (in_array($file['server_filename'], $fileNames)) {
                    $targetFiles[] = $file;
                    $fsIdList[] = $file['fs_id'];
                    $filePath = '/' . $folderName . '/' . $file['server_filename'];
                    $filePaths[] = $filePath;
                    
                    // 检查是否包含广告关键词
                    $containsAd = $this->containsAdKeywords($file['server_filename']);
                    
                    if ($file['isdir'] == 1) {
                        // 检查目录内容
                        $subDirList = $this->baiduWork->getDirList($filePath);
                        if (!is_numeric($subDirList)) {
                            foreach ($subDirList as $subFile) {
                                if ($this->containsAdKeywords($subFile['server_filename'])) {
                                    $adFilePaths[] = $filePath . '/' . $subFile['server_filename'];
                                } else {
                                    $allFilesAreAds = false;
                                }
                            }
                        }
                    } else {
                        if ($containsAd) {
                            $adFilePaths[] = $filePath;
                        } else {
                            $allFilesAreAds = false;
                        }
                    }
                }
            }
            
            if (empty($targetFiles)) {
                return [
                    'success' => false,
                    'message' => '找不到刚转存的文件',
                    'error_code' => self::ERROR_NETWORK_ERROR
                ];
            }
            
            // 处理广告文件
            if ($allFilesAreAds && !empty($targetFiles)) {
                // 删除所有文件
                $deleteResult = $this->baiduWork->batchDeleteFiles($filePaths);
                return [
                    'success' => false,
                    'message' => '资源内容为空或所有转存的文件都包含广告内容，已全部删除',
                    'error_code' => self::ERROR_BANNED_CONTENT
                ];
            }
            
            // 删除部分广告文件
            if (!empty($adFilePaths)) {
                $deleteResult = $this->baiduWork->batchDeleteFiles($adFilePaths);
                if ($deleteResult['errno'] === 0) {
                    // 从列表中移除已删除的文件
                    foreach ($adFilePaths as $adPath) {
                        $key = array_search($adPath, $filePaths);
                        if ($key !== false) {
                            unset($filePaths[$key]);
                            unset($fsIdList[$key]);
                        }
                    }
                    $fsIdList = array_values($fsIdList);
                }
            }
            
            if (empty($fsIdList)) {
                return [
                    'success' => false,
                    'message' => '资源内容为空或所有转存的文件都包含广告内容，已全部删除',
                    'error_code' => self::ERROR_BANNED_CONTENT
                ];
            }
            
            return [
                'success' => true,
                'data' => [
                    'fs_id_list' => $fsIdList,
                    'file_paths' => array_values($filePaths),
                    'deleted_ads' => count($adFilePaths)
                ]
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '处理转存文件失败：' . $e->getMessage(),
                'error_code' => self::ERROR_NETWORK_ERROR
            ];
        }
    }
    
    /**
     * 检查文件名是否包含广告关键词
     * 
     * @param string $filename 文件名
     * @return bool
     */
    private function containsAdKeywords($filename) {
        $bannedKeywords = $this->config->get('baidu_banned_keywords', '');
        if (empty($bannedKeywords)) {
            return false;
        }
        
        $keywords = explode(',', $bannedKeywords);
        $keywords = array_map('trim', $keywords);
        $keywords = array_filter($keywords);
        
        foreach ($keywords as $keyword) {
            if (strpos($filename, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 创建分享链接
     * 
     * @param array $fsIdList 文件ID列表
     * @param string $title 分享标题
     * @return array
     */
    private function createShare($fsIdList, $title) {
        try {
            $expiry = 0; // 永久有效
            $password = '6666'; // 固定提取码，也可以随机生成
            
            $shareLink = $this->baiduWork->createShare(implode(',', $fsIdList), $expiry, $password);
            
            if (is_numeric($shareLink)) {
                return [
                    'success' => false,
                    'message' => $this->baiduWork->getErrorMessage($shareLink),
                    'error_code' => self::ERROR_SHARE_FAILED
                ];
            }
            
            // 构造完整的分享链接
            $fullShareUrl = $shareLink;
            if (!empty($password)) {
                $fullShareUrl .= '?pwd=' . $password;
            }
            
            return [
                'success' => true,
                'data' => [
                    'share_url' => $shareLink,
                    'full_url' => $fullShareUrl,
                    'code' => $password,
                    'title' => $title
                ]
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '创建分享失败：' . $e->getMessage(),
                'error_code' => self::ERROR_SHARE_FAILED
            ];
        }
    }
    
    /**
     * 记录转存开始
     * 
     * @param string $url 原始链接
     * @param string $code 提取码
     * @return string 转存ID
     */
    private function logTransferStart($url, $code) {
        $transferId = uniqid('baidu_', true);
        
        $this->db->insert('transfer_logs', [
            'id' => $transferId,
            'original_url' => $url,
            'original_code' => $code,
            'original_platform' => 'unknown',
            'target_platform' => 'baidu',
            'transfer_status' => self::STATUS_PENDING,
            'user_ip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'start_time' => date('Y-m-d H:i:s')
        ]);
        
        return $transferId;
    }
    
    /**
     * 记录转存结束
     * 
     * @param string $transferId 转存ID
     * @param string $status 转存状态
     * @param string $message 消息
     */
    private function logTransferEnd($transferId, $status, $message) {
        $updateData = [
            'transfer_status' => $status,
            'end_time' => date('Y-m-d H:i:s')
        ];
        
        if ($status === self::STATUS_FAILED) {
            $updateData['error_message'] = $message;
        }
        
        // 计算耗时
        $log = $this->db->find('transfer_logs', ['id' => $transferId]);
        if ($log) {
            $startTime = strtotime($log['start_time']);
            $endTime = time();
            $updateData['duration'] = $endTime - $startTime;
        }
        
        $this->db->update('transfer_logs', $updateData, ['id' => $transferId]);
    }
}