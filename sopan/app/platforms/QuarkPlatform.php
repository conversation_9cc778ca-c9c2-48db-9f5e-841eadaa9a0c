<?php
/**
 * SOPAN 夸克网盘平台类
 * 优化的夸克网盘转存功能实现
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class QuarkPlatform {
    private $config;
    private $db;
    private $httpClient;
    
    // 夸克网盘API配置
    private $apiBaseUrl = 'https://drive-pc.quark.cn/1/clouddrive';
    private $headers = [];
    
    // 转存状态
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    
    // 错误码定义
    const ERROR_INVALID_URL = 1001;
    const ERROR_INVALID_CODE = 1002;
    const ERROR_NETWORK_ERROR = 1003;
    const ERROR_AUTH_FAILED = 1004;
    const ERROR_CAPACITY_FULL = 1005;
    const ERROR_SHARE_FAILED = 1006;
    const ERROR_BANNED_CONTENT = 1007;
    
    public function __construct() {
        $this->config = Config::getInstance();
        $this->db = Database::getInstance();
        $this->httpClient = new HttpClient();
        
        $this->initializeHeaders();
    }
    
    /**
     * 初始化请求头
     */
    private function initializeHeaders() {
        $cookie = $this->config->get('quark_cookie', '');
        
        $this->headers = [
            'Accept: application/json, text/plain, */*',
            'Accept-Language: zh-CN,zh;q=0.9',
            'Content-Type: application/json;charset=UTF-8',
            'sec-ch-ua: "Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "Windows"',
            'sec-fetch-dest: empty',
            'sec-fetch-mode: cors',
            'sec-fetch-site: same-site',
            'Referer: https://pan.quark.cn/',
            'Referrer-Policy: strict-origin-when-cross-origin',
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Cookie: ' . $cookie
        ];
    }
    
    /**
     * 检查夸克网盘是否已配置
     * 
     * @return array
     */
    public function checkConfiguration() {
        $cookie = $this->config->get('quark_cookie', '');
        $status = $this->config->get('quark_status', '0');
        
        if (empty($cookie)) {
            return [
                'success' => false,
                'message' => '夸克网盘Cookie未配置',
                'error_code' => self::ERROR_AUTH_FAILED
            ];
        }
        
        if ($status !== '1') {
            return [
                'success' => false,
                'message' => '夸克网盘未启用',
                'error_code' => self::ERROR_AUTH_FAILED
            ];
        }
        
        return ['success' => true];
    }
    
    /**
     * 验证夸克网盘链接
     * 
     * @param string $url 分享链接
     * @param string $code 提取码
     * @return array
     */
    public function validateLink($url, $code = '') {
        try {
            // 检查配置
            $configCheck = $this->checkConfiguration();
            if (!$configCheck['success']) {
                return $configCheck;
            }
            
            // 提取share_id
            $shareId = $this->extractShareId($url);
            if (!$shareId) {
                return [
                    'success' => false,
                    'message' => '无效的夸克网盘链接格式',
                    'error_code' => self::ERROR_INVALID_URL
                ];
            }
            
            // 获取stoken验证链接有效性
            $result = $this->getStoken($shareId, $code);
            if (!$result['success']) {
                return $result;
            }
            
            return [
                'success' => true,
                'data' => [
                    'share_id' => $shareId,
                    'title' => $result['data']['title'] ?? '未知资源',
                    'stoken' => $result['data']['stoken']
                ],
                'message' => '链接验证成功'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '链接验证失败：' . $e->getMessage(),
                'error_code' => self::ERROR_NETWORK_ERROR
            ];
        }
    }
    
    /**
     * 转存链接到夸克网盘
     * 
     * @param string $url 分享链接
     * @param string $code 提取码
     * @param string $title 自定义标题
     * @param int $expiredType 过期类型
     * @param string $targetDir 目标目录
     * @return array
     */
    public function transferLink($url, $code = '', $title = '', $expiredType = 1, $targetDir = '') {
        try {
            // 记录转存开始
            $transferId = $this->logTransferStart($url, $code);
            
            // 验证链接
            $validation = $this->validateLink($url, $code);
            if (!$validation['success']) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $validation['message']);
                return $validation;
            }
            
            $shareId = $validation['data']['share_id'];
            $stoken = $validation['data']['stoken'];
            $resourceTitle = $title ?: $validation['data']['title'];
            
            // 获取分享详情
            $shareDetail = $this->getShareDetail($shareId, $stoken);
            if (!$shareDetail['success']) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $shareDetail['message']);
                return $shareDetail;
            }
            
            $fileList = $shareDetail['data']['list'];
            $fidList = [];
            $fidTokenList = [];
            
            foreach ($fileList as $file) {
                $fidList[] = $file['fid'];
                $fidTokenList[] = $file['share_fid_token'];
            }
            
            // 转存到网盘
            $saveResult = $this->saveToNetdisk($shareId, $stoken, $fidList, $fidTokenList, $targetDir);
            if (!$saveResult['success']) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $saveResult['message']);
                return $saveResult;
            }
            
            $savedFids = $saveResult['data']['save_as_top_fids'];
            
            // 清理广告文件
            $this->cleanBannedFiles($savedFids);
            
            // 重新分享
            $shareResult = $this->createShare($savedFids, $resourceTitle, $expiredType);
            if (!$shareResult['success']) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $shareResult['message']);
                return $shareResult;
            }
            
            // 记录转存成功
            $this->logTransferEnd($transferId, self::STATUS_SUCCESS, '转存成功');
            
            return [
                'success' => true,
                'data' => [
                    'share_url' => $shareResult['data']['share_url'],
                    'code' => $shareResult['data']['code'],
                    'title' => $resourceTitle,
                    'platform' => '夸克网盘',
                    'platform_key' => 'quark',
                    'expired_type' => $expiredType,
                    'transfer_time' => date('Y-m-d H:i:s'),
                    'transfer_id' => $transferId
                ],
                'message' => '转存成功'
            ];
            
        } catch (Exception $e) {
            if (isset($transferId)) {
                $this->logTransferEnd($transferId, self::STATUS_FAILED, $e->getMessage());
            }
            
            return [
                'success' => false,
                'message' => '转存失败：' . $e->getMessage(),
                'error_code' => self::ERROR_NETWORK_ERROR
            ];
        }
    }
    
    /**
     * 获取网盘文件列表
     * 
     * @param string $parentId 父目录ID
     * @param int $page 页码
     * @param int $size 每页数量
     * @return array
     */
    public function getFileList($parentId = '0', $page = 1, $size = 50) {
        try {
            $configCheck = $this->checkConfiguration();
            if (!$configCheck['success']) {
                return $configCheck;
            }
            
            $queryParams = [
                'pr' => 'ucpro',
                'fr' => 'pc',
                'uc_param_str' => '',
                'pdir_fid' => $parentId,
                '_page' => $page,
                '_size' => $size,
                '_fetch_total' => 1,
                '_fetch_sub_dirs' => 0,
                '_sort' => 'file_type:asc,updated_at:desc',
            ];
            
            $response = $this->httpClient->curlHelper(
                $this->apiBaseUrl . '/file/sort',
                'GET',
                null,
                $this->headers,
                http_build_query($queryParams)
            );
            
            $result = json_decode($response['body'], true);
            
            if ($result['status'] !== 200) {
                $message = $result['message'] === 'require login [guest]' 
                    ? '夸克网盘未登录，请检查Cookie' 
                    : $result['message'];
                    
                return [
                    'success' => false,
                    'message' => $message,
                    'error_code' => self::ERROR_AUTH_FAILED
                ];
            }
            
            return [
                'success' => true,
                'data' => [
                    'list' => $result['data']['list'],
                    'total' => $result['data']['total'] ?? 0
                ],
                'message' => '获取成功'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '获取文件列表失败：' . $e->getMessage(),
                'error_code' => self::ERROR_NETWORK_ERROR
            ];
        }
    }
    
    /**
     * 提取分享ID
     * 
     * @param string $url 分享链接
     * @return string|false
     */
    private function extractShareId($url) {
        // 夸克网盘链接格式：https://pan.quark.cn/s/xxxxxxxx
        if (preg_match('/\/s\/([a-zA-Z0-9]+)/', $url, $matches)) {
            return $matches[1];
        }
        return false;
    }
    
    /**
     * 获取stoken
     * 
     * @param string $shareId 分享ID
     * @param string $code 提取码
     * @return array
     */
    private function getStoken($shareId, $code = '') {
        $data = [
            'passcode' => $code,
            'pwd_id' => $shareId,
        ];
        
        $queryParams = [
            'pr' => 'ucpro',
            'fr' => 'pc',
            'uc_param_str' => '',
        ];
        
        $response = $this->httpClient->curlHelper(
            $this->apiBaseUrl . '/share/sharepage/token',
            'POST',
            json_encode($data),
            $this->headers,
            http_build_query($queryParams)
        );
        
        $result = json_decode($response['body'], true);
        
        if ($result['status'] !== 200) {
            return [
                'success' => false,
                'message' => $result['message'] ?? 'stoken获取失败',
                'error_code' => self::ERROR_AUTH_FAILED
            ];
        }
        
        return [
            'success' => true,
            'data' => $result['data']
        ];
    }
    
    /**
     * 获取分享详情
     * 
     * @param string $shareId 分享ID
     * @param string $stoken stoken
     * @return array
     */
    private function getShareDetail($shareId, $stoken) {
        $data = [
            'pwd_id' => $shareId,
            'stoken' => $stoken,
            'pdir_fid' => '0',
            'force' => 0,
            '_page' => 1,
            '_size' => 50,
            '_fetch_banner' => 1,
            '_fetch_share' => 1,
            '_fetch_total' => 1,
            '_sort' => 'file_type:asc,updated_at:desc',
        ];
        
        $queryParams = [
            'pr' => 'ucpro',
            'fr' => 'pc',
            'uc_param_str' => '',
        ];
        
        $response = $this->httpClient->curlHelper(
            $this->apiBaseUrl . '/share/sharepage/detail',
            'POST',
            json_encode($data),
            $this->headers,
            http_build_query($queryParams)
        );
        
        $result = json_decode($response['body'], true);
        
        if ($result['status'] !== 200) {
            return [
                'success' => false,
                'message' => $result['message'] ?? '获取分享详情失败',
                'error_code' => self::ERROR_NETWORK_ERROR
            ];
        }
        
        return [
            'success' => true,
            'data' => $result['data']
        ];
    }
    
    /**
     * 转存到网盘
     * 
     * @param string $shareId 分享ID
     * @param string $stoken stoken
     * @param array $fidList 文件ID列表
     * @param array $fidTokenList 文件token列表
     * @param string $targetDir 目标目录
     * @return array
     */
    private function saveToNetdisk($shareId, $stoken, $fidList, $fidTokenList, $targetDir = '') {
        $saveDir = $targetDir ?: $this->config->get('quark_file_path', '/SOPAN转存');
        
        $data = [
            'fid_list' => $fidList,
            'fid_token_list' => $fidTokenList,
            'to_pdir_fid' => '0',
            'pwd_id' => $shareId,
            'stoken' => $stoken,
            'pdir_fid' => '0',
            'scene' => 'link',
        ];
        
        $queryParams = [
            'pr' => 'ucpro',
            'fr' => 'pc',
            'uc_param_str' => '',
        ];
        
        $response = $this->httpClient->curlHelper(
            $this->apiBaseUrl . '/share/sharepage/save',
            'POST',
            json_encode($data),
            $this->headers,
            http_build_query($queryParams)
        );
        
        $result = json_decode($response['body'], true);
        
        if ($result['status'] !== 200) {
            $message = $result['message'] ?? '转存失败';
            if (strpos($message, 'capacity limit') !== false) {
                $message = '存储容量不足';
            }
            
            return [
                'success' => false,
                'message' => $message,
                'error_code' => self::ERROR_CAPACITY_FULL
            ];
        }
        
        // 等待转存任务完成
        $taskId = $result['data']['task_id'];
        $saveResult = $this->waitForTaskComplete($taskId);
        
        return $saveResult;
    }
    
    /**
     * 等待任务完成
     * 
     * @param string $taskId 任务ID
     * @param int $maxRetries 最大重试次数
     * @return array
     */
    private function waitForTaskComplete($taskId, $maxRetries = 50) {
        $retryCount = 0;
        
        while ($retryCount < $maxRetries) {
            $result = $this->getTaskStatus($taskId);
            
            if ($result['success'] && isset($result['data']['status']) && $result['data']['status'] == 2) {
                return [
                    'success' => true,
                    'data' => $result['data']
                ];
            }
            
            if (!$result['success']) {
                return $result;
            }
            
            $retryCount++;
            sleep(1); // 等待1秒后重试
        }
        
        return [
            'success' => false,
            'message' => '任务超时',
            'error_code' => self::ERROR_NETWORK_ERROR
        ];
    }
    
    /**
     * 获取任务状态
     * 
     * @param string $taskId 任务ID
     * @return array
     */
    private function getTaskStatus($taskId) {
        $queryParams = [
            'pr' => 'ucpro',
            'fr' => 'pc',
            'uc_param_str' => '',
            'task_id' => $taskId,
            'retry_index' => 0,
        ];
        
        $response = $this->httpClient->curlHelper(
            $this->apiBaseUrl . '/task',
            'GET',
            null,
            $this->headers,
            http_build_query($queryParams)
        );
        
        $result = json_decode($response['body'], true);
        
        if ($result['status'] !== 200) {
            return [
                'success' => false,
                'message' => $result['message'] ?? '获取任务状态失败',
                'error_code' => self::ERROR_NETWORK_ERROR
            ];
        }
        
        return [
            'success' => true,
            'data' => $result['data']
        ];
    }
    
    /**
     * 清理广告文件
     * 
     * @param array $fidList 文件ID列表
     */
    private function cleanBannedFiles($fidList) {
        $bannedKeywords = $this->config->get('quark_banned_keywords', '');
        if (empty($bannedKeywords)) {
            return;
        }
        
        $keywords = explode(',', $bannedKeywords);
        $keywords = array_map('trim', $keywords);
        $keywords = array_filter($keywords);
        
        if (empty($keywords)) {
            return;
        }
        
        foreach ($fidList as $fid) {
            $this->removeBannedFilesInDirectory($fid, $keywords);
        }
    }
    
    /**
     * 在目录中移除广告文件
     * 
     * @param string $parentId 父目录ID
     * @param array $keywords 关键词列表
     */
    private function removeBannedFilesInDirectory($parentId, $keywords) {
        try {
            $fileList = $this->getFileList($parentId);
            if (!$fileList['success']) {
                return;
            }
            
            $toDelete = [];
            foreach ($fileList['data']['list'] as $file) {
                foreach ($keywords as $keyword) {
                    if (strpos($file['file_name'], $keyword) !== false) {
                        $toDelete[] = $file['fid'];
                        break;
                    }
                }
            }
            
            if (!empty($toDelete)) {
                // 如果所有文件都要删除，则删除整个目录
                if (count($toDelete) === count($fileList['data']['list'])) {
                    $this->deleteFiles([$parentId]);
                } else {
                    $this->deleteFiles($toDelete);
                }
            }
            
        } catch (Exception $e) {
            // 忽略清理错误，不影响主流程
        }
    }
    
    /**
     * 删除文件
     * 
     * @param array $fidList 文件ID列表
     * @return array
     */
    private function deleteFiles($fidList) {
        $data = [
            'action_type' => 2,
            'filelist' => $fidList,
            'exclude_fids' => [],
        ];
        
        $queryParams = [
            'pr' => 'ucpro',
            'fr' => 'pc',
            'uc_param_str' => '',
        ];
        
        $response = $this->httpClient->curlHelper(
            $this->apiBaseUrl . '/file/delete',
            'POST',
            json_encode($data),
            $this->headers,
            http_build_query($queryParams)
        );
        
        $result = json_decode($response['body'], true);
        
        return [
            'success' => $result['status'] === 200,
            'message' => $result['message'] ?? ''
        ];
    }
    
    /**
     * 创建分享
     * 
     * @param array $fidList 文件ID列表
     * @param string $title 标题
     * @param int $expiredType 过期类型
     * @return array
     */
    private function createShare($fidList, $title, $expiredType = 1) {
        $data = [
            'fid_list' => $fidList,
            'title' => $title,
            'url_type' => 1,
            'passcode' => '',
            'expired_type' => $expiredType,
        ];
        
        $queryParams = [
            'pr' => 'ucpro',
            'fr' => 'pc',
            'uc_param_str' => '',
        ];
        
        $response = $this->httpClient->curlHelper(
            $this->apiBaseUrl . '/share',
            'POST',
            json_encode($data),
            $this->headers,
            http_build_query($queryParams)
        );
        
        $result = json_decode($response['body'], true);
        
        if ($result['status'] !== 200) {
            return [
                'success' => false,
                'message' => $result['message'] ?? '创建分享失败',
                'error_code' => self::ERROR_SHARE_FAILED
            ];
        }
        
        $taskId = $result['data']['task_id'];
        $shareResult = $this->waitForTaskComplete($taskId);
        
        if (!$shareResult['success']) {
            return $shareResult;
        }
        
        // 获取分享密码
        $shareId = $shareResult['data']['share_id'];
        $passwordResult = $this->getSharePassword($shareId);
        
        return $passwordResult;
    }
    
    /**
     * 获取分享密码
     * 
     * @param string $shareId 分享ID
     * @return array
     */
    private function getSharePassword($shareId) {
        $queryParams = [
            'pr' => 'ucpro',
            'fr' => 'pc',
            'uc_param_str' => '',
        ];
        
        $response = $this->httpClient->curlHelper(
            $this->apiBaseUrl . '/share/password/' . $shareId,
            'GET',
            null,
            $this->headers,
            http_build_query($queryParams)
        );
        
        $result = json_decode($response['body'], true);
        
        if ($result['status'] !== 200) {
            return [
                'success' => false,
                'message' => $result['message'] ?? '获取分享密码失败',
                'error_code' => self::ERROR_SHARE_FAILED
            ];
        }
        
        return [
            'success' => true,
            'data' => [
                'share_url' => 'https://pan.quark.cn/s/' . $shareId,
                'code' => $result['data']['passcode'] ?? '',
                'share_id' => $shareId
            ]
        ];
    }
    
    /**
     * 记录转存开始
     * 
     * @param string $url 原始链接
     * @param string $code 提取码
     * @return string 转存ID
     */
    private function logTransferStart($url, $code) {
        $transferId = uniqid('quark_', true);
        
        $this->db->insert('transfer_logs', [
            'id' => $transferId,
            'original_url' => $url,
            'original_code' => $code,
            'original_platform' => 'unknown',
            'target_platform' => 'quark',
            'transfer_status' => self::STATUS_PENDING,
            'user_ip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'start_time' => date('Y-m-d H:i:s')
        ]);
        
        return $transferId;
    }
    
    /**
     * 记录转存结束
     * 
     * @param string $transferId 转存ID
     * @param string $status 转存状态
     * @param string $message 消息
     */
    private function logTransferEnd($transferId, $status, $message) {
        $updateData = [
            'transfer_status' => $status,
            'end_time' => date('Y-m-d H:i:s')
        ];
        
        if ($status === self::STATUS_FAILED) {
            $updateData['error_message'] = $message;
        }
        
        // 计算耗时
        $log = $this->db->find('transfer_logs', ['id' => $transferId]);
        if ($log) {
            $startTime = strtotime($log['start_time']);
            $endTime = time();
            $updateData['duration'] = $endTime - $startTime;
        }
        
        $this->db->update('transfer_logs', $updateData, ['id' => $transferId]);
    }
}