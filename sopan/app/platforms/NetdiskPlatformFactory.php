<?php
/**
 * SOPAN 网盘平台工厂类
 * 统一管理各个网盘平台的实例化和调用
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class NetdiskPlatformFactory {
    // 平台类型常量
    const PLATFORM_QUARK = 'quark';
    const PLATFORM_BAIDU = 'baidu';
    const PLATFORM_ALIYUN = 'aliyun';
    const PLATFORM_UC = 'uc';
    const PLATFORM_XUNLEI = 'xunlei';
    
    // 平台类映射
    private static $platformClasses = [
        self::PLATFORM_QUARK => 'QuarkPlatform',
        self::PLATFORM_BAIDU => 'BaiduPlatform',
        self::PLATFORM_ALIYUN => 'AliyunPlatform',  // 待实现
        self::PLATFORM_UC => 'UcPlatform',          // 待实现
        self::PLATFORM_XUNLEI => 'XunleiPlatform',  // 待实现
    ];
    
    // 平台名称映射
    private static $platformNames = [
        self::PLATFORM_QUARK => '夸克网盘',
        self::PLATFORM_BAIDU => '百度网盘',
        self::PLATFORM_ALIYUN => '阿里云盘',
        self::PLATFORM_UC => 'UC网盘',
        self::PLATFORM_XUNLEI => '迅雷网盘',
    ];
    
    // URL域名到平台的映射
    private static $urlPlatformMap = [
        'pan.quark.cn' => self::PLATFORM_QUARK,
        'www.alipan.com' => self::PLATFORM_ALIYUN,
        'alipan.com' => self::PLATFORM_ALIYUN,
        'www.aliyundrive.com' => self::PLATFORM_ALIYUN,
        'aliyundrive.com' => self::PLATFORM_ALIYUN,
        'pan.baidu.com' => self::PLATFORM_BAIDU,
        'drive.uc.cn' => self::PLATFORM_UC,
        'fast.uc.cn' => self::PLATFORM_UC,
        'pan.xunlei.com' => self::PLATFORM_XUNLEI,
    ];
    
    // 平台实例缓存
    private static $instances = [];
    
    /**
     * 获取平台实例
     * 
     * @param string $platform 平台类型
     * @return object|null
     */
    public static function getInstance($platform) {
        if (!isset(self::$platformClasses[$platform])) {
            throw new Exception("不支持的网盘平台：{$platform}");
        }
        
        if (!isset(self::$instances[$platform])) {
            $className = self::$platformClasses[$platform];
            
            // 检查类文件是否存在
            $classFile = dirname(__DIR__) . '/platforms/' . $className . '.php';
            if (!file_exists($classFile)) {
                throw new Exception("平台类文件不存在：{$classFile}");
            }
            
            // 加载并实例化平台类
            require_once $classFile;
            if (!class_exists($className)) {
                throw new Exception("平台类不存在：{$className}");
            }
            
            self::$instances[$platform] = new $className();
        }
        
        return self::$instances[$platform];
    }
    
    /**
     * 根据URL自动检测平台类型
     * 
     * @param string $url 分享链接
     * @return string|null
     */
    public static function detectPlatform($url) {
        foreach (self::$urlPlatformMap as $domain => $platform) {
            if (strpos($url, $domain) !== false) {
                return $platform;
            }
        }
        return null;
    }
    
    /**
     * 获取所有支持的平台列表
     * 
     * @return array
     */
    public static function getSupportedPlatforms() {
        return array_keys(self::$platformClasses);
    }
    
    /**
     * 获取平台名称
     * 
     * @param string $platform 平台类型
     * @return string
     */
    public static function getPlatformName($platform) {
        return self::$platformNames[$platform] ?? $platform;
    }
    
    /**
     * 获取所有平台名称映射
     * 
     * @return array
     */
    public static function getAllPlatformNames() {
        return self::$platformNames;
    }
    
    /**
     * 检查平台是否支持
     * 
     * @param string $platform 平台类型
     * @return bool
     */
    public static function isSupported($platform) {
        return isset(self::$platformClasses[$platform]);
    }
    
    /**
     * 获取平台状态信息
     * 
     * @param string $platform 平台类型
     * @return array
     */
    public static function getPlatformStatus($platform) {
        try {
            $instance = self::getInstance($platform);
            
            if (method_exists($instance, 'checkConfiguration')) {
                $config = $instance->checkConfiguration();
                return [
                    'platform' => $platform,
                    'name' => self::getPlatformName($platform),
                    'enabled' => $config['success'],
                    'message' => $config['message'] ?? '',
                    'error_code' => $config['error_code'] ?? null
                ];
            }
            
            return [
                'platform' => $platform,
                'name' => self::getPlatformName($platform),
                'enabled' => true,
                'message' => '平台可用'
            ];
            
        } catch (Exception $e) {
            return [
                'platform' => $platform,
                'name' => self::getPlatformName($platform),
                'enabled' => false,
                'message' => $e->getMessage(),
                'error_code' => 500
            ];
        }
    }
    
    /**
     * 获取所有平台状态
     * 
     * @return array
     */
    public static function getAllPlatformStatus() {
        $status = [];
        foreach (self::getSupportedPlatforms() as $platform) {
            $status[$platform] = self::getPlatformStatus($platform);
        }
        return $status;
    }
    
    /**
     * 批量转存到多个平台
     * 
     * @param string $url 原始链接
     * @param string $code 提取码
     * @param array $targetPlatforms 目标平台列表
     * @param array $options 转存选项
     * @return array
     */
    public static function batchTransfer($url, $code, $targetPlatforms, $options = []) {
        $results = [];
        $sourcePlatform = self::detectPlatform($url);
        
        if (!$sourcePlatform) {
            return [
                'success' => false,
                'message' => '无法识别源平台类型',
                'results' => []
            ];
        }
        
        foreach ($targetPlatforms as $platform) {
            try {
                // 跳过源平台
                if ($platform === $sourcePlatform) {
                    $results[$platform] = [
                        'success' => false,
                        'message' => '不能转存到相同平台',
                        'platform' => $platform
                    ];
                    continue;
                }
                
                $instance = self::getInstance($platform);
                
                if (method_exists($instance, 'transferLink')) {
                    $result = $instance->transferLink(
                        $url,
                        $code,
                        $options['title'] ?? '',
                        $options['expired_type'] ?? 1,
                        $options['target_dir'] ?? ''
                    );
                    
                    $results[$platform] = array_merge($result, ['platform' => $platform]);
                } else {
                    $results[$platform] = [
                        'success' => false,
                        'message' => '平台不支持转存功能',
                        'platform' => $platform
                    ];
                }
                
            } catch (Exception $e) {
                $results[$platform] = [
                    'success' => false,
                    'message' => $e->getMessage(),
                    'platform' => $platform
                ];
            }
        }
        
        $successCount = count(array_filter($results, function($result) {
            return $result['success'];
        }));
        
        return [
            'success' => $successCount > 0,
            'message' => "成功转存到 {$successCount} 个平台",
            'total_platforms' => count($targetPlatforms),
            'success_count' => $successCount,
            'results' => $results
        ];
    }
    
    /**
     * 验证链接格式
     * 
     * @param string $url 链接
     * @return array
     */
    public static function validateUrl($url) {
        $platform = self::detectPlatform($url);
        
        if (!$platform) {
            return [
                'success' => false,
                'message' => '不支持的网盘链接格式',
                'platform' => null
            ];
        }
        
        try {
            $instance = self::getInstance($platform);
            
            if (method_exists($instance, 'validateLink')) {
                $result = $instance->validateLink($url);
                return array_merge($result, ['platform' => $platform]);
            }
            
            return [
                'success' => true,
                'message' => '链接格式正确',
                'platform' => $platform
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'platform' => $platform
            ];
        }
    }
    
    /**
     * 清理实例缓存
     */
    public static function clearInstances() {
        self::$instances = [];
    }
}