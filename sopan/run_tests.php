#!/usr/bin/env php
<?php
/**
 * SOPAN 测试运行脚本
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 获取项目根目录
$rootDir = dirname(__DIR__);

// 加载测试框架
require_once $rootDir . '/tests/TestRunner.php';

echo "SOPAN 项目测试套件\n";
echo "==================\n\n";

// 创建测试运行器
$runner = new TestRunner();

// 检查是否指定了特定的测试类
if ($argc > 1) {
    $testClass = $argv[1];
    if (class_exists($testClass)) {
        $runner->addTestClass($testClass);
    } else {
        echo "错误：找不到测试类 '{$testClass}'\n";
        exit(1);
    }
} else {
    // 自动发现所有测试类
    $testDirs = [
        $rootDir . '/tests/unit/',
        $rootDir . '/tests/integration/'
    ];
    
    foreach ($testDirs as $dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '*Test.php');
            foreach ($files as $file) {
                $className = basename($file, '.php');
                require_once $file;
                if (class_exists($className)) {
                    $runner->addTestClass($className);
                }
            }
        }
    }
}

// 运行测试
try {
    $runner->runAll();
} catch (Exception $e) {
    echo "测试运行失败：" . $e->getMessage() . "\n";
    exit(1);
}

echo "\n测试完成！\n";