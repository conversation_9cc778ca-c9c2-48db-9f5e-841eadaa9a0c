<?php
/**
 * SOPAN 项目安装程序
 * 一键安装和配置系统
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

// 开启错误显示（仅用于安装调试）
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 创建错误日志函数
function logError($message, $context = []) {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        @mkdir($logDir, 0755, true);
    }
    
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context,
        'request' => $_REQUEST ?? [],
        'server' => [
            'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? '',
            'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? '',
            'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]
    ];
    
    $logFile = $logDir . '/install_' . date('Y-m-d') . '.log';
    @file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
}

try {
    session_start();
} catch (Exception $e) {
    logError('Session启动失败', ['error' => $e->getMessage()]);
    // 忽略session错误，但记录日志
}

// 记录访问日志
logError('安装程序访问', ['action' => 'page_load']);

// 检查是否已安装
if (file_exists(__DIR__ . '/config/installed.lock')) {
    logError('系统已安装，重定向到首页');
    header('Location: /');
    exit('系统已安装，如需重新安装请删除 config/installed.lock 文件');
}

// 处理安装请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    $action = $_POST['action'];
    logError('处理安装请求', ['action' => $action, 'post_data' => $_POST]);
    
    try {
        if ($action === 'install') {
            logError('开始执行安装流程');
            handleInstallation();
        } elseif ($action === 'test_db') {
            logError('测试数据库连接');
            testDatabaseConnection();
        } else {
            throw new Exception('未知的操作类型: ' . $action);
        }
    } catch (Exception $e) {
        logError('安装过程出错', [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
        
        echo json_encode([
            'success' => false,
            'message' => '操作失败：' . $e->getMessage(),
            'debug' => [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    } catch (Error $e) {
        logError('PHP致命错误', [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
        
        echo json_encode([
            'success' => false,
            'message' => 'PHP错误：' . $e->getMessage(),
            'debug' => [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

/**
 * 处理安装流程
 */
function handleInstallation() {
    try {
        // 验证输入
        $config = validateInput();
        
        // 测试数据库连接
        testDatabase($config['database']);
        
        // 创建配置文件
        createConfigFile($config);
        
        // 导入数据库
        importDatabase($config['database']);
        
        // 创建管理员账户
        createAdminUser($config['admin'], $config['database']);
        
        // 设置权限
        setPermissions();
        
        // 创建安装锁文件
        createInstallLock();
        
        echo json_encode([
            'success' => true,
            'message' => '安装成功！正在跳转到首页...',
            'redirect' => '/'
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '安装失败：' . $e->getMessage(),
            'debug' => [
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]
        ], JSON_UNESCAPED_UNICODE);
    }
    exit;
}

/**
 * 测试数据库连接
 */
function testDatabaseConnection() {
    try {
        $host = $_POST['db_host'] ?? 'localhost';
        $port = $_POST['db_port'] ?? 3306;
        $username = $_POST['db_username'] ?? '';
        $password = $_POST['db_password'] ?? '';
        $database = $_POST['db_database'] ?? '';
        
        $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // 检查数据库是否存在，不存在则创建
        $stmt = $pdo->query("SHOW DATABASES LIKE '{$database}'");
        if ($stmt->rowCount() === 0) {
            $pdo->exec("CREATE DATABASE `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        }
        
        echo json_encode([
            'success' => true,
            'message' => '数据库连接成功'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '数据库连接失败：' . $e->getMessage()
        ]);
    }
    exit;
}

/**
 * 验证输入数据
 */
function validateInput() {
    $config = [
        'app' => [
            'name' => trim($_POST['site_name'] ?? 'SOPAN搜索'),
            'url' => trim($_POST['site_url'] ?? ''),
            'environment' => 'production',
            'debug' => false,
            'timezone' => 'Asia/Shanghai'
        ],
        'database' => [
            'host' => trim($_POST['db_host'] ?? 'localhost'),
            'port' => intval($_POST['db_port'] ?? 3306),
            'username' => trim($_POST['db_username'] ?? ''),
            'password' => trim($_POST['db_password'] ?? ''),
            'database' => trim($_POST['db_database'] ?? ''),
            'charset' => 'utf8mb4'
        ],
        'search' => [
            'panseeker_api_url' => trim($_POST['api_url'] ?? ''),
            'api_key' => trim($_POST['api_key'] ?? ''),
            'timeout' => 30,
            'max_results' => 100
        ],
        'admin' => [
            'username' => trim($_POST['admin_username'] ?? 'admin'),
            'password' => trim($_POST['admin_password'] ?? ''),
            'email' => trim($_POST['admin_email'] ?? '')
        ]
    ];
    
    // 验证必填项
    if (empty($config['database']['username'])) {
        throw new Exception('请填写数据库用户名');
    }
    
    if (empty($config['database']['database'])) {
        throw new Exception('请填写数据库名称');
    }
    
    if (empty($config['admin']['password'])) {
        throw new Exception('请设置管理员密码');
    }
    
    if (strlen($config['admin']['password']) < 6) {
        throw new Exception('管理员密码长度不能少于6位');
    }
    
    return $config;
}

/**
 * 测试数据库连接
 */
function testDatabase($dbConfig) {
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
}

/**
 * 创建配置文件
 */
function createConfigFile($config) {
    $configDir = __DIR__ . '/config';
    if (!is_dir($configDir)) {
        mkdir($configDir, 0755, true);
    }
    
    // 创建应用配置文件
    $appConfig = [
        'app_name' => $config['app']['name'],
        'app_version' => '1.0.0',
        'app_url' => $config['app']['url'],
        'app_env' => $config['app']['environment'],
        'app_debug' => $config['app']['debug'],
        'app_timezone' => $config['app']['timezone'],
        
        'cache_enabled' => true,
        'cache_expire' => 3600,
        
        'log_enabled' => true,
        'log_level' => 'info',
        'log_max_files' => 30,
        
        'session_name' => 'SOPAN_SESSION',
        'session_expire' => 7200,
        
        'upload_max_size' => '10M',
        'upload_allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf'],
        
        'pagination_size' => 20,
        'search_cache_time' => 300,
        'api_timeout' => $config['search']['timeout']
    ];
    
    $appConfigContent = "<?php\n";
    $appConfigContent .= "/**\n";
    $appConfigContent .= " * SOPAN 应用配置文件\n";
    $appConfigContent .= " * 安装时间：" . date('Y-m-d H:i:s') . "\n";
    $appConfigContent .= " */\n\n";
    $appConfigContent .= "return " . var_export($appConfig, true) . ";\n";
    
    if (!file_put_contents($configDir . '/app.php', $appConfigContent)) {
        throw new Exception('无法创建应用配置文件，请检查目录权限');
    }
    
    // 创建数据库配置文件
    $dbConfig = [
        'host' => $config['database']['host'],
        'port' => $config['database']['port'],
        'database' => $config['database']['database'],
        'username' => $config['database']['username'],
        'password' => $config['database']['password'],
        'charset' => $config['database']['charset'],
        'collation' => 'utf8mb4_unicode_ci',
        'options' => [
            'PDO::ATTR_ERRMODE' => 'PDO::ERRMODE_EXCEPTION',
            'PDO::ATTR_DEFAULT_FETCH_MODE' => 'PDO::FETCH_ASSOC',
            'PDO::ATTR_EMULATE_PREPARES' => false,
            'PDO::MYSQL_ATTR_INIT_COMMAND' => '"SET NAMES utf8mb4"'
        ]
    ];
    
    $dbConfigContent = "<?php\n";
    $dbConfigContent .= "/**\n";
    $dbConfigContent .= " * SOPAN 数据库配置文件\n";
    $dbConfigContent .= " * 安装时间：" . date('Y-m-d H:i:s') . "\n";
    $dbConfigContent .= " */\n\n";
    $dbConfigContent .= "return [\n";
    $dbConfigContent .= "    'host' => '{$dbConfig['host']}',\n";
    $dbConfigContent .= "    'port' => {$dbConfig['port']},\n";
    $dbConfigContent .= "    'database' => '{$dbConfig['database']}',\n";
    $dbConfigContent .= "    'username' => '{$dbConfig['username']}',\n";
    $dbConfigContent .= "    'password' => '{$dbConfig['password']}',\n";
    $dbConfigContent .= "    'charset' => '{$dbConfig['charset']}',\n";
    $dbConfigContent .= "    'collation' => '{$dbConfig['collation']}',\n";
    $dbConfigContent .= "    'options' => [\n";
    $dbConfigContent .= "        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n";
    $dbConfigContent .= "        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,\n";
    $dbConfigContent .= "        PDO::ATTR_EMULATE_PREPARES => false,\n";
    $dbConfigContent .= "        PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4\"\n";
    $dbConfigContent .= "    ]\n";
    $dbConfigContent .= "];\n";
    
    if (!file_put_contents($configDir . '/database.php', $dbConfigContent)) {
        throw new Exception('无法创建数据库配置文件，请检查目录权限');
    }
    
    // 创建搜索配置文件
    $searchConfig = [
        'panseeker_api_url' => $config['search']['panseeker_api_url'],
        'api_key' => $config['search']['api_key'],
        'timeout' => $config['search']['timeout'],
        'max_results' => $config['search']['max_results'],
        'enable_cache' => true,
        'cache_time' => 300
    ];
    
    $searchConfigContent = "<?php\n";
    $searchConfigContent .= "/**\n";
    $searchConfigContent .= " * SOPAN 搜索配置文件\n";
    $searchConfigContent .= " * 安装时间：" . date('Y-m-d H:i:s') . "\n";
    $searchConfigContent .= " */\n\n";
    $searchConfigContent .= "return " . var_export($searchConfig, true) . ";\n";
    
    if (!file_put_contents($configDir . '/search.php', $searchConfigContent)) {
        throw new Exception('无法创建搜索配置文件，请检查目录权限');
    }
}

/**
 * 导入数据库
 */
function importDatabase($dbConfig) {
    $sqlFile = __DIR__ . '/database/sopan.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception('数据库SQL文件不存在：' . $sqlFile);
    }
    
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    // 读取SQL文件
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception('无法读取SQL文件');
    }
    
    // 分割SQL语句（按分号分割）
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    // 执行每个SQL语句
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                // 忽略“表已存在”等错误
                if (strpos($e->getMessage(), 'already exists') === false && 
                    strpos($e->getMessage(), 'Duplicate entry') === false) {
                    throw new Exception('SQL执行错误：' . $e->getMessage() . '\nSQL: ' . substr($statement, 0, 100));
                }
            }
        }
    }
}

/**
 * 创建管理员账户
 */
function createAdminUser($adminConfig, $dbConfig) {
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    // 检查管理员表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
    if ($stmt->rowCount() === 0) {
        throw new Exception('管理员表不存在，请检查数据库初始化');
    }
    
    // 删除默认管理员（如果存在）
    $pdo->exec("DELETE FROM admins WHERE username = 'admin'");
    
    // 插入新管理员
    $stmt = $pdo->prepare("
        INSERT INTO admins (username, password, email, nickname, role, status) 
        VALUES (?, ?, ?, ?, 'super_admin', 1)
    ");
    
    $hashedPassword = password_hash($adminConfig['password'], PASSWORD_DEFAULT);
    $nickname = $adminConfig['username'] . '（管理员）';
    
    $stmt->execute([
        $adminConfig['username'],
        $hashedPassword,
        $adminConfig['email'],
        $nickname
    ]);
    
    // 验证插入成功
    if ($pdo->lastInsertId() === false) {
        throw new Exception('创建管理员账户失败');
    }
}

/**
 * 设置权限
 */
function setPermissions() {
    $dirs = ['storage', 'logs', 'config'];
    
    foreach ($dirs as $dir) {
        $dirPath = __DIR__ . '/' . $dir;
        if (!is_dir($dirPath)) {
            mkdir($dirPath, 0755, true);
        }
        chmod($dirPath, 0755);
    }
}

/**
 * 创建安装锁文件
 */
function createInstallLock() {
    $lockData = [
        'installed_at' => date('Y-m-d H:i:s'),
        'version' => '1.0.0',
        'installer_ip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
    ];
    
    file_put_contents(__DIR__ . '/config/installed.lock', json_encode($lockData));
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOPAN 系统安装</title>
    <script src="./public/assets/js/tailwind.min.js"></script>
    <style>
        .install-step {
            display: none;
        }
        .install-step.active {
            display: block;
        }
        .loading {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-2xl w-full mx-4">
        <!-- 调试信息显示区域 -->
        <div id="debug-info" class="bg-red-50 border-l-4 border-red-400 p-4 mb-6 hidden">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">安装错误</h3>
                    <div class="mt-2 text-sm text-red-700" id="debug-message"></div>
                    <div class="mt-2">
                        <button onclick="toggleDebugDetails()" class="text-sm underline text-red-800">查看详细信息</button>
                        <div id="debug-details" class="mt-2 text-xs bg-red-100 p-2 rounded hidden overflow-x-auto">
                            <pre id="debug-details-content"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-8">
            <!-- 安装头部 -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">SOPAN 系统安装</h1>
                <p class="text-gray-600">欢迎使用 SOPAN 网盘搜索系统，请按照向导完成安装</p>
            </div>

            <!-- 进度条 -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-600">安装进度</span>
                    <span class="text-sm text-gray-600" id="progress-text">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" id="progress-bar" style="width: 0%"></div>
                </div>
            </div>

            <!-- 安装表单 -->
            <form id="installForm" class="space-y-6">
                <!-- 步骤1：环境检查 -->
                <div class="install-step active" id="step-1">
                    <h2 class="text-xl font-semibold mb-4">步骤 1：环境检查</h2>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span>PHP 版本 ≥ 7.4</span>
                            <span class="text-green-600">✓ <?= PHP_VERSION ?></span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span>PDO MySQL 扩展</span>
                            <span class="<?= extension_loaded('pdo_mysql') ? 'text-green-600' : 'text-red-600' ?>">
                                <?= extension_loaded('pdo_mysql') ? '✓ 已安装' : '✗ 未安装' ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span>JSON 扩展</span>
                            <span class="<?= extension_loaded('json') ? 'text-green-600' : 'text-red-600' ?>">
                                <?= extension_loaded('json') ? '✓ 已安装' : '✗ 未安装' ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span>目录写入权限</span>
                            <span class="<?= is_writable(__DIR__) ? 'text-green-600' : 'text-red-600' ?>">
                                <?= is_writable(__DIR__) ? '✓ 可写' : '✗ 不可写' ?>
                            </span>
                        </div>
                    </div>
                    <div class="mt-6">
                        <button type="button" onclick="nextStep(2)" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                            下一步
                        </button>
                    </div>
                </div>

                <!-- 步骤2：网站配置 -->
                <div class="install-step" id="step-2">
                    <h2 class="text-xl font-semibold mb-4">步骤 2：网站配置</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">网站名称</label>
                            <input type="text" name="site_name" value="SOPAN搜索" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">网站地址</label>
                            <input type="url" name="site_url" placeholder="https://your-domain.com" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">搜索API地址</label>
                            <input type="url" name="api_url" placeholder="http://your-panseeker-api.com" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">API密钥</label>
                            <input type="text" name="api_key" placeholder="your_api_key" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    <div class="mt-6 flex space-x-4">
                        <button type="button" onclick="prevStep(1)" class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
                            上一步
                        </button>
                        <button type="button" onclick="nextStep(3)" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                            下一步
                        </button>
                    </div>
                </div>

                <!-- 步骤3：数据库配置 -->
                <div class="install-step" id="step-3">
                    <h2 class="text-xl font-semibold mb-4">步骤 3：数据库配置</h2>
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">数据库主机</label>
                                <input type="text" name="db_host" value="localhost" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">端口</label>
                                <input type="number" name="db_port" value="3306" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">数据库名称</label>
                            <input type="text" name="db_database" placeholder="sopan" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                            <input type="text" name="db_username" placeholder="数据库用户名" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                            <input type="password" name="db_password" placeholder="数据库密码" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <button type="button" onclick="testDatabase()" 
                                    class="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700" id="test-db-btn">
                                测试数据库连接
                            </button>
                        </div>
                    </div>
                    <div class="mt-6 flex space-x-4">
                        <button type="button" onclick="prevStep(2)" class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
                            上一步
                        </button>
                        <button type="button" onclick="nextStep(4)" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                            下一步
                        </button>
                    </div>
                </div>

                <!-- 步骤4：管理员账户 -->
                <div class="install-step" id="step-4">
                    <h2 class="text-xl font-semibold mb-4">步骤 4：管理员账户</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">管理员用户名</label>
                            <input type="text" name="admin_username" value="admin" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">管理员密码</label>
                            <input type="password" name="admin_password" placeholder="至少6位字符" required minlength="6"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">管理员邮箱</label>
                            <input type="email" name="admin_email" placeholder="<EMAIL>" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    <div class="mt-6 flex space-x-4">
                        <button type="button" onclick="prevStep(3)" class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
                            上一步
                        </button>
                        <button type="button" onclick="startInstall()" class="flex-1 bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700" id="install-btn">
                            开始安装
                        </button>
                    </div>
                </div>

                <!-- 步骤5：安装完成 -->
                <div class="install-step" id="step-5">
                    <div class="text-center">
                        <div class="mb-4">
                            <div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">安装成功！</h2>
                        <p class="text-gray-600 mb-6">SOPAN 系统已成功安装，您现在可以开始使用了。</p>
                        <a href="/" class="bg-blue-600 text-white py-2 px-6 rounded hover:bg-blue-700">
                            进入系统
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentStep = 1;
        const totalSteps = 5;

        function updateProgress() {
            const progress = (currentStep - 1) / (totalSteps - 1) * 100;
            document.getElementById('progress-bar').style.width = progress + '%';
            document.getElementById('progress-text').textContent = Math.round(progress) + '%';
        }

        function showStep(step) {
            document.querySelectorAll('.install-step').forEach(el => el.classList.remove('active'));
            document.getElementById('step-' + step).classList.add('active');
            currentStep = step;
            updateProgress();
        }

        function nextStep(step) {
            showStep(step);
        }

        function prevStep(step) {
            showStep(step);
        }

        function toggleDebugDetails() {
            const details = document.getElementById('debug-details');
            details.classList.toggle('hidden');
        }
        
        function showError(message, debugInfo = null) {
            const errorDiv = document.getElementById('debug-info');
            const messageDiv = document.getElementById('debug-message');
            const detailsDiv = document.getElementById('debug-details-content');
            
            messageDiv.textContent = message;
            errorDiv.classList.remove('hidden');
            
            if (debugInfo) {
                detailsDiv.textContent = JSON.stringify(debugInfo, null, 2);
            }
        }
        
        function hideError() {
            document.getElementById('debug-info').classList.add('hidden');
        }
        
        function testDatabase() {
            hideError();
            const btn = document.getElementById('test-db-btn');
            const originalText = btn.textContent;
            btn.innerHTML = '<div class="loading inline-block mr-2"></div>测试中...';
            btn.disabled = true;

            const formData = new FormData(document.getElementById('installForm'));
            formData.append('action', 'test_db');

            fetch('install.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('服务器响应错误: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    btn.innerHTML = '✓ ' + data.message;
                    btn.className = 'w-full bg-green-600 text-white py-2 px-4 rounded';
                } else {
                    btn.innerHTML = '✗ ' + data.message;
                    btn.className = 'w-full bg-red-600 text-white py-2 px-4 rounded';
                    showError(data.message, data.debug);
                }
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.className = 'w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700';
                    btn.disabled = false;
                }, 3000);
            })
            .catch(error => {
                console.error('数据库测试错误:', error);
                btn.innerHTML = '✗ 连接失败';
                btn.className = 'w-full bg-red-600 text-white py-2 px-4 rounded';
                showError('连接失败: ' + error.message);
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.className = 'w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700';
                    btn.disabled = false;
                }, 3000);
            });
        }

        function startInstall() {
            hideError();
            const btn = document.getElementById('install-btn');
            btn.innerHTML = '<div class="loading inline-block mr-2"></div>安装中...';
            btn.disabled = true;

            const formData = new FormData(document.getElementById('installForm'));
            formData.append('action', 'install');

            fetch('install.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('服务器响应错误: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showStep(5);
                    if (data.redirect) {
                        setTimeout(() => {
                            window.location.href = data.redirect;
                        }, 2000);
                    }
                } else {
                    showError(data.message, data.debug);
                    btn.textContent = '重新安装';
                    btn.disabled = false;
                }
            })
            .catch(error => {
                console.error('安装错误:', error);
                showError('安装过程中发生错误: ' + error.message);
                btn.textContent = '重新安装';
                btn.disabled = false;
            });
        }

        // 初始化进度条
        updateProgress();
    </script>
</body>
</html>