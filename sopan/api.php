<?php
/**
 * SOPAN API 入口文件
 * 所有API请求的统一入口点
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

// 设置错误报告级别
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
ini_set('display_errors', 0);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 设置内存限制
ini_set('memory_limit', '256M');

// 设置执行时间限制
ini_set('max_execution_time', 300); // 5分钟，用于SSE流式响应

// 设置输出缓冲
if (ob_get_level()) {
    ob_end_clean();
}

try {
    // 加载引导文件
    require_once __DIR__ . '/bootstrap.php';
    
    // 检查请求路径（支持从 index.php 传递的路径）
    $requestUri = '';
    
    // 优先从环境变量获取（由 index.php 设置）
    if (isset($_SERVER['API_REQUEST_URI'])) {
        $requestUri = $_SERVER['API_REQUEST_URI'];
    } else {
        // 备用方式：直接访问 api.php 时的处理
        $requestUri = $_SERVER['REQUEST_URI'] ?? '/';
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        
        // 移除脚本名称获取实际路径
        $basePath = dirname($scriptName);
        if ($basePath !== '/') {
            $requestUri = substr($requestUri, strlen($basePath));
        }
        
        // 移除查询参数
        $requestUri = strtok($requestUri, '?');
    }
    
    // 检查是否为API请求
    if (!preg_match('#^/api/#', $requestUri)) {
        // 非API请求返回404
        http_response_code(404);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => false,
            'code' => 404,
            'message' => '请求路径错误，API路径应以 /api/ 开头',
            'example' => '/api/health',
            'timestamp' => time()
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 记录访问日志
    logApiAccess($requestUri);
    
    // 加载API路由
    require_once __DIR__ . '/routes/api.php';
    
} catch (Exception $e) {
    // 捕获所有异常
    http_response_code(500);
    header('Content-Type: application/json; charset=utf-8');
    
    // 记录错误日志
    error_log('API Fatal Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
    
    echo json_encode([
        'success' => false,
        'code' => 500,
        'message' => '系统错误',
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Error $e) {
    // 捕获PHP7+的Error
    http_response_code(500);
    header('Content-Type: application/json; charset=utf-8');
    
    // 记录错误日志
    error_log('API Fatal Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
    
    echo json_encode([
        'success' => false,
        'code' => 500,
        'message' => '系统错误',
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 记录API访问日志
 * 
 * @param string $requestUri 请求URI
 */
function logApiAccess($requestUri) {
    try {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'GET',
            'uri' => $requestUri,
            'ip' => getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'referer' => $_SERVER['HTTP_REFERER'] ?? ''
        ];
        
        $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        
        // 写入日志文件
        $logDir = __DIR__ . '/storage/logs';
        if (!is_dir($logDir)) {
            @mkdir($logDir, 0755, true);
        }
        
        $logFile = $logDir . '/api_access_' . date('Y-m-d') . '.log';
        @file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
        
    } catch (Exception $e) {
        // 忽略日志记录错误
    }
}

/**
 * 获取客户端IP
 * 
 * @return string
 */
function getClientIp() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            return trim($ips[0]);
        }
    }
    
    return '127.0.0.1';
}