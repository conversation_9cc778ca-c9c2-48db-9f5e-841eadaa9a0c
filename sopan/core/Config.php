<?php
/**
 * SOPAN 配置管理类
 * 支持文件配置和数据库配置，提供缓存机制
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class Config {
    private static $instance = null;
    private static $config = [];
    private static $loaded = false;
    private static $cache = [];
    private static $cacheEnabled = true;
    private static $cacheExpire = 3600; // 1小时
    private static $configService = null;
    
    /**
     * 获取单例实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 加载配置
     * 
     * @param string $file 配置文件名
     * @return bool
     */
    public static function load($file = null) {
        if (self::$loaded && $file === null) {
            return true;
        }
        
        try {
            // 加载文件配置
            if ($file) {
                self::loadFromFile($file);
            } else {
                self::loadDefaultFiles();
            }
            
            // 加载数据库配置
            self::loadFromDatabase();
            
            self::$loaded = true;
            return true;
            
        } catch (Exception $e) {
            error_log('Config load failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 从文件加载配置
     * 
     * @param string $file 文件名
     */
    private static function loadFromFile($file) {
        $configPath = dirname(__DIR__) . '/config/' . $file . '.php';
        
        if (!file_exists($configPath)) {
            throw new Exception("Config file not found: {$configPath}");
        }
        
        $fileConfig = require $configPath;
        
        if (!is_array($fileConfig)) {
            throw new Exception("Config file must return an array: {$configPath}");
        }
        
        self::$config = array_merge(self::$config, $fileConfig);
    }
    
    /**
     * 加载默认配置文件
     */
    private static function loadDefaultFiles() {
        $configDir = dirname(__DIR__) . '/config/';
        $defaultFiles = ['app', 'database'];
        
        foreach ($defaultFiles as $file) {
            $configPath = $configDir . $file . '.php';
            if (file_exists($configPath)) {
                $fileConfig = require $configPath;
                if (is_array($fileConfig)) {
                    self::$config = array_merge(self::$config, $fileConfig);
                }
            }
        }
    }
    
    /**
     * 从数据库加载配置
     */
    public static function loadFromDatabase() {
        try {
            // 检查缓存
            if (self::$cacheEnabled) {
                $cacheKey = 'config_database';
                $cached = self::getFromCache($cacheKey);
                if ($cached !== null) {
                    self::$config = array_merge(self::$config, $cached);
                    return;
                }
            }
            
            $db = Database::getInstance();
            $configs = $db->findAll('site_config', ['status' => 1]);
            
            $dbConfig = [];
            foreach ($configs as $config) {
                $dbConfig[$config['config_key']] = $config['config_value'];
            }
            
            // 缓存数据库配置
            if (self::$cacheEnabled) {
                self::saveToCache($cacheKey, $dbConfig);
            }
            
            self::$config = array_merge(self::$config, $dbConfig);
            
        } catch (Exception $e) {
            // 数据库配置加载失败时不抛出异常，使用默认配置
            error_log('Database config load failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取配置值
     * 
     * @param string $key 配置键名（支持点分隔符）
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get($key, $default = null) {
        if (!self::$loaded) {
            self::load();
        }
        
        // 支持点分隔符访问多维数组
        if (strpos($key, '.') !== false) {
            $keys = explode('.', $key);
            $value = self::$config;
            
            foreach ($keys as $k) {
                if (is_array($value) && isset($value[$k])) {
                    $value = $value[$k];
                } else {
                    return $default;
                }
            }
            
            return $value;
        }
        
        return isset(self::$config[$key]) ? self::$config[$key] : $default;
    }
    
    /**
     * 设置配置值
     * 
     * @param string $key 配置键名
     * @param mixed $value 配置值
     * @param bool $saveToDatabase 是否保存到数据库
     * @return bool
     */
    public static function set($key, $value, $saveToDatabase = false) {
        // 支持点分隔符设置多维数组
        if (strpos($key, '.') !== false) {
            $keys = explode('.', $key);
            $config = &self::$config;
            
            for ($i = 0; $i < count($keys) - 1; $i++) {
                $k = $keys[$i];
                if (!isset($config[$k]) || !is_array($config[$k])) {
                    $config[$k] = [];
                }
                $config = &$config[$k];
            }
            
            $config[end($keys)] = $value;
        } else {
            self::$config[$key] = $value;
        }
        
        // 保存到数据库
        if ($saveToDatabase) {
            return self::saveToDatabase($key, $value);
        }
        
        // 清除相关缓存
        if (self::$cacheEnabled) {
            self::clearCache('config_database');
        }
        
        return true;
    }
    
    /**
     * 获取后台访问路径
     * 
     * @return string
     */
    public static function getAdminPath() {
        return self::get('admin_path', 'admin');
    }
    
    /**
     * 设置后台访问路径
     * 
     * @param string $path 后台路径
     * @return bool
     */
    public static function setAdminPath($path) {
        // 验证路径格式
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $path)) {
            throw new InvalidArgumentException('后台路径只能包含字母、数字、下划线和中横线');
        }
        
        return self::set('admin_path', $path, true);
    }
    
    /**
     * 检查后台路径是否可用
     * 
     * @param string $path 要检查的路径
     * @return bool
     */
    public static function isValidAdminPath($path) {
        // 检查格式
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $path)) {
            return false;
        }
        
        // 检查是否与系统保留路径冲突
        $reservedPaths = [
            'api', 'install', 'qrcode', 'public', 'assets', 
            'search', 'about', 'help', 'contact', 'terms', 
            'privacy', 'sitemap', 'test_env', 'debug_install', 
            'check_install'
        ];
        
        return !in_array($path, $reservedPaths);
    }
    
    /**
     * 检查配置是否存在
     * 
     * @param string $key 配置键名
     * @return bool
     */
    public static function has($key) {
        if (!self::$loaded) {
            self::load();
        }
        
        if (strpos($key, '.') !== false) {
            $keys = explode('.', $key);
            $value = self::$config;
            
            foreach ($keys as $k) {
                if (is_array($value) && isset($value[$k])) {
                    $value = $value[$k];
                } else {
                    return false;
                }
            }
            
            return true;
        }
        
        return isset(self::$config[$key]);
    }
    
    /**
     * 获取所有配置
     * 
     * @return array
     */
    public static function all() {
        if (!self::$loaded) {
            self::load();
        }
        
        return self::$config;
    }
    
    /**
     * 保存配置到数据库
     * 
     * @param string $key 配置键名
     * @param mixed $value 配置值
     * @return bool
     */
    private static function saveToDatabase($key, $value) {
        try {
            $db = Database::getInstance();
            
            // 检查配置是否存在
            $existing = $db->find('site_config', ['config_key' => $key]);
            
            if ($existing) {
                // 更新现有配置
                $result = $db->update('site_config', [
                    'config_value' => is_array($value) ? json_encode($value) : $value,
                    'updated_time' => date('Y-m-d H:i:s')
                ], ['config_key' => $key]);
            } else {
                // 插入新配置
                $result = $db->insert('site_config', [
                    'config_key' => $key,
                    'config_value' => is_array($value) ? json_encode($value) : $value,
                    'config_desc' => '',
                    'config_type' => 'text',
                    'config_group' => 'custom',
                    'is_system' => 0,
                    'status' => 1
                ]);
            }
            
            // 清除缓存
            if (self::$cacheEnabled) {
                self::clearCache('config_database');
            }
            
            return $result !== false;
            
        } catch (Exception $e) {
            error_log('Config save to database failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 从缓存获取数据
     * 
     * @param string $key 缓存键名
     * @return mixed|null
     */
    private static function getFromCache($key) {
        if (!self::$cacheEnabled) {
            return null;
        }
        
        $cacheFile = self::getCacheFile($key);
        
        if (!file_exists($cacheFile)) {
            return null;
        }
        
        $cacheData = @unserialize(file_get_contents($cacheFile));
        
        if (!$cacheData || !isset($cacheData['expire']) || $cacheData['expire'] < time()) {
            @unlink($cacheFile);
            return null;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * 保存数据到缓存
     * 
     * @param string $key 缓存键名
     * @param mixed $data 数据
     * @return bool
     */
    private static function saveToCache($key, $data) {
        if (!self::$cacheEnabled) {
            return false;
        }
        
        $cacheFile = self::getCacheFile($key);
        $cacheDir = dirname($cacheFile);
        
        if (!is_dir($cacheDir)) {
            @mkdir($cacheDir, 0755, true);
        }
        
        $cacheData = [
            'data' => $data,
            'expire' => time() + self::$cacheExpire
        ];
        
        return @file_put_contents($cacheFile, serialize($cacheData)) !== false;
    }
    
    /**
     * 清除缓存
     * 
     * @param string $key 缓存键名（可选）
     */
    public static function clearCache($key = null) {
        if ($key) {
            $cacheFile = self::getCacheFile($key);
            @unlink($cacheFile);
        } else {
            $cacheDir = dirname(__DIR__) . '/storage/cache/config/';
            if (is_dir($cacheDir)) {
                $files = glob($cacheDir . '*.cache');
                foreach ($files as $file) {
                    @unlink($file);
                }
            }
        }
    }
    
    /**
     * 获取缓存文件路径
     * 
     * @param string $key 缓存键名
     * @return string
     */
    private static function getCacheFile($key) {
        $cacheDir = dirname(__DIR__) . '/storage/cache/config/';
        return $cacheDir . md5($key) . '.cache';
    }
    
    /**
     * 设置缓存配置
     * 
     * @param bool $enabled 是否启用缓存
     * @param int $expire 缓存过期时间
     */
    public static function setCacheConfig($enabled = true, $expire = 3600) {
        self::$cacheEnabled = $enabled;
        self::$cacheExpire = $expire;
    }
    
    /**
     * 重新加载配置
     */
    public static function reload() {
        self::$config = [];
        self::$loaded = false;
        self::clearCache();
        self::load();
    }
    
    /**
     * 获取网盘配置（便捷方法）
     * 
     * @param string $platform 网盘平台
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getNetdiskConfig($platform, $key = null, $default = null) {
        $prefix = $platform . '_';
        
        if ($key === null) {
            // 获取整个平台配置
            $config = [];
            $allConfig = self::all();
            
            foreach ($allConfig as $configKey => $value) {
                if (strpos($configKey, $prefix) === 0) {
                    $shortKey = substr($configKey, strlen($prefix));
                    $config[$shortKey] = $value;
                }
            }
            
            return $config;
        }
        
        return self::get($prefix . $key, $default);
    }
}