<?php
/**
 * SOPAN 异常处理器
 * 提供统一的异常处理和错误响应格式
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class ExceptionHandler {
    private static $instance = null;
    private $logger;
    private $isDebug = false;
    
    private function __construct() {
        $this->isDebug = defined('SOPAN_DEBUG') && SOPAN_DEBUG;
        $this->initializeLogger();
        $this->registerHandlers();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 初始化日志记录器
     */
    private function initializeLogger() {
        require_once __DIR__ . '/Logger.php';
        $this->logger = Logger::getInstance();
    }
    
    /**
     * 注册异常和错误处理器
     */
    private function registerHandlers() {
        // 设置异常处理器
        set_exception_handler([$this, 'handleException']);
        
        // 设置错误处理器
        set_error_handler([$this, 'handleError']);
        
        // 设置致命错误处理器
        register_shutdown_function([$this, 'handleFatalError']);
    }
    
    /**
     * 处理未捕获的异常
     */
    public function handleException($exception) {
        $this->logger->error('Uncaught Exception', [
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'type' => get_class($exception)
        ]);
        
        $this->sendErrorResponse([
            'error_type' => 'Exception',
            'message' => $this->isDebug ? $exception->getMessage() : '系统异常，请稍后重试',
            'file' => $this->isDebug ? $exception->getFile() : null,
            'line' => $this->isDebug ? $exception->getLine() : null,
            'trace' => $this->isDebug ? $exception->getTraceAsString() : null
        ], 500);
    }
    
    /**
     * 处理PHP错误
     */
    public function handleError($severity, $message, $file, $line) {
        // 不处理被@抑制的错误
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorType = $this->getErrorTypeName($severity);
        
        $this->logger->error('PHP Error', [
            'severity' => $severity,
            'type' => $errorType,
            'message' => $message,
            'file' => $file,
            'line' => $line
        ]);
        
        // 对于严重错误，发送错误响应
        if ($severity & (E_ERROR | E_CORE_ERROR | E_COMPILE_ERROR | E_USER_ERROR)) {
            $this->sendErrorResponse([
                'error_type' => 'PHP Error',
                'severity' => $errorType,
                'message' => $this->isDebug ? $message : '系统错误',
                'file' => $this->isDebug ? $file : null,
                'line' => $this->isDebug ? $line : null
            ], 500);
        }
        
        return true;
    }
    
    /**
     * 处理致命错误
     */
    public function handleFatalError() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            $this->logger->critical('Fatal Error', [
                'type' => $this->getErrorTypeName($error['type']),
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line']
            ]);
            
            $this->sendErrorResponse([
                'error_type' => 'Fatal Error',
                'message' => $this->isDebug ? $error['message'] : '系统发生致命错误',
                'file' => $this->isDebug ? $error['file'] : null,
                'line' => $this->isDebug ? $error['line'] : null
            ], 500);
        }
    }
    
    /**
     * 发送标准化错误响应
     */
    private function sendErrorResponse($error, $statusCode = 500) {
        // 清除之前的输出
        if (ob_get_level()) {
            ob_clean();
        }
        
        http_response_code($statusCode);
        
        // 检查是否是API请求
        if ($this->isApiRequest()) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode([
                'success' => false,
                'error' => array_filter($error), // 移除null值
                'code' => $statusCode,
                'timestamp' => time(),
                'request_id' => $this->generateRequestId()
            ], JSON_UNESCAPED_UNICODE);
        } else {
            // Web页面错误
            $this->renderErrorPage($error, $statusCode);
        }
        
        exit;
    }
    
    /**
     * 渲染错误页面
     */
    private function renderErrorPage($error, $statusCode) {
        $title = $this->getStatusTitle($statusCode);
        $message = $error['message'] ?? '未知错误';
        
        echo "<!DOCTYPE html>
<html lang=\"zh-CN\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>{$title} - SOPAN</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 50px auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #dc2626; margin-bottom: 20px; }
        .error-code { font-size: 2em; color: #666; margin-bottom: 10px; }
        .error-message { color: #333; margin-bottom: 20px; }
        .back-link { display: inline-block; margin-top: 20px; color: #2563eb; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px; font-family: monospace; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class=\"container\">
        <div class=\"error-code\">{$statusCode}</div>
        <h1>{$title}</h1>
        <div class=\"error-message\">{$message}</div>
        <a href=\"/\" class=\"back-link\">← 返回首页</a>";
        
        if ($this->isDebug && !empty($error['file'])) {
            echo "<div class=\"debug-info\">
                <strong>Debug Info:</strong><br>
                File: {$error['file']}<br>
                Line: {$error['line']}";
            
            if (!empty($error['trace'])) {
                echo "<br><br><strong>Stack Trace:</strong><br><pre>" . htmlspecialchars($error['trace']) . "</pre>";
            }
            
            echo "</div>";
        }
        
        echo "
    </div>
</body>
</html>";
    }
    
    /**
     * 检查是否是API请求
     */
    private function isApiRequest() {
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        $accept = $_SERVER['HTTP_ACCEPT'] ?? '';
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        return strpos($uri, '/api/') !== false || 
               strpos($accept, 'application/json') !== false ||
               strpos($contentType, 'application/json') !== false;
    }
    
    /**
     * 获取错误类型名称
     */
    private function getErrorTypeName($type) {
        $types = [
            E_ERROR => 'E_ERROR',
            E_WARNING => 'E_WARNING',
            E_PARSE => 'E_PARSE',
            E_NOTICE => 'E_NOTICE',
            E_CORE_ERROR => 'E_CORE_ERROR',
            E_CORE_WARNING => 'E_CORE_WARNING',
            E_COMPILE_ERROR => 'E_COMPILE_ERROR',
            E_COMPILE_WARNING => 'E_COMPILE_WARNING',
            E_USER_ERROR => 'E_USER_ERROR',
            E_USER_WARNING => 'E_USER_WARNING',
            E_USER_NOTICE => 'E_USER_NOTICE',
            E_STRICT => 'E_STRICT',
            E_RECOVERABLE_ERROR => 'E_RECOVERABLE_ERROR',
            E_DEPRECATED => 'E_DEPRECATED',
            E_USER_DEPRECATED => 'E_USER_DEPRECATED'
        ];
        
        return $types[$type] ?? 'UNKNOWN';
    }
    
    /**
     * 获取HTTP状态码标题
     */
    private function getStatusTitle($code) {
        $titles = [
            400 => '请求错误',
            401 => '未授权',
            403 => '禁止访问',
            404 => '页面未找到',
            405 => '方法不允许',
            429 => '请求过于频繁',
            500 => '服务器内部错误',
            502 => '网关错误',
            503 => '服务不可用',
            504 => '网关超时'
        ];
        
        return $titles[$code] ?? '系统错误';
    }
    
    /**
     * 生成请求ID
     */
    private function generateRequestId() {
        return uniqid('req_', true);
    }
    
    /**
     * 手动处理应用异常
     */
    public static function handleApplicationException($exception, $context = []) {
        $handler = self::getInstance();
        
        $handler->logger->error('Application Exception', array_merge([
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'type' => get_class($exception)
        ], $context));
        
        $handler->sendErrorResponse([
            'error_type' => 'Application Exception',
            'message' => $handler->isDebug ? $exception->getMessage() : '操作失败，请稍后重试',
            'file' => $handler->isDebug ? $exception->getFile() : null,
            'line' => $handler->isDebug ? $exception->getLine() : null
        ], 500);
    }
    
    /**
     * 处理验证错误
     */
    public static function handleValidationError($errors, $statusCode = 400) {
        $handler = self::getInstance();
        
        $handler->logger->warning('Validation Error', ['errors' => $errors]);
        
        if ($handler->isApiRequest()) {
            header('Content-Type: application/json; charset=utf-8');
            http_response_code($statusCode);
            echo json_encode([
                'success' => false,
                'error' => [
                    'type' => 'Validation Error',
                    'message' => '输入数据验证失败',
                    'errors' => $errors
                ],
                'code' => $statusCode,
                'timestamp' => time()
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        $handler->sendErrorResponse([
            'error_type' => 'Validation Error',
            'message' => '输入数据验证失败：' . implode(', ', (array)$errors)
        ], $statusCode);
    }
}