<?php
/**
 * SOPAN 网络请求工具类
 * 包含心悦搜索项目的curlHelper函数
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class HttpClient {
    
    /**
     * 通用CURL请求函数
     * 复制自心悦搜索项目的curlHelper函数
     *
     * @param string $url URL地址
     * @param string $method 请求方法, 支持GET/POST/PUT/DELETE/PATCH/TRACE/OPTIONS/HEAD 默认POST
     * @param mixed $data 请求数据包体
     * @param array $header 请求头 数组
     * @param array $queryParams 查询参数 数组
     * @param string $cookies 请求COOKIES字符串
     * @param int $timeout 请求超时时间，默认30秒
     * @return array 响应数组，包括header, body, detail, error
     */
    public static function curlHelper($url, $method = 'POST', $data = null, $header = [], $queryParams = "", $cookies = "", $timeout = 60) {
        // 构建查询参数
        if (!empty($queryParams)) {
            $queryString = http_build_query($queryParams);
            $url .= '?' . $queryString;
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        // 设置请求方法
        switch (strtoupper($method)) {
            case 'GET':
                // GET是默认方法，无需额外设置
                break;
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                }
                break;
            case 'PATCH':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                }
                break;
            case 'HEAD':
                curl_setopt($ch, CURLOPT_NOBODY, true);
                break;
            case 'OPTIONS':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'OPTIONS');
                break;
            case 'TRACE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'TRACE');
                break;
            default:
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, strtoupper($method));
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                }
                break;
        }

        // 设置请求头
        if (!empty($header)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        }

        // 设置cookies
        if (!empty($cookies)) {
            curl_setopt($ch, CURLOPT_COOKIE, $cookies);
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        
        curl_close($ch);

        $output = [
            'header' => substr($response, 0, $headerSize),
            'body' => substr($response, $headerSize),
            'detail' => $info,
            'error' => $error,
            'http_code' => $httpCode
        ];

        return $output;
    }

    /**
     * 模拟表单上传文件请求
     * 
     * @param string $url 提交地址
     * @param array $data 提交数据
     * @param array $header 请求头
     * @param string $cookies 如设置了Content-Type将被自动覆写为formdata
     * @return mixed
     */
    public static function curlForm($url, $data = null, $header = [], $cookies = "") {
        $header[] = 'Content-Type: multipart/form-data';
        return self::curlHelper($url, "POST", $data, $header, "", $cookies);
    }

    /**
     * GET请求封装
     * 
     * @param string $url URL地址
     * @param array $params 查询参数
     * @param array $headers 请求头
     * @param int $timeout 超时时间
     * @return array
     */
    public static function get($url, $params = [], $headers = [], $timeout = 30) {
        return self::curlHelper($url, 'GET', null, $headers, $params, '', $timeout);
    }

    /**
     * POST请求封装
     * 
     * @param string $url URL地址
     * @param mixed $data 请求数据
     * @param array $headers 请求头
     * @param int $timeout 超时时间
     * @return array
     */
    public static function post($url, $data = null, $headers = [], $timeout = 30) {
        return self::curlHelper($url, 'POST', $data, $headers, '', '', $timeout);
    }

    /**
     * JSON POST请求封装
     * 
     * @param string $url URL地址
     * @param array $data 请求数据
     * @param array $headers 额外请求头
     * @param int $timeout 超时时间
     * @return array
     */
    public static function postJson($url, $data = [], $headers = [], $timeout = 30) {
        $defaultHeaders = ['Content-Type: application/json'];
        $headers = array_merge($defaultHeaders, $headers);
        $jsonData = is_array($data) ? json_encode($data) : $data;
        
        return self::curlHelper($url, 'POST', $jsonData, $headers, '', '', $timeout);
    }
}

// 为了兼容性，创建全局curlHelper函数
if (!function_exists('curlHelper')) {
    function curlHelper($url, $method = 'POST', $data = null, $header = [], $queryParams = "", $cookies = "", $timeout = 60) {
        return HttpClient::curlHelper($url, $method, $data, $header, $queryParams, $cookies, $timeout);
    }
}