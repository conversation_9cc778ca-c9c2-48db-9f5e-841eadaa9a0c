<?php
/**
 * SOPAN 数据库连接类
 * 基于PDO的轻量级数据库操作封装
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class Database {
    private static $instance = null;
    private $pdo;
    private $config;
    
    /**
     * 构造函数 - 私有化防止直接实例化
     */
    private function __construct() {
        $this->config = $this->loadConfig();
        $this->connect();
    }
    
    /**
     * 获取数据库实例（单例模式）
     * 
     * @return Database
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 加载数据库配置
     * 
     * @return array
     */
    private function loadConfig() {
        $configFile = dirname(__DIR__) . '/config/database.php';
        if (file_exists($configFile)) {
            return require $configFile;
        }
        
        // 默认配置
        return [
            'host' => 'localhost',
            'port' => 3306,
            'database' => 'sopan_db',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        ];
    }
    
    /**
     * 建立数据库连接
     */
    private function connect() {
        try {
            $dsn = sprintf(
                'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                $this->config['host'],
                $this->config['port'],
                $this->config['database'],
                $this->config['charset']
            );
            
            $this->pdo = new PDO(
                $dsn,
                $this->config['username'],
                $this->config['password'],
                $this->config['options']
            );
            
        } catch (PDOException $e) {
            error_log('Database connection failed: ' . $e->getMessage());
            throw new Exception('数据库连接失败');
        }
    }
    
    /**
     * 获取PDO实例
     * 
     * @return PDO
     */
    public function getPdo() {
        return $this->pdo;
    }
    
    /**
     * 执行SQL查询
     * 
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return PDOStatement
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log('Database query failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            throw new Exception('数据库查询失败');
        }
    }
    
    /**
     * 查找单条记录
     * 
     * @param string $table 表名
     * @param array $conditions 查询条件
     * @param string $fields 查询字段
     * @return array|null
     */
    public function find($table, $conditions = [], $fields = '*') {
        $sql = "SELECT {$fields} FROM `{$table}`";
        $params = [];
        
        if (!empty($conditions)) {
            $where = [];
            foreach ($conditions as $key => $value) {
                $where[] = "`{$key}` = :{$key}";
                $params[$key] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $where);
        }
        
        $sql .= " LIMIT 1";
        
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 查找多条记录
     * 
     * @param string $table 表名
     * @param array $conditions 查询条件
     * @param string $fields 查询字段
     * @param string $orderBy 排序
     * @param int $limit 限制条数
     * @param int $offset 偏移量
     * @return array
     */
    public function findAll($table, $conditions = [], $fields = '*', $orderBy = '', $limit = 0, $offset = 0) {
        $sql = "SELECT {$fields} FROM `{$table}`";
        $params = [];
        
        if (!empty($conditions)) {
            $where = [];
            foreach ($conditions as $key => $value) {
                if (is_array($value)) {
                    // IN 查询
                    $placeholders = implode(',', array_fill(0, count($value), '?'));
                    $where[] = "`{$key}` IN ({$placeholders})";
                    $params = array_merge($params, $value);
                } else {
                    $where[] = "`{$key}` = :{$key}";
                    $params[$key] = $value;
                }
            }
            $sql .= " WHERE " . implode(' AND ', $where);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit > 0) {
            $sql .= " LIMIT {$limit}";
            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }
        }
        
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 插入记录
     * 
     * @param string $table 表名
     * @param array $data 数据
     * @return int 插入的ID
     */
    public function insert($table, $data) {
        $fields = array_keys($data);
        $placeholders = array_map(function($field) { return ":{$field}"; }, $fields);
        
        $sql = sprintf(
            "INSERT INTO `%s` (`%s`) VALUES (%s)",
            $table,
            implode('`, `', $fields),
            implode(', ', $placeholders)
        );
        
        $this->query($sql, $data);
        return $this->pdo->lastInsertId();
    }
    
    /**
     * 更新记录
     * 
     * @param string $table 表名
     * @param array $data 数据
     * @param array $conditions 条件
     * @return int 影响行数
     */
    public function update($table, $data, $conditions) {
        $set = [];
        $params = [];
        
        foreach ($data as $key => $value) {
            $set[] = "`{$key}` = :set_{$key}";
            $params["set_{$key}"] = $value;
        }
        
        $where = [];
        foreach ($conditions as $key => $value) {
            $where[] = "`{$key}` = :where_{$key}";
            $params["where_{$key}"] = $value;
        }
        
        $sql = sprintf(
            "UPDATE `%s` SET %s WHERE %s",
            $table,
            implode(', ', $set),
            implode(' AND ', $where)
        );
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 删除记录
     * 
     * @param string $table 表名
     * @param array $conditions 条件
     * @return int 影响行数
     */
    public function delete($table, $conditions) {
        $where = [];
        $params = [];
        
        foreach ($conditions as $key => $value) {
            $where[] = "`{$key}` = :{$key}";
            $params[$key] = $value;
        }
        
        $sql = sprintf(
            "DELETE FROM `%s` WHERE %s",
            $table,
            implode(' AND ', $where)
        );
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 获取记录总数
     * 
     * @param string $table 表名
     * @param array $conditions 条件
     * @return int
     */
    public function count($table, $conditions = []) {
        $sql = "SELECT COUNT(*) FROM `{$table}`";
        $params = [];
        
        if (!empty($conditions)) {
            $where = [];
            foreach ($conditions as $key => $value) {
                $where[] = "`{$key}` = :{$key}";
                $params[$key] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $where);
        }
        
        $stmt = $this->query($sql, $params);
        return (int) $stmt->fetchColumn();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        return $this->pdo->rollBack();
    }
    
    /**
     * 执行事务
     * 
     * @param callable $callback 回调函数
     * @return mixed
     */
    public function transaction($callback) {
        $this->beginTransaction();
        
        try {
            $result = call_user_func($callback, $this);
            $this->commit();
            return $result;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * 防止克隆
     */
    private function __clone() {}
    
    /**
     * 防止反序列化
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}