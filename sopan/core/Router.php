<?php
/**
 * SOPAN 轻量级路由器
 * 提供RESTful路由支持，无依赖框架
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class Router {
    private $routes = [];
    private $middlewares = [];
    private $groupPrefix = '';
    private $groupMiddlewares = [];
    
    /**
     * 添加GET路由
     * 
     * @param string $path 路径
     * @param mixed $callback 回调函数或控制器方法
     * @return Router
     */
    public function get($path, $callback) {
        return $this->addRoute('GET', $path, $callback);
    }
    
    /**
     * 添加POST路由
     * 
     * @param string $path 路径
     * @param mixed $callback 回调函数或控制器方法
     * @return Router
     */
    public function post($path, $callback) {
        return $this->addRoute('POST', $path, $callback);
    }
    
    /**
     * 添加PUT路由
     * 
     * @param string $path 路径
     * @param mixed $callback 回调函数或控制器方法
     * @return Router
     */
    public function put($path, $callback) {
        return $this->addRoute('PUT', $path, $callback);
    }
    
    /**
     * 添加DELETE路由
     * 
     * @param string $path 路径
     * @param mixed $callback 回调函数或控制器方法
     * @return Router
     */
    public function delete($path, $callback) {
        return $this->addRoute('DELETE', $path, $callback);
    }
    
    /**
     * 添加路由组
     * 
     * @param string $prefix 前缀
     * @param callable $callback 回调函数
     * @param array $middlewares 中间件
     */
    public function group($prefix, $callback, $middlewares = []) {
        $previousPrefix = $this->groupPrefix;
        $previousMiddlewares = $this->groupMiddlewares;
        
        $this->groupPrefix = $previousPrefix . '/' . trim($prefix, '/');
        $this->groupMiddlewares = array_merge($previousMiddlewares, $middlewares);
        
        call_user_func($callback, $this);
        
        $this->groupPrefix = $previousPrefix;
        $this->groupMiddlewares = $previousMiddlewares;
    }
    
    /**
     * 添加中间件
     * 
     * @param string $name 中间件名称
     * @param callable $middleware 中间件函数
     */
    public function middleware($name, $middleware) {
        $this->middlewares[$name] = $middleware;
    }
    
    /**
     * 添加路由
     * 
     * @param string $method HTTP方法
     * @param string $path 路径
     * @param mixed $callback 回调函数
     * @return Router
     */
    private function addRoute($method, $path, $callback) {
        $fullPath = $this->groupPrefix . '/' . trim($path, '/');
        $fullPath = '/' . trim($fullPath, '/');
        if ($fullPath === '/') $fullPath = '/';
        
        $this->routes[] = [
            'method' => $method,
            'path' => $fullPath,
            'callback' => $callback,
            'middlewares' => $this->groupMiddlewares,
            'pattern' => $this->convertToPattern($fullPath)
        ];
        
        return $this;
    }
    
    /**
     * 将路径转换为正则表达式模式
     * 
     * @param string $path 路径
     * @return string
     */
    private function convertToPattern($path) {
        // 转换参数占位符 {id} -> (?P<id>[^/]+)
        $pattern = preg_replace('/\{([a-zA-Z_][a-zA-Z0-9_]*)\}/', '(?P<$1>[^/]+)', $path);
        // 转义其他正则字符
        $pattern = str_replace('/', '\/', $pattern);
        return '/^' . $pattern . '$/';
    }
    
    /**
     * 路由分发
     * 
     * @param string $method HTTP方法
     * @param string $uri 请求URI
     * @return mixed
     */
    public function dispatch($method = null, $uri = null) {
        $method = $method ?: $_SERVER['REQUEST_METHOD'];
        $uri = $uri ?: $this->getCurrentUri();
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && preg_match($route['pattern'], $uri, $matches)) {
                // 提取路由参数
                $params = [];
                foreach ($matches as $key => $value) {
                    if (!is_numeric($key)) {
                        $params[$key] = $value;
                    }
                }
                
                // 执行中间件
                foreach ($route['middlewares'] as $middlewareName) {
                    if (isset($this->middlewares[$middlewareName])) {
                        $result = call_user_func($this->middlewares[$middlewareName]);
                        if ($result === false) {
                            return false; // 中间件阻止继续执行
                        }
                    }
                }
                
                // 执行路由回调
                return $this->executeCallback($route['callback'], $params);
            }
        }
        
        // 未找到路由，返回404
        $this->handle404();
        return false;
    }
    
    /**
     * 获取当前URI
     * 支持ThinkPHP风格的s参数路由
     * 
     * @return string
     */
    private function getCurrentUri() {
        // 优先从 s 参数获取路径（Nginx重写后的参数）
        if (isset($_GET['s'])) {
            $uri = $_GET['s'];
            // 确保以/开头
            $uri = '/' . ltrim($uri, '/');
            return $uri;
        }
        
        // 备用方式：从 REQUEST_URI 获取
        $uri = $_SERVER['REQUEST_URI'];
        
        // 移除查询字符串
        if (($pos = strpos($uri, '?')) !== false) {
            $uri = substr($uri, 0, $pos);
        }
        
        return $uri;
    }
    
    /**
     * 获取当前URI（公共方法）
     * 支持ThinkPHP风格的s参数路由
     * 
     * @return string
     */
    public function getCurrentPath() {
        return $this->getCurrentUri();
    }
    
    /**
     * 执行回调函数
     * 
     * @param mixed $callback 回调函数
     * @param array $params 参数
     * @return mixed
     */
    private function executeCallback($callback, $params = []) {
        if (is_callable($callback)) {
            return call_user_func_array($callback, $params);
        }
        
        if (is_string($callback)) {
            // 解析控制器@方法格式
            if (strpos($callback, '@') !== false) {
                list($controller, $method) = explode('@', $callback, 2);
                return $this->callController($controller, $method, $params);
            }
            
            // 解析控制器::方法格式
            if (strpos($callback, '::') !== false) {
                list($controller, $method) = explode('::', $callback, 2);
                return $this->callController($controller, $method, $params);
            }
        }
        
        if (is_array($callback) && count($callback) === 2) {
            list($controller, $method) = $callback;
            return $this->callController($controller, $method, $params);
        }
        
        throw new Exception('Invalid callback format');
    }
    
    /**
     * 调用控制器方法
     * 
     * @param string $controllerName 控制器名称
     * @param string $methodName 方法名称
     * @param array $params 参数
     * @return mixed
     */
    private function callController($controllerName, $methodName, $params = []) {
        // 加载控制器文件
        $controllerFile = dirname(__DIR__) . '/app/controllers/' . $controllerName . '.php';
        
        if (!file_exists($controllerFile)) {
            throw new Exception("Controller file not found: {$controllerFile}");
        }
        
        require_once $controllerFile;
        
        if (!class_exists($controllerName)) {
            throw new Exception("Controller class not found: {$controllerName}");
        }
        
        $controller = new $controllerName();
        
        if (!method_exists($controller, $methodName)) {
            throw new Exception("Controller method not found: {$controllerName}::{$methodName}");
        }
        
        return call_user_func_array([$controller, $methodName], $params);
    }
    
    /**
     * 处理404错误
     */
    private function handle404() {
        http_response_code(404);
        
        // 尝试加载自定义404页面
        $errorPage = dirname(__DIR__) . '/views/errors/404.php';
        if (file_exists($errorPage)) {
            include $errorPage;
        } else {
            echo json_encode([
                'code' => 404,
                'message' => 'Not Found',
                'data' => null
            ]);
        }
    }
    
    /**
     * 处理500错误
     * 
     * @param Exception $e 异常对象
     */
    public static function handle500($e) {
        http_response_code(500);
        
        // 记录错误日志
        error_log('Router Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
        
        // 尝试加载自定义500页面
        $errorPage = dirname(__DIR__) . '/views/errors/500.php';
        if (file_exists($errorPage)) {
            include $errorPage;
        } else {
            if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
                echo json_encode([
                    'code' => 500,
                    'message' => 'Internal Server Error',
                    'data' => null
                ]);
            } else {
                echo '<h1>500 Internal Server Error</h1>';
                echo '<p>Something went wrong. Please try again later.</p>';
            }
        }
    }
    
    /**
     * 生成URL
     * 
     * @param string $path 路径
     * @param array $params 参数
     * @return string
     */
    public static function url($path, $params = []) {
        $baseUrl = self::getBaseUrl();
        $url = $baseUrl . '/' . ltrim($path, '/');
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }
    
    /**
     * 获取基础URL
     * 
     * @return string
     */
    private static function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $path = dirname($_SERVER['SCRIPT_NAME']);
        
        return $protocol . '://' . $host . rtrim($path, '/');
    }
    
    /**
     * 重定向
     * 
     * @param string $url URL
     * @param int $code 状态码
     */
    public static function redirect($url, $code = 302) {
        http_response_code($code);
        header('Location: ' . $url);
        exit;
    }
    
    /**
     * 获取所有路由
     * 
     * @return array
     */
    public function getRoutes() {
        return $this->routes;
    }
}