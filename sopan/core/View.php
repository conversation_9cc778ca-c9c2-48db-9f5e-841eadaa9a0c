<?php
/**
 * SOPAN 视图渲染类
 * 原生PHP模板引擎，支持布局继承和数据绑定
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class View {
    private $templatePath;
    private $data = [];
    private $sections = [];
    private $currentSection = null;
    private $layout = null;
    private static $cacheEnabled = true;
    private static $cacheDir;
    
    /**
     * 构造函数
     * 
     * @param string $templatePath 模板路径
     */
    public function __construct($templatePath = null) {
        $this->templatePath = $templatePath ?: dirname(__DIR__) . '/views/';
        self::$cacheDir = dirname(__DIR__) . '/storage/cache/views/';
        
        // 确保缓存目录存在
        if (!is_dir(self::$cacheDir)) {
            @mkdir(self::$cacheDir, 0755, true);
        }
    }
    
    /**
     * 渲染模板
     * 
     * @param string $template 模板文件名
     * @param array $data 数据
     * @return string
     */
    public function render($template, $data = []) {
        // 合并数据
        $this->data = array_merge($this->data, $data);
        
        // 检查模板缓存
        if (self::$cacheEnabled) {
            $cacheKey = $this->getCacheKey($template, $this->data);
            $cached = $this->getFromCache($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }
        
        // 渲染模板
        $content = $this->renderTemplate($template);
        
        // 如果有布局，渲染布局
        if ($this->layout) {
            $this->assign('content', $content);
            $content = $this->renderTemplate($this->layout);
        }
        
        // 保存到缓存
        if (self::$cacheEnabled) {
            $this->saveToCache($cacheKey, $content);
        }
        
        return $content;
    }
    
    /**
     * 使用布局渲染
     * 
     * @param string $layout 布局文件名
     * @param string $template 模板文件名
     * @param array $data 数据
     * @return string
     */
    public function renderWithLayout($layout, $template, $data = []) {
        $this->layout = $layout;
        return $this->render($template, $data);
    }
    
    /**
     * 渲染模板文件
     * 
     * @param string $template 模板文件名
     * @return string
     */
    private function renderTemplate($template) {
        $templateFile = $this->getTemplatePath($template);
        
        if (!file_exists($templateFile)) {
            throw new Exception("Template file not found: {$templateFile}");
        }
        
        // 提取数据变量到当前作用域
        extract($this->data, EXTR_SKIP);
        
        // 开始输出缓冲
        ob_start();
        
        try {
            include $templateFile;
            $content = ob_get_contents();
        } catch (Exception $e) {
            ob_end_clean();
            throw $e;
        }
        
        ob_end_clean();
        
        return $content;
    }
    
    /**
     * 分配数据变量
     * 
     * @param string $key 变量名
     * @param mixed $value 变量值
     * @return View
     */
    public function assign($key, $value) {
        $this->data[$key] = $value;
        return $this;
    }
    
    /**
     * 批量分配数据
     * 
     * @param array $data 数据数组
     * @return View
     */
    public function assignArray($data) {
        $this->data = array_merge($this->data, $data);
        return $this;
    }
    
    /**
     * 设置布局
     * 
     * @param string $layout 布局文件名
     * @return View
     */
    public function setLayout($layout) {
        $this->layout = $layout;
        return $this;
    }
    
    /**
     * 开始内容块
     * 
     * @param string $name 块名称
     */
    public function startSection($name) {
        $this->currentSection = $name;
        ob_start();
    }
    
    /**
     * 结束内容块
     */
    public function endSection() {
        if ($this->currentSection) {
            $this->sections[$this->currentSection] = ob_get_contents();
            ob_end_clean();
            $this->currentSection = null;
        }
    }
    
    /**
     * 输出内容块
     * 
     * @param string $name 块名称
     * @param string $default 默认内容
     */
    public function section($name, $default = '') {
        echo isset($this->sections[$name]) ? $this->sections[$name] : $default;
    }
    
    /**
     * 检查内容块是否存在
     * 
     * @param string $name 块名称
     * @return bool
     */
    public function hasSection($name) {
        return isset($this->sections[$name]);
    }
    
    /**
     * 包含子模板
     * 
     * @param string $template 模板文件名
     * @param array $data 额外数据
     */
    public function include($template, $data = []) {
        $originalData = $this->data;
        $this->data = array_merge($this->data, $data);
        
        echo $this->renderTemplate($template);
        
        $this->data = $originalData;
    }
    
    /**
     * 转义HTML字符
     * 
     * @param string $value 值
     * @return string
     */
    public function escape($value) {
        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 格式化URL
     * 
     * @param string $path 路径
     * @param array $params 参数
     * @return string
     */
    public function url($path, $params = []) {
        return Router::url($path, $params);
    }
    
    /**
     * 获取配置值
     * 
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function config($key, $default = null) {
        return Config::get($key, $default);
    }
    
    /**
     * 格式化日期
     * 
     * @param string $date 日期
     * @param string $format 格式
     * @return string
     */
    public function formatDate($date, $format = 'Y-m-d H:i:s') {
        return date($format, strtotime($date));
    }
    
    /**
     * 格式化文件大小
     * 
     * @param int $bytes 字节数
     * @param int $precision 精度
     * @return string
     */
    public function formatFileSize($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 截取字符串
     * 
     * @param string $string 字符串
     * @param int $length 长度
     * @param string $suffix 后缀
     * @return string
     */
    public function truncate($string, $length = 100, $suffix = '...') {
        if (mb_strlen($string, 'UTF-8') <= $length) {
            return $string;
        }
        
        return mb_substr($string, 0, $length, 'UTF-8') . $suffix;
    }
    
    /**
     * 获取模板文件路径
     * 
     * @param string $template 模板名称
     * @return string
     */
    private function getTemplatePath($template) {
        // 支持不同的路径格式
        if (strpos($template, '/') !== false) {
            return $this->templatePath . $template . '.php';
        }
        
        return $this->templatePath . $template . '.php';
    }
    
    /**
     * 获取缓存键名
     * 
     * @param string $template 模板名称
     * @param array $data 数据
     * @return string
     */
    private function getCacheKey($template, $data = []) {
        $templateFile = $this->getTemplatePath($template);
        $mtime = file_exists($templateFile) ? filemtime($templateFile) : 0;
        
        return md5($template . $mtime . serialize($data));
    }
    
    /**
     * 从缓存获取内容
     * 
     * @param string $key 缓存键名
     * @return string|null
     */
    private function getFromCache($key) {
        $cacheFile = self::$cacheDir . $key . '.cache';
        
        if (!file_exists($cacheFile)) {
            return null;
        }
        
        $cacheData = @unserialize(file_get_contents($cacheFile));
        
        if (!$cacheData || !isset($cacheData['expire']) || $cacheData['expire'] < time()) {
            @unlink($cacheFile);
            return null;
        }
        
        return $cacheData['content'];
    }
    
    /**
     * 保存内容到缓存
     * 
     * @param string $key 缓存键名
     * @param string $content 内容
     * @return bool
     */
    private function saveToCache($key, $content) {
        $cacheFile = self::$cacheDir . $key . '.cache';
        
        $cacheData = [
            'content' => $content,
            'expire' => time() + 3600 // 1小时过期
        ];
        
        return @file_put_contents($cacheFile, serialize($cacheData)) !== false;
    }
    
    /**
     * 清除模板缓存
     * 
     * @param string $template 模板名称（可选）
     */
    public static function clearCache($template = null) {
        if ($template) {
            $view = new self();
            $cacheKey = $view->getCacheKey($template);
            $cacheFile = self::$cacheDir . $cacheKey . '.cache';
            @unlink($cacheFile);
        } else {
            $files = glob(self::$cacheDir . '*.cache');
            foreach ($files as $file) {
                @unlink($file);
            }
        }
    }
    
    /**
     * 设置缓存配置
     * 
     * @param bool $enabled 是否启用缓存
     */
    public static function setCacheEnabled($enabled = true) {
        self::$cacheEnabled = $enabled;
    }
    
    /**
     * 静态渲染方法
     * 
     * @param string $template 模板文件名
     * @param array $data 数据
     * @return string
     */
    public static function make($template, $data = []) {
        $view = new self();
        return $view->render($template, $data);
    }
    
    /**
     * 输出JSON响应
     * 
     * @param mixed $data 数据
     * @param int $code 状态码
     * @param string $message 消息
     */
    public static function json($data = null, $code = 200, $message = 'success') {
        header('Content-Type: application/json; charset=utf-8');
        
        echo json_encode([
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ], JSON_UNESCAPED_UNICODE);
        
        exit;
    }
}