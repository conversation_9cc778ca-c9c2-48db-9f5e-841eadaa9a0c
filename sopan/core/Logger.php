<?php
/**
 * SOPAN 日志记录器
 * 支持文件日志和数据库日志，提供多种日志级别
 * 
 * <AUTHOR> Project
 * @version 1.0.0
 */

class Logger {
    private static $instance = null;
    private $db;
    private $logDir;
    private $maxFileSize = 10485760; // 10MB
    private $maxFiles = 5;
    
    // 日志级别常量
    const EMERGENCY = 'emergency';
    const ALERT     = 'alert';
    const CRITICAL  = 'critical';
    const ERROR     = 'error';
    const WARNING   = 'warning';
    const NOTICE    = 'notice';
    const INFO      = 'info';
    const DEBUG     = 'debug';
    
    // 日志类型
    const TYPE_SYSTEM   = 'system';
    const TYPE_ERROR    = 'error';
    const TYPE_SEARCH   = 'search';
    const TYPE_TRANSFER = 'transfer';
    const TYPE_API      = 'api';
    const TYPE_ACCESS   = 'access';
    
    private function __construct() {
        $this->initializeLogger();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 初始化日志器
     */
    private function initializeLogger() {
        // 初始化数据库连接
        try {
            require_once __DIR__ . '/Database.php';
            $this->db = Database::getInstance();
        } catch (Exception $e) {
            // 数据库不可用时只使用文件日志
            $this->db = null;
        }
        
        // 创建日志目录
        $this->logDir = dirname(__DIR__) . '/logs';
        if (!is_dir($this->logDir)) {
            mkdir($this->logDir, 0755, true);
        }
    }
    
    /**
     * 记录紧急日志
     */
    public function emergency($message, array $context = []) {
        $this->log(self::EMERGENCY, $message, $context);
    }
    
    /**
     * 记录警报日志
     */
    public function alert($message, array $context = []) {
        $this->log(self::ALERT, $message, $context);
    }
    
    /**
     * 记录严重错误日志
     */
    public function critical($message, array $context = []) {
        $this->log(self::CRITICAL, $message, $context);
    }
    
    /**
     * 记录错误日志
     */
    public function error($message, array $context = []) {
        $this->log(self::ERROR, $message, $context);
    }
    
    /**
     * 记录警告日志
     */
    public function warning($message, array $context = []) {
        $this->log(self::WARNING, $message, $context);
    }
    
    /**
     * 记录通知日志
     */
    public function notice($message, array $context = []) {
        $this->log(self::NOTICE, $message, $context);
    }
    
    /**
     * 记录信息日志
     */
    public function info($message, array $context = []) {
        $this->log(self::INFO, $message, $context);
    }
    
    /**
     * 记录调试日志
     */
    public function debug($message, array $context = []) {
        $this->log(self::DEBUG, $message, $context);
    }
    
    /**
     * 记录搜索日志
     */
    public function logSearch($keyword, $type, $resultCount, $responseTime, $userIp, $success = true) {
        $context = [
            'keyword' => $keyword,
            'search_type' => $type,
            'result_count' => $resultCount,
            'response_time' => $responseTime,
            'user_ip' => $userIp,
            'is_success' => $success,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $this->log(self::INFO, '搜索操作', $context, self::TYPE_SEARCH);
        
        // 同时记录到搜索日志表
        $this->logSearchToDatabase($context);
    }
    
    /**
     * 记录转存日志
     */
    public function logTransfer($url, $platform, $userIp, $status, $message = '') {
        $context = [
            'url' => $url,
            'platform' => $platform,
            'user_ip' => $userIp,
            'status' => $status,
            'message' => $message
        ];
        
        $this->log(self::INFO, '转存操作', $context, self::TYPE_TRANSFER);
    }
    
    /**
     * 记录API访问日志
     */
    public function logApiAccess($endpoint, $method, $statusCode, $responseTime, $userIp) {
        $context = [
            'endpoint' => $endpoint,
            'method' => $method,
            'status_code' => $statusCode,
            'response_time' => $responseTime,
            'user_ip' => $userIp,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $this->log(self::INFO, 'API访问', $context, self::TYPE_API);
    }
    
    /**
     * 核心日志记录方法
     */
    public function log($level, $message, array $context = [], $type = self::TYPE_SYSTEM) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'type' => $type,
            'message' => $message,
            'context' => $context,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'request_id' => $this->getRequestId()
        ];
        
        // 写入文件日志
        $this->writeToFile($logData);
        
        // 写入数据库日志（如果可用）
        if ($this->shouldLogToDatabase($level, $type)) {
            $this->writeToDatabase($logData);
        }
    }
    
    /**
     * 写入文件日志
     */
    private function writeToFile($logData) {
        try {
            $filename = $this->getLogFileName($logData['type']);
            $logLine = $this->formatLogLine($logData);
            
            // 检查文件大小并轮转
            $this->rotateLogFile($filename);
            
            // 写入日志
            file_put_contents($filename, $logLine . PHP_EOL, FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            // 文件日志失败时，尝试写入错误日志
            error_log('Logger file write failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 写入数据库日志
     */
    private function writeToDatabase($logData) {
        if (!$this->db) {
            return;
        }
        
        try {
            $this->db->insert('system_logs', [
                'level' => $logData['level'],
                'type' => $logData['type'],
                'message' => $logData['message'],
                'context' => json_encode($logData['context'], JSON_UNESCAPED_UNICODE),
                'ip' => $logData['ip'],
                'request_id' => $logData['request_id'],
                'created_at' => $logData['timestamp']
            ]);
        } catch (Exception $e) {
            // 数据库日志失败时，写入文件日志
            error_log('Logger database write failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 记录搜索到数据库
     */
    private function logSearchToDatabase($context) {
        if (!$this->db) {
            return;
        }
        
        try {
            $this->db->insert('search_logs', [
                'keyword' => $context['keyword'],
                'user_ip' => $context['user_ip'],
                'user_agent' => $context['user_agent'],
                'search_type' => $context['search_type'],
                'result_count' => $context['result_count'],
                'response_time' => $context['response_time'],
                'is_success' => $context['is_success'] ? 1 : 0,
                'search_time' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log('Search log database write failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 格式化日志行
     */
    private function formatLogLine($logData) {
        $context = empty($logData['context']) ? '' : ' ' . json_encode($logData['context'], JSON_UNESCAPED_UNICODE);
        
        return sprintf(
            '[%s] %s.%s: %s%s {ip: %s, request: %s}',
            $logData['timestamp'],
            strtoupper($logData['level']),
            strtoupper($logData['type']),
            $logData['message'],
            $context,
            $logData['ip'],
            $logData['request_id']
        );
    }
    
    /**
     * 获取日志文件名
     */
    private function getLogFileName($type) {
        return $this->logDir . '/' . $type . '_' . date('Y-m-d') . '.log';
    }
    
    /**
     * 日志文件轮转
     */
    private function rotateLogFile($filename) {
        if (!file_exists($filename) || filesize($filename) < $this->maxFileSize) {
            return;
        }
        
        // 轮转日志文件
        for ($i = $this->maxFiles - 1; $i >= 1; $i--) {
            $oldFile = $filename . '.' . $i;
            $newFile = $filename . '.' . ($i + 1);
            
            if (file_exists($oldFile)) {
                if ($i == $this->maxFiles - 1) {
                    unlink($oldFile); // 删除最旧的文件
                } else {
                    rename($oldFile, $newFile);
                }
            }
        }
        
        // 将当前日志文件重命名
        rename($filename, $filename . '.1');
    }
    
    /**
     * 判断是否应该记录到数据库
     */
    private function shouldLogToDatabase($level, $type) {
        // 调试日志不记录到数据库
        if ($level === self::DEBUG) {
            return false;
        }
        
        // 高频日志类型可以选择性记录
        if ($type === self::TYPE_ACCESS && $level === self::INFO) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取请求ID
     */
    private function getRequestId() {
        static $requestId = null;
        
        if ($requestId === null) {
            $requestId = uniqid('req_', true);
        }
        
        return $requestId;
    }
    
    /**
     * 清理过期日志文件
     */
    public function cleanupOldLogs($days = 30) {
        $cutoffTime = time() - ($days * 24 * 3600);
        $cleaned = 0;
        
        if (is_dir($this->logDir)) {
            $files = glob($this->logDir . '/*.log*');
            
            foreach ($files as $file) {
                if (filemtime($file) < $cutoffTime) {
                    unlink($file);
                    $cleaned++;
                }
            }
        }
        
        // 清理数据库日志
        if ($this->db) {
            try {
                $cutoffDate = date('Y-m-d H:i:s', $cutoffTime);
                $this->db->query("DELETE FROM system_logs WHERE created_at < ?", [$cutoffDate]);
            } catch (Exception $e) {
                $this->error('Failed to cleanup database logs', ['error' => $e->getMessage()]);
            }
        }
        
        $this->info('Log cleanup completed', ['files_removed' => $cleaned, 'days' => $days]);
        
        return $cleaned;
    }
    
    /**
     * 获取日志统计信息
     */
    public function getLogStats() {
        $stats = [
            'file_logs' => [],
            'database_logs' => []
        ];
        
        // 统计文件日志
        if (is_dir($this->logDir)) {
            $files = glob($this->logDir . '/*.log');
            foreach ($files as $file) {
                $stats['file_logs'][] = [
                    'name' => basename($file),
                    'size' => filesize($file),
                    'modified' => filemtime($file)
                ];
            }
        }
        
        // 统计数据库日志
        if ($this->db) {
            try {
                $counts = $this->db->query(
                    "SELECT level, COUNT(*) as count FROM system_logs 
                     WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
                     GROUP BY level"
                );
                
                foreach ($counts as $count) {
                    $stats['database_logs'][$count['level']] = (int)$count['count'];
                }
            } catch (Exception $e) {
                $this->error('Failed to get log stats', ['error' => $e->getMessage()]);
            }
        }
        
        return $stats;
    }
}