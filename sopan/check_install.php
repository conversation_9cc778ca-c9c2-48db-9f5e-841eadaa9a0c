<?php
/**
 * SOPAN 安装环境快速检查脚本
 * 快速检查系统是否准备好进行安装
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOPAN 安装环境检查</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; }
        .content { padding: 20px; }
        .check-item { display: flex; align-items: center; padding: 12px; margin: 8px 0; border-radius: 5px; border-left: 4px solid #ddd; }
        .check-item.success { background: #f6ffed; border-left-color: #52c41a; }
        .check-item.warning { background: #fffbe6; border-left-color: #faad14; }
        .check-item.error { background: #fff2f0; border-left-color: #ff4d4f; }
        .status-icon { font-size: 18px; margin-right: 10px; }
        .action-button { display: inline-block; padding: 12px 24px; background: #1890ff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; transition: background 0.3s; }
        .action-button:hover { background: #40a9ff; }
        .action-button.success { background: #52c41a; }
        .summary { background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0; }
        h2 { margin: 0 0 10px 0; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 SOPAN 安装环境检查</h1>
            <p>快速检查系统是否准备好进行安装</p>
        </div>
        
        <div class="content">
            <?php
            $checks = [];
            $canInstall = true;
            
            // 1. PHP版本检查
            $phpVersion = PHP_VERSION;
            $phpVersionOk = version_compare($phpVersion, '7.4.0', '>=');
            $checks[] = [
                'title' => 'PHP版本检查',
                'status' => $phpVersionOk ? 'success' : 'error',
                'message' => "当前版本: {$phpVersion} " . ($phpVersionOk ? '(符合要求 ≥7.4.0)' : '(需要 ≥7.4.0)'),
                'required' => true
            ];
            if (!$phpVersionOk) $canInstall = false;
            
            // 2. 必需扩展检查
            $requiredExts = [
                'pdo_mysql' => 'MySQL数据库支持',
                'json' => 'JSON处理',
                'mbstring' => '多字节字符串',
                'curl' => 'HTTP请求',
                'openssl' => 'SSL加密'
            ];
            
            foreach ($requiredExts as $ext => $desc) {
                $loaded = extension_loaded($ext);
                $checks[] = [
                    'title' => "{$ext} 扩展",
                    'status' => $loaded ? 'success' : 'error',
                    'message' => $desc . ($loaded ? ' ✓' : ' ✗'),
                    'required' => true
                ];
                if (!$loaded) $canInstall = false;
            }
            
            // 3. 可选扩展检查
            $optionalExts = ['gd' => '图像处理', 'zip' => '压缩文件'];
            foreach ($optionalExts as $ext => $desc) {
                $loaded = extension_loaded($ext);
                $checks[] = [
                    'title' => "{$ext} 扩展",
                    'status' => $loaded ? 'success' : 'warning',
                    'message' => $desc . ($loaded ? ' ✓' : ' (可选)'),
                    'required' => false
                ];
            }
            
            // 4. 文件权限检查
            $writableDirs = ['config', 'logs', 'storage'];
            foreach ($writableDirs as $dir) {
                $dirPath = __DIR__ . '/' . $dir;
                $writable = is_dir($dirPath) ? is_writable($dirPath) : is_writable(__DIR__);
                $checks[] = [
                    'title' => "{$dir}/ 目录权限",
                    'status' => $writable ? 'success' : 'error',
                    'message' => $writable ? '可写 ✓' : '不可写 ✗',
                    'required' => true
                ];
                if (!$writable) $canInstall = false;
            }
            
            // 5. 配置目录状态检查
            $configDir = __DIR__ . '/config';
            $configEmpty = true;
            $configFiles = [];
            
            if (is_dir($configDir)) {
                $files = glob($configDir . '/*.php');
                $lockFile = $configDir . '/installed.lock';
                if (!empty($files) || file_exists($lockFile)) {
                    $configEmpty = false;
                    $configFiles = array_merge($files, file_exists($lockFile) ? [$lockFile] : []);
                }
            }
            
            $checks[] = [
                'title' => '配置目录状态',
                'status' => $configEmpty ? 'success' : 'warning',
                'message' => $configEmpty ? '准备就绪 ✓' : '存在配置文件 (需要重新安装时请清理)',
                'required' => false
            ];
            
            // 6. 数据库文件检查
            $sqlFile = __DIR__ . '/database/sopan.sql';
            $sqlExists = file_exists($sqlFile);
            $checks[] = [
                'title' => '数据库文件',
                'status' => $sqlExists ? 'success' : 'error',
                'message' => $sqlExists ? 'sopan.sql 存在 ✓' : 'sopan.sql 不存在 ✗',
                'required' => true
            ];
            if (!$sqlExists) $canInstall = false;
            
            // 显示检查结果
            echo "<h2>📋 环境检查结果</h2>";
            echo "<div class='grid'>";
            
            foreach ($checks as $check) {
                $iconMap = [
                    'success' => '✅',
                    'warning' => '⚠️',
                    'error' => '❌'
                ];
                
                echo "<div class='check-item {$check['status']}'>";
                echo "<span class='status-icon'>{$iconMap[$check['status']]}</span>";
                echo "<div>";
                echo "<strong>{$check['title']}</strong><br>";
                echo "<span>{$check['message']}</span>";
                echo "</div>";
                echo "</div>";
            }
            
            echo "</div>";
            
            // 显示总结
            echo "<div class='summary'>";
            if ($canInstall) {
                echo "<h2 style='color: #52c41a;'>🎉 系统准备就绪！</h2>";
                echo "<p>您的服务器环境已满足SOPAN的安装要求，可以开始安装了。</p>";
            } else {
                echo "<h2 style='color: #ff4d4f;'>⚠️ 环境不满足要求</h2>";
                echo "<p>请先解决上述标红的问题，然后再进行安装。</p>";
            }
            echo "</div>";
            
            // 操作按钮
            echo "<div style='text-align: center; margin-top: 20px;'>";
            
            if ($canInstall) {
                echo "<a href='/install' class='action-button success'>🚀 开始安装</a>";
            }
            
            echo "<a href='/debug_install.php' class='action-button'>🔧 详细调试</a>";
            echo "<a href='/test_env.php' class='action-button'>🧪 环境测试</a>";
            
            if (!$configEmpty) {
                echo "<div style='margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px;'>";
                echo "<p><strong>发现已存在的配置文件：</strong></p>";
                echo "<ul style='text-align: left; display: inline-block;'>";
                foreach ($configFiles as $file) {
                    echo "<li>" . basename($file) . "</li>";
                }
                echo "</ul>";
                echo "<p>如需重新安装，请先备份重要配置文件，然后删除它们。</p>";
                echo "</div>";
            }
            
            echo "</div>";
            ?>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666;">
                <p>📖 需要帮助？请查看 <a href="DEPLOYMENT.md" style="color: #1890ff;">部署文档</a></p>
                <p style="font-size: 0.9em;">SOPAN v1.0.0 | PHP <?php echo PHP_VERSION; ?></p>
            </div>
        </div>
    </div>
</body>
</html>