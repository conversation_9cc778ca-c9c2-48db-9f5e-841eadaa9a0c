# 📂 xinyue-search 功能-代码映射报告

## 🏗️ 项目概览
- 技术栈:
  - 后端: ThinkPHP 6 MVC（路由与控制器）<mcfile name="app.php 路由定义" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile>
  - 管理后台前端: Vue 2.6.10 + Element UI + Axios + ECharts + UEditor（按页初始化 Vue 实例）
    - 关键静态资源: <mcfile name="vue-2.6.10.min.js" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/js/vue-2.6.10.min.js"></mcfile> <mcfile name="element.js" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/js/element.js"></mcfile> <mcfile name="element.css" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/css/element.css"></mcfile> <mcfile name="axios.min.js" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/js/axios.min.js"></mcfile> <mcfile name="echarts.min.js" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/js/echarts.min.js"></mcfile> <mcfile name="UEditor" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/UEditor/"></mcfile>
  - 网站前台: 模板 HTML + Vue（独立构建 runtime，无打包）+ Axios
    - 关键静态资源: <mcfile name="vue.global.min.js" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/news/common/static/js/vue.global.min.js"></mcfile> <mcfile name="axios.min.js" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/news/common/static/js/axios.min.js"></mcfile>
  - 小程序: 微信小程序（WXML/WXSS + page JS），多页面目录
    - 根: <mcfile name="miniapp" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp"></mcfile>
  - 包管理: PHP 使用 Composer（vendor/）；前端无 npm 构建
- 架构模式: 后端 MVC + 管理后台按页面的 Vue 单实例 + 前台模板页面 + 小程序 pages 目录结构
- 状态管理: 管理后台每页本地 data 与 axios；未使用 Vuex。小程序使用页面 data 与本地存储。
- 样式方案: Element UI 主题 + 自定义 YAdmin.css + 前台静态 CSS + 小程序 WXSS
- 构建工具: 无前端打包工具；后端原生 ThinkPHP；小程序通过微信开发者工具运行

## 📊 功能模块统计
- 页面级组件: 35 个（管理后台 20 + 网站前台 6 + 小程序 9）
- 可复用组件: 1+（富文本组件 UEditor 封装，及少量页面内组件）
- 业务逻辑模块: 55 个（后端控制器 34 个 + 模型 21 个）
  - 控制器：API 12 个 <mcfile name="app/api/controller" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/api/controller"></mcfile>，Admin 16 个 <mcfile name="app/admin/controller" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller"></mcfile>，前台 3 个 <mcfile name="app/index/controller" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/index/controller"></mcfile>，Qfadmin 3 个 <mcfile name="app/qfadmin/controller" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/qfadmin/controller"></mcfile>
  - 模型：21 个 <mcfile name="app/model" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/model"></mcfile>
- 样式文件: 15 个（Admin 2 + News 3 + 小程序 WXSS 10）
  - Admin: <mcfile name="YAdmin.css" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/css/YAdmin.css"></mcfile> <mcfile name="element.css" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/css/element.css"></mcfile>
  - News: <mcfile name="app.css" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/news/common/static/css/app.css"></mcfile> <mcfile name="index.min.css" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/news/common/static/css/index.min.css"></mcfile> <mcfile name="m.css" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/news/common/static/css/m.css"></mcfile>
  - 小程序: 各页面 WXSS 见 <mcfile name="miniapp/pages" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp/pages"></mcfile>
- 配置文件: 15 个（config 目录 14 + 路由 1）<mcfile name="config" path="/Users/<USER>/Codes/panseeker/xinyue-search/config"></mcfile> <mcfile name="route/app.php" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile>

## 🗂️ 目录结构概览
```
/Users/<USER>/Codes/panseeker/xinyue-search/
├── app/                 # 应用（控制器/模型/多应用）
├── config/              # 配置（app、route、database、cache...）
├── public/              # 公共静态资源与视图（管理后台/前台）
│   ├── static/admin/    # 管理后台 JS/CSS/UEditor
│   └── views/           # 模板视图（index/qfadmin）
├── miniapp/             # 微信小程序（pages、utils、images）
├── route/               # 路由定义（ThinkPHP）
├── vendor/              # Composer 依赖
└── docs/                # 文档（本报告）
```

---

## 🎯 功能映射表

### 管理后台 - 登录

- 🔤 用户描述方式:
  - 主要: "后台登录页面"、"管理员登录"
  - 别名: "登录表单"、"账号密码登录"
- 📍 代码位置:
  - 主文件: <mcfile name="login.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/admin/login.html"></mcfile>
  - 样式: <mcfile name="YAdmin.css / element.css" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/css/"></mcfile>
  - 逻辑: <mcfile name="Admin 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Admin.php"></mcfile>
- 🎨 视觉标识:
  - 外观: 居中登录卡片背景、Logo、输入框、登录按钮
  - 文本: "登录"、"账号"、"密码"
- ⚡ 修改指引:
  - 修改外观: 编辑登录模板中的表单布局与 Element 组件样式；必要时修改 YAdmin.css
  - 修改行为: 在登录页的 Vue 实例 methods 中调整提交逻辑；后端校验在 Admin 控制器中调整
  - 修改文本: 直接修改 login.html 中对应文案

---

### 管理后台 - 修改密码
- 🔤 用户描述方式:
  - 主要: "修改密码"、"重置我的密码"
  - 别名: "更改密码"
- 📍 代码位置:
  - 主文件: <mcfile name="motifypassword.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/admin/motifypassword.html"></mcfile>
  - 逻辑: <mcfile name="Admin 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Admin.php"></mcfile>
- 🎨 视觉标识: 三个输入框（旧密码/新密码/确认新密码）+ 确认按钮
- ⚡ 修改指引:
  - 表单校验与提交流程在页面 Vue methods 中
  - 后端密码校验与保存逻辑在 Admin 控制器相应方法中

---

### 管理后台 - 修改个人资料
- 🔤 用户描述方式:
  - 主要: "修改个人资料"、"更新我的信息"
- 📍 代码位置:
  - 主文件: <mcfile name="updatemyinfo.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/admin/updatemyinfo.html"></mcfile>
  - 逻辑: <mcfile name="Admin 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Admin.php"></mcfile>
- 🎨 视觉标识: 头像、昵称、手机号、邮箱等字段
- ⚡ 修改指引:
  - 字段显示/校验在页面模板与 Vue data 中；保存接口在 Admin 控制器

---

### 管理后台 - 配置列表（键值对）
- 🔤 用户描述方式:
  - 主要: "配置管理列表"、"新增/编辑/删除配置项"、"搜索配置"
  - 别名: "系统配置"
- 📍 代码位置:
  - 主文件: <mcfile name="conf/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/conf/index.html"></mcfile>
  - 逻辑: <mcfile name="Conf 控制器（Admin）" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Conf.php"></mcfile>
- 🎨 视觉标识: 顶部类别筛选（如 搜索设置/上传配置/SEO 等）、搜索框、表格、分页、对话框
- ⚡ 修改指引:
  - 修改外观: 调整 Element 表格、分页与筛选项在 index.html 中；分类标签列表在模板常量部分
  - 修改行为: 新增/编辑/删除与搜索方法在页面 Vue methods；后端增删改查在 Conf 控制器
  - 修改文本: 直接修改模板中分类文案与列名

---

### 管理后台 - 基础配置（分组表单）
- 🔤 用户描述方式:
  - 主要: "基础设置/SEO/模板/搜索/微信/交易/售后/上传/其他的配置页"
  - 别名: "标签页配置"
- 📍 代码位置:
  - 主文件: <mcfile name="conf/base.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/conf/base.html"></mcfile>
  - 组件: <mcfile name="component/view.html（UEditor 模板）" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/component/view.html"></mcfile> <mcfile name="component.js（注册 UEditor 组件）" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/js/component.js"></mcfile>
  - 逻辑: 后端接口 <mcfile name="Conf 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Conf.php"></mcfile>；UEditor 静态资源 <mcfile name="UEditor/" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/UEditor/"></mcfile>
- 🎨 视觉标识: 顶部标签页切换，多种字段类型（单行/多行文本、单选、多选、单图/多图、富文本、颜色选择）
- ⚡ 修改指引:
  - 修改外观: 标签与表单布局在 base.html；富文本由 UEditor 组件负责
  - 修改行为: 数据获取与保存在 base.html 的 Vue methods；后端读写在 Conf 控制器
  - 上传与富文本: UEditor 与上传配置见 Attach 控制器及 UEditor 配置

---

### 管理后台 - 附件/上传
- 🔤 用户描述方式:
  - 主要: "附件管理"、"上传图片/文件"
- 📍 代码位置:
  - 页面: <mcfile name="attach/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/attach/index.html"></mcfile>
  - 逻辑: <mcfile name="Attach 控制器（Admin）" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Attach.php"></mcfile>
  - UEditor 上传配置读取: <mcfile name="Attach.php（读取 UEditor config.json）" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Attach.php"></mcfile> <mcfile name="UEditor/config.json" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/UEditor/config.json"></mcfile>
- 🎨 视觉标识: 文件列表、上传按钮、预览
- ⚡ 修改指引:
  - 上传策略/白名单: 编辑 UEditor/config.json
  - 后端保存路径/策略: 编辑 Attach 控制器

---

### 管理后台 - 接口源管理（ApiList）
- 🔤 用户描述方式:
  - 主要: "接口源列表"、"添加/编辑接口源"
- 📍 代码位置:
  - 页面: <mcfile name="source/apilist.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/source/apilist.html"></mcfile>
  - 逻辑: <mcfile name="ApiList 控制器（Admin）" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/ApiList.php"></mcfile>
- 🎨 视觉标识: 表格、编辑弹窗、开关
- ⚡ 修改指引:
  - 字段与列头在 apilist.html；保存/测试接口在 Admin\ApiList 控制器

---

### 管理后台 - 资源管理
- 🔤 用户描述方式:
  - 主要: "资源列表"、"资源分类"、"资源日志"、"转存/采集"
- 📍 代码位置:
  - 页面: <mcfile name="source/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/source/index.html"></mcfile> <mcfile name="source/category.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/source/category.html"></mcfile> <mcfile name="source/log.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/source/log.html"></mcfile> <mcfile name="source/feedback.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/source/feedback.html"></mcfile>
  - 逻辑: <mcfile name="Source 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Source.php"></mcfile> <mcfile name="SourceCategory 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/SourceCategory.php"></mcfile> <mcfile name="SourceLog 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/SourceLog.php"></mcfile> <mcfile name="Feedback 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Feedback.php"></mcfile>
- 🎨 视觉标识: 列表 + 筛选 + 分页 + 操作按钮
- ⚡ 修改指引:
  - 列结构与筛选项在各自页面 HTML；接口字段与业务规则在对应控制器

---

### 管理后台 - 权限与分组（RBAC）
- 🔤 用户描述方式:
  - 主要: "管理员列表"、"角色/分组"、"权限节点"
- 📍 代码位置:
  - 页面: <mcfile name="admin/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/admin/index.html"></mcfile> <mcfile name="group/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/group/index.html"></mcfile> <mcfile name="node/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/node/index.html"></mcfile>
  - 逻辑: <mcfile name="Admin 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Admin.php"></mcfile> <mcfile name="Group 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Group.php"></mcfile> <mcfile name="Node 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Node.php"></mcfile>
- 🎨 视觉标识: 表格、树形权限、分配弹窗
- ⚡ 修改指引:
  - 列与表单在页面模板；权限计算与保存逻辑在各控制器

---

### 管理后台 - 操作日志
- 🔤 用户描述方式:
  - 主要: "系统/操作日志"
- 📍 代码位置:
  - 页面: <mcfile name="log/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/log/index.html"></mcfile>
  - 逻辑: <mcfile name="Log 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Log.php"></mcfile>
- 🎨 视觉标识: 列表 + 过滤条件 + 分页
- ⚡ 修改指引:
  - 日志展示字段在页面；查询与存储在 Log 控制器

---

### 管理后台 - IndexNow 推送
- 🔤 用户描述方式:
  - 主要: "IndexNow 推送记录/配置/验证"
- 📍 代码位置:
  - 页面: <mcfile name="indexnow/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/indexnow/index.html"></mcfile>
  - 逻辑: <mcfile name="Indexnow 控制器（Admin）" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/Indexnow.php"></mcfile>；公有验证接口 <mcfile name="Indexnow 控制器（API）" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/api/controller/Indexnow.php"></mcfile>
  - 路由: <mcfile name=":key.txt 验证路由" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile>
- 🎨 视觉标识: 列表 + 触发按钮
- ⚡ 修改指引:
  - 数据列与触发按钮在页面；推送逻辑在 Indexnow 控制器

---

### 管理后台 - 系统清理
- 🔤 用户描述方式:
  - 主要: "清理缓存/日志"、"系统清理"
- 📍 代码位置:
  - 页面: <mcfile name="system/clean.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/system/clean.html"></mcfile>
  - 逻辑: Admin 后台 <mcfile name="System 控制器" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/admin/controller/System.php"></mcfile>；API 清理 <mcfile name="api.System/clean" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/api/controller/System.php"></mcfile> <mcfile name="Route 定义" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile>
- 🎨 视觉标识: 操作按钮 + 执行状态
- ⚡ 修改指引:
  - 清理范围在控制器中实现；前端仅触发

---

### 网站前台 - 首页
- 🔤 用户描述方式:
  - 主要: "前台首页"、"最新/推荐内容展示"
- 📍 代码位置:
  - 页面: <mcfile name="index/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/index/index.html"></mcfile>
  - 详情页: <mcfile name="index/show.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/index/show.html"></mcfile>
  - 控制器: <mcfile name="Index 控制器（前台）" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/index/controller/Index.php"></mcfile>
- 🎨 视觉标识: 列表卡片/资讯区块
- ⚡ 修改指引:
  - 视图结构在模板 HTML；数据来源与分发在前台 Index 控制器

---

### 网站前台 - 新闻列表/详情
- 🔤 用户描述方式:
  - 主要: "新闻列表"、"新闻详情页"
- 📍 代码位置:
  - 页面: <mcfile name="news/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/news/index.html"></mcfile> <mcfile name="news/list.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/news/list.html"></mcfile> <mcfile name="news/detail.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/news/detail.html"></mcfile>
  - 公共头尾与静态资源: <mcfile name="news/common" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/news/common/"></mcfile>
  - 样式: <mcfile name="news/common/static/css" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/index/news/common/static/css"></mcfile>
- 🎨 视觉标识: 列表卡片、分页、详情正文
- ⚡ 修改指引:
  - 布局与样式在各模板与 common/static/css；如需数据调整在前台控制器

---

### 小程序 - 搜索页
- 🔤 用户描述方式:
  - 主要: "小程序搜索"、"本地搜/全网搜切换"、"网盘/分类筛选"、"流式搜索"
  - 别名: "全网搜索"、"SSE 流式"
- 📍 代码位置:
  - 页面: <mcfile name="search.wxml" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp/pages/search/search.wxml"></mcfile> <mcfile name="search.js" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp/pages/search/search.js"></mcfile> <mcfile name="search.wxss" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp/pages/search/search.wxss"></mcfile>
  - API/工具: <mcfile name="miniapp/utils/api.js" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp/utils/api.js"></mcfile>
  - 后端路由（全网搜索 SSE）: <mcfile name="Route: GET /api/search/allWeb" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile> → <mcfile name="api/controller/Search.php::allWeb" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/api/controller/Search.php"></mcfile>
- 🎨 视觉标识: 顶部搜索框，类型切换（本地搜/全网搜），网盘与分类筛选条，结果计数与瀑布流列表，底部加载更多
- ⚡ 修改指引:
  - 修改 UI: 编辑 search.wxml + search.wxss
  - 修改行为: 编辑 search.js（关键词处理、类型切换、分页、SSE 消费）
  - 修改后端: 修改 api/controller/Search.php 的 allWeb 实现；路由在 route/app.php

---

### 小程序 - 首页
- 🔤 用户描述方式:
  - 主要: "小程序首页"、"最新资源列表"、"热门搜索"
- 📍 代码位置:
  - 页面: <mcfile name="index.wxml" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp/pages/index/index.wxml"></mcfile> <mcfile name="index.js" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp/pages/index/index.js"></mcfile>
- 🎨 视觉标识: 顶部搜索区块 + 热搜词；下方最新资源列表，加载/空状态
- ⚡ 修改指引:
  - 列表项样式与布局在 WXML/WXSS；数据加载逻辑在 index.js

---

### 小程序 - 分类页
- 🔤 用户描述方式:
  - 主要: "资源分类浏览"、"按分类筛选"
- 📍 代码位置:
  - 页面: <mcfile name="category.*" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp/pages/category/"></mcfile>
- 🎨 视觉标识: 分类网格/列表，点击进入分类详情或搜索
- ⚡ 修改指引:
  - 分类数据来源与 UI 在 category.js / category.wxml 中

---

### 小程序 - 详情页
- 🔤 用户描述方式:
  - 主要: "资源详情"、"复制链接/转存"
- 📍 代码位置:
  - 页面: <mcfile name="detail.*" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp/pages/detail/"></mcfile>
- 🎨 视觉标识: 标题、描述、网盘类型图标、操作按钮
- ⚡ 修改指引:
  - UI 在 detail.wxml/wxss；行为在 detail.js；如涉及转存，后端 API 位于 <mcfile name="api/controller/Open.php::transfer" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/api/controller/Open.php"></mcfile>（路由见 <mcfile name="POST /api/transfer" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile>）

---

### 小程序 - 个人中心（关于/反馈/历史/设置）
- 🔤 用户描述方式:
  - 主要: "我的"、"关于我们"、"意见反馈"、"浏览历史"、"设置"
- 📍 代码位置:
  - 页面: <mcfile name="profile/" path="/Users/<USER>/Codes/panseeker/xinyue-search/miniapp/pages/profile/"></mcfile>
- 🎨 视觉标识: 多个子页，表单/列表/信息展示
- ⚡ 修改指引:
  - 各子页 UI/逻辑分别在对应的 about/feedback/history/settings 目录

---

### 管理后台 - 首页与导航
- 🔤 用户描述方式:
  - 主要: "后台首页仪表盘"、"左侧菜单"
- 📍 代码位置:
  - 页面: <mcfile name="qfadmin/index/index.html" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/index/index.html"></mcfile>
  - 公共头尾与菜单: <mcfile name="qfadmin/common/*" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/views/qfadmin/common/"></mcfile>
- 🎨 视觉标识: 顶部 Header、左侧菜单、右侧内容区
- ⚡ 修改指引:
  - 菜单项与路由跳转在 common/menu.html；样式在 YAdmin.css/element.css

---

### 管理后台 - 搜索与统计（可选）
- 🔤 用户描述方式:
  - 主要: "后台内数据统计/图表展示"
- 📍 代码位置:
  - 静态库: <mcfile name="echarts.min.js" path="/Users/<USER>/Codes/panseeker/xinyue-search/public/static/admin/js/echarts.min.js"></mcfile>
  - 相关页面: 多处管理页（如资源/日志）可引入图表
- ⚡ 修改指引:
  - 在对应页面初始化 ECharts 实例，容器样式需设置宽高

---

## 🚀 使用说明

- 对于用户:
  - 示例: "我想把后台登录按钮改成蓝色" → 定位到 管理后台-登录 的主文件，修改按钮样式（element 类或内联样式）
  - 示例: "新增一个配置分类" → 定位到 管理后台-配置列表 的模板标签项，增加分类项，并在后端 Conf 控制器处理分类
  - 示例: "小程序全网搜索的流式太慢" → 定位到 小程序-搜索页 的 search.js 与后端 Search.php::allWeb 优化

- 对于 AI:
  1. 在本映射表中搜索相关"用户描述方式"
  2. 打开对应的"代码位置"文件
  3. 根据"修改指引"直接调整 UI 或逻辑，并更新后端接口实现

---

附：关键后端 API 路由总览（选摘）
- <mcfile name="POST /api/search" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile> → <mcfile name="api/Plugin/search" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/api/controller/Plugin.php"></mcfile>
- <mcfile name="GET /api/search/allWeb" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile> → <mcfile name="api/Search/allWeb" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/api/controller/Search.php"></mcfile>
- <mcfile name="POST /api/transfer" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile> → <mcfile name="api/Open/transfer" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/api/controller/Open.php"></mcfile>
- <mcfile name="POST /api/system/clean" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile> → <mcfile name="api/System/clean" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/api/controller/System.php"></mcfile>
- <mcfile name=":key.txt IndexNow 验证" path="/Users/<USER>/Codes/panseeker/xinyue-search/route/app.php"></mcfile> → <mcfile name="api/Indexnow/verifyFile" path="/Users/<USER>/Codes/panseeker/xinyue-search/app/api/controller/Indexnow.php"></mcfile>