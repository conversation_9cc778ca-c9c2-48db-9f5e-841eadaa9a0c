openapi: 3.0.1
info:
  title: PanSeeker盘搜插件API
  description: PanSeeker盘搜插件，用于搜索百度网盘资源
  version: 1.0.0
servers:
  - url: https://www.panseeker.com
paths:
  /baidu/search_plugin.php:
    post:
      operationId: searchResources
      summary: 搜索百度网盘资源
      description: 根据关键词搜索百度网盘资源
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: string
                  description: 搜索关键词
              required:
                - query
      responses:
        '200':
          description: 成功返回搜索结果
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      code:
                        type: integer
                        description: 状态码
                      message:
                        type: string
                        description: 响应消息
                      data:
                        type: object
                        properties:
                          resources:
                            type: array
                            items:
                              type: object
                              properties:
                                id:
                                  type: string
                                  description: 资源ID
                                  x-visible: false
                                title:
                                  type: string
                                  description: 资源标题
                                link:
                                  type: string
                                  description: 资源链接
                                type:
                                  type: string
                                  description: 网盘类型
                                  x-visible: false
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      code:
                        type: integer
                        description: 状态码
                      message:
                        type: string
                        description: 错误信息