<?php
// 百度文心智能体插件 - 百度网盘搜索插件（返回2条转存链接版本，保持API兼容性）
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取POST请求数据
$input = json_decode(file_get_contents('php://input'), true);
$query = $input['query'] ?? '';

// 验证参数
if (empty($query)) {
    echo json_encode([
        'result' => [
            'code' => 400,
            'message' => '搜索关键词不能为空'
        ]
    ]);
    exit;
}

// 设置执行时间限制
set_time_limit(30);

try {
    // 调用Panseeker搜索API（只搜索百度网盘，限定2个结果）
    $apiUrl = "https://www.panseeker.com/api/search/allWeb";
    $params = [
        'title' => $query,
        'is_type' => 2,  // 百度网盘类型
        'page_no' => 1,
        'page_size' => 2
    ];

    // 发起API请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl . '?' . http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);  // 设置15秒超时
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);  // 设置10秒连接超时
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'BaiduYiyanPlugin/1.0');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // 如果请求失败，返回空结果
    if ($httpCode !== 200) {
        echo json_encode([
            'result' => [
                'code' => 200,
                'message' => '搜索完成，未找到相关资源',
                'data' => [
                    'resources' => []
                ]
            ]
        ]);
        exit;
    }

    // 解析响应数据
    $searchResult = json_decode($response, true);

    // 如果API响应格式错误或返回错误，返回空结果
    if (!isset($searchResult['code']) || $searchResult['code'] !== 200) {
        echo json_encode([
            'result' => [
                'code' => 200,
                'message' => '搜索完成，未找到相关资源',
                'data' => [
                    'resources' => []
                ]
            ]
        ]);
        exit;
    }

    // 处理找到的资源并返回转存链接
    $resources = [];
    if (isset($searchResult['data']['items']) && is_array($searchResult['data']['items'])) {
        // 限制最多处理2个结果
        foreach (array_slice($searchResult['data']['items'], 0, 2) as $item) {
            // 只处理百度网盘资源
            if (isset($item['is_type']) && $item['is_type'] == 2 && isset($item['url']) && isset($item['title'])) {
                // 处理链接以获取转存后的分享链接
                $processedResult = convertLink($item['url'], $item['title']);
                
                $resources[] = [
                    'id' => $item['id'] ?? uniqid(),
                    'title' => $processedResult['title'] ?? $item['title'],
                    'link' => $processedResult['url'] ?? $item['url'],  // 使用转存后的链接
                    'type' => 'baidu'
                ];
            }
        }
    }

    // 返回结果（保持API兼容性）
    echo json_encode([
        'result' => [
            'code' => 200,
            'message' => '搜索完成，为您找到 ' . count($resources) . ' 个资源',
            'data' => [
                'resources' => $resources
            ]
        ]
    ]);

} catch (Exception $e) {
    // 发生任何异常都返回空结果，避免插件调用失败
    echo json_encode([
        'result' => [
            'code' => 200,
            'message' => '搜索完成，未找到相关资源',
            'data' => [
                'resources' => []
            ]
        ]
    ]);
}

/**
 * 转存链接并返回新链接
 * @param string $url 原始链接
 * @param string $title 资源标题
 * @return array 处理结果
 */
function convertLink($url, $title) {
    // 调用您网站的链接转存API
    $convertApiUrl = "https://www.panseeker.com/api/other/convert_allweb_url";
    
    // 准备POST数据
    $postData = [
        'url' => $url,
        'title' => $title
    ];
    
    // 发起转存请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $convertApiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);  // 设置20秒超时
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);  // 设置15秒连接超时
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'User-Agent: PanSeeker Plugin/1.0'
    ]);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 跟随重定向
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 临时跳过SSL验证（仅用于测试）
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    // 检查转存是否成功
    if ($httpCode === 200) {
        $convertResult = json_decode($response, true);
        if (isset($convertResult['code']) && $convertResult['code'] == 200 && isset($convertResult['data']['url'])) {
            // 转存成功，返回新链接
            return [
                'url' => $convertResult['data']['url'],
                'title' => $convertResult['data']['title'] ?? $title
            ];
        }
    }
    
    // 转存失败，返回原始链接
    return [
        'url' => $url,
        'title' => $title
    ];
}
?>